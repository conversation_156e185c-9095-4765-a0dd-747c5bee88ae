#!/usr/bin/env python3
"""
Download prebuilt wheels for Windows

This script downloads prebuilt wheels for PyQt5 and its dependencies
to avoid the need for Microsoft Visual C++ build tools.
"""

import os
import sys
import platform
import urllib.request
import ssl
import certifi

# Directory to store wheels
WHEELS_DIR = "wheels"

# URLs for prebuilt wheels - these match Python 3.10 on Windows 64-bit
# Adjust URLs if you're using a different Python version
WHEEL_URLS = {
    "PyQt5": "https://download.lfd.uci.edu/pythonlibs/archived/PyQt5-5.15.9-cp310-cp310-win_amd64.whl",
    "PyQt5_sip": "https://download.lfd.uci.edu/pythonlibs/archived/PyQt5_sip-12.12.2-cp310-cp310-win_amd64.whl",
    "PyQt5_Qt5": "https://download.lfd.uci.edu/pythonlibs/archived/PyQt5_Qt5-5.15.2-py3-none-win_amd64.whl"
}

def download_file(url, target_dir):
    """Download a file from URL to target directory."""
    os.makedirs(target_dir, exist_ok=True)
    filename = os.path.basename(url)
    target_path = os.path.join(target_dir, filename)

    if os.path.exists(target_path):
        print(f"File {filename} already exists. Skipping download.")
        return

    print(f"Downloading {filename}...")

    try:
        # Create a SSL context that uses the certifi CA bundle
        context = ssl.create_default_context(cafile=certifi.where())

        # Download the file
        with urllib.request.urlopen(url, context=context) as response:
            with open(target_path, 'wb') as out_file:
                out_file.write(response.read())

        print(f"Downloaded {filename} successfully!")
    except Exception as e:
        print(f"Error downloading {filename}: {str(e)}")
        if os.path.exists(target_path):
            os.remove(target_path)

def main():
    """Main function."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    wheels_dir = os.path.join(script_dir, WHEELS_DIR)

    print(f"Downloading wheels to: {wheels_dir}")

    # Only download for Windows
    if platform.system() != "Windows":
        print("This script is only for Windows. Skipping downloads.")
        return

    # Download all wheels
    for name, url in WHEEL_URLS.items():
        download_file(url, wheels_dir)

    print("\nDownload complete!")
    print("You can now use launch.py or KnowledgeApp.bat to start the application.")

if __name__ == "__main__":
    main()
