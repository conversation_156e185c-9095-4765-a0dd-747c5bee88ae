#!/usr/bin/env python3
"""
Test script to verify lifecycle management fixes:
1. Service registration (ShutdownManager in DI container)
2. Memory manager attribute error fix
3. Memory usage improvements
4. Shutdown signal handling
"""

import sys
import os
import logging
import time

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_service_registration():
    """Test that ShutdownManager is properly registered in DI container"""
    print("🔧 Testing Service Registration")
    print("=" * 50)
    
    try:
        from knowledge_app.core.enterprise_di_container import configure_services
        from knowledge_app.utils.shutdown_manager import ShutdownManager
        
        # Configure DI container
        container = configure_services()
        print("✅ DI container configured")
        
        # Test ShutdownManager registration
        shutdown_manager = container.resolve(ShutdownManager)
        print(f"✅ ShutdownManager resolved: {type(shutdown_manager).__name__}")
        
        # Test that it's the same instance (singleton)
        shutdown_manager2 = container.resolve(ShutdownManager)
        is_singleton = shutdown_manager is shutdown_manager2
        print(f"✅ Singleton behavior: {is_singleton}")
        
        return True
        
    except Exception as e:
        print(f"❌ Service registration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_manager_methods():
    """Test that MemoryManager has all required methods"""
    print("\n🔧 Testing Memory Manager Methods")
    print("=" * 50)
    
    try:
        from knowledge_app.core.memory_manager import MemoryManager
        
        # Create memory manager with test config
        config = {
            'memory_cache_path': 'test_cache',
            'memory_threshold': 0.6,
            'gpu_memory_threshold': 0.85
        }
        
        memory_manager = MemoryManager(config)
        print("✅ MemoryManager created")
        
        # Test that clear_expired_cache method exists
        if hasattr(memory_manager, 'clear_expired_cache'):
            print("✅ clear_expired_cache method exists")
            
            # Test calling the method
            memory_manager.clear_expired_cache()
            print("✅ clear_expired_cache method works")
        else:
            print("❌ clear_expired_cache method missing")
            return False
        
        # Test that _cleanup_expired method exists
        if hasattr(memory_manager, '_cleanup_expired'):
            print("✅ _cleanup_expired method exists")
        else:
            print("❌ _cleanup_expired method missing")
            return False
        
        # Test memory thresholds
        print(f"✅ Memory threshold: {memory_manager.memory_threshold}")
        print(f"✅ GPU memory threshold: {memory_manager.gpu_memory_threshold}")
        
        # Cleanup
        memory_manager.cleanup()
        print("✅ Memory manager cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Memory manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_shutdown_manager():
    """Test shutdown manager functionality"""
    print("\n🔧 Testing Shutdown Manager")
    print("=" * 50)
    
    try:
        from knowledge_app.utils.shutdown_manager import get_shutdown_manager
        
        shutdown_manager = get_shutdown_manager()
        print("✅ Shutdown manager obtained")
        
        # Test handler registration
        test_handler_called = False
        
        def test_handler():
            nonlocal test_handler_called
            test_handler_called = True
            print("✅ Test shutdown handler called")
        
        shutdown_manager.register_handler(
            'test_handler',
            test_handler,
            category='test',
            priority=50
        )
        print("✅ Shutdown handler registered")
        
        # Test memory thresholds
        print(f"✅ Memory threshold: {shutdown_manager._memory_threshold}")
        print(f"✅ GPU memory threshold: {shutdown_manager._gpu_memory_threshold}")
        
        # Test that shutdown manager is not currently shutting down
        print(f"✅ Is shutting down: {shutdown_manager.is_shutting_down}")
        
        return True
        
    except Exception as e:
        print(f"❌ Shutdown manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_application_bootstrapper():
    """Test application bootstrapper DI integration"""
    print("\n🔧 Testing Application Bootstrapper")
    print("=" * 50)
    
    try:
        from knowledge_app.core.application_bootstrapper import ApplicationBootstrapper
        
        # Create bootstrapper (but don't run full bootstrap)
        bootstrapper = ApplicationBootstrapper()
        print("✅ Application bootstrapper created")
        
        # Test DI container setup
        bootstrapper._setup_dependency_injection()
        print("✅ DI container setup completed")
        
        # Test that container has ShutdownManager
        from knowledge_app.utils.shutdown_manager import ShutdownManager
        shutdown_manager = bootstrapper.container.resolve(ShutdownManager)
        print(f"✅ ShutdownManager resolved from bootstrapper: {type(shutdown_manager).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Application bootstrapper test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all lifecycle fix tests"""
    print("🚀 Testing Lifecycle Management Fixes")
    print("=" * 60)
    
    tests = [
        ("Service Registration", test_service_registration),
        ("Memory Manager Methods", test_memory_manager_methods),
        ("Shutdown Manager", test_shutdown_manager),
        ("Application Bootstrapper", test_application_bootstrapper),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 All lifecycle management fixes are working correctly!")
        return 0
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
