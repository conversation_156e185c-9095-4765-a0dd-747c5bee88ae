name: autofix.ci

on:
  pull_request:
  push:
    branches: [ "main" ]

permissions:
  contents: read

jobs:
  autofix:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      # Install ruff via uv (or use pip if you want)
      - uses: astral-sh/setup-uv@f0ec1fc3b38f5e7cd731bb6ce540c5af426746bb
        with:
          version: "0.4.20"
      
      # Run autofixers
      - run: uv run ruff check --fix-only .
      - run: uv run ruff format .

      # Trigger autofix commit if fixes made
      - uses: autofix-ci/action@551dded8c6cc8a1054039c8bc0b8b48c51dfc6ef
