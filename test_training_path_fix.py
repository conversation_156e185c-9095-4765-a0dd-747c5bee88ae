#!/usr/bin/env python3
"""
Test Training Path Fix

This script tests the training data path resolution fix to ensure
that the training system can find the training data file correctly.
"""

import sys
import os
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.knowledge_app.core.real_7b_config import Real7BConfigManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_training_path_resolution():
    """Test that training data path resolution works correctly"""
    
    print("🧪 Testing Training Data Path Resolution")
    print("=" * 50)
    
    try:
        # Initialize config manager
        config_manager = Real7BConfigManager()
        
        # Get the training data path from config
        training_data_path = config_manager.config["data"]["train_data_path"]
        print(f"📁 Config path: {training_data_path}")
        
        # Check if it's relative or absolute
        is_absolute = os.path.isabs(training_data_path)
        print(f"🔍 Is absolute: {is_absolute}")
        
        # Convert to absolute path (this is what the fix does)
        if not is_absolute:
            absolute_path = os.path.abspath(training_data_path)
            print(f"📍 Absolute path: {absolute_path}")
        else:
            absolute_path = training_data_path
            print(f"📍 Already absolute: {absolute_path}")
        
        # Check if the file exists
        file_exists = os.path.exists(absolute_path)
        print(f"✅ File exists: {file_exists}")
        
        if file_exists:
            # Get file size
            file_size = os.path.getsize(absolute_path) / 1024 / 1024  # MB
            print(f"📊 File size: {file_size:.1f} MB")
            
            # Get file info
            stat = os.stat(absolute_path)
            print(f"📅 Last modified: {stat.st_mtime}")
            
            print("\n✅ SUCCESS: Training data file found and accessible!")
            return True
        else:
            print(f"\n❌ ERROR: Training data file not found at {absolute_path}")
            
            # Try to help debug
            dir_path = os.path.dirname(absolute_path)
            print(f"🔍 Checking directory: {dir_path}")
            
            if os.path.exists(dir_path):
                files = os.listdir(dir_path)
                print(f"📂 Files in directory: {files}")
            else:
                print(f"❌ Directory does not exist: {dir_path}")
            
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_working_directory_independence():
    """Test that path resolution works from different working directories"""
    
    print("\n🧪 Testing Working Directory Independence")
    print("=" * 50)
    
    original_cwd = os.getcwd()
    print(f"📁 Original working directory: {original_cwd}")
    
    try:
        # Test from different directories
        test_dirs = [
            original_cwd,
            os.path.dirname(original_cwd) if os.path.dirname(original_cwd) != original_cwd else "C:\\",
            "C:\\" if os.name == 'nt' else "/"
        ]
        
        for test_dir in test_dirs:
            if os.path.exists(test_dir):
                print(f"\n🔄 Testing from directory: {test_dir}")
                os.chdir(test_dir)
                
                # Get config path
                config_manager = Real7BConfigManager()
                training_data_path = config_manager.config["data"]["train_data_path"]
                
                # Convert to absolute
                absolute_path = os.path.abspath(training_data_path)
                file_exists = os.path.exists(absolute_path)
                
                print(f"  📁 Current working dir: {os.getcwd()}")
                print(f"  📍 Absolute path: {absolute_path}")
                print(f"  ✅ File exists: {file_exists}")
                
                if not file_exists:
                    print(f"  ❌ FAILED: File not found from {test_dir}")
                    return False
        
        print("\n✅ SUCCESS: Path resolution works from all directories!")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False
    finally:
        # Restore original working directory
        os.chdir(original_cwd)

def test_trainer_validation():
    """Test the trainer validation logic"""
    
    print("\n🧪 Testing Trainer Validation Logic")
    print("=" * 50)
    
    try:
        from src.knowledge_app.core.real_7b_trainer import Real7BTrainer
        from src.knowledge_app.core.real_7b_config import Real7BConfig
        
        # Create a config
        config = Real7BConfig()
        
        # Get the training data path and make it absolute
        config_manager = Real7BConfigManager()
        training_data_path = config_manager.config["data"]["train_data_path"]
        
        if not os.path.isabs(training_data_path):
            training_data_path = os.path.abspath(training_data_path)
        
        print(f"📍 Using training data path: {training_data_path}")
        
        # Create trainer (but don't start it)
        trainer = Real7BTrainer(config, training_data_path)
        
        print(f"🔍 Trainer training_data_path: {trainer.training_data_path}")
        print(f"✅ Trainer created successfully!")
        
        # Test the validation method directly
        try:
            trainer._validate_training_environment()
            print("✅ Training environment validation passed!")
            return True
        except Exception as validation_error:
            print(f"❌ Training environment validation failed: {validation_error}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR creating trainer: {e}")
        return False

def main():
    """Run all tests"""
    
    print("🚀 Training Path Fix Test Suite")
    print("=" * 60)
    
    tests = [
        ("Path Resolution", test_training_path_resolution),
        ("Working Directory Independence", test_working_directory_independence),
        ("Trainer Validation", test_trainer_validation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Training path fix is working correctly!")
        print("\n💡 You should now be able to click 'Mistral 7B QLora' without getting 'Training cancelled'")
    else:
        print("❌ Some tests failed. The training path fix may need more work.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
