{"timestamp": "2025-06-11T01:48:19.164162", "error_type": "simulation_detected", "message": "Training is running in simulation mode instead of real 7B model training", "stack_trace": "Training validation likely failed", "screenshot_path": "automation_screenshots\\20250611_014819_error_simulation_detected.png", "ui_state": {"automation_state": "error_detected", "main_window_visible": true, "training_dialog_visible": true, "active_widgets": [{"type": "QFrame", "text": "", "enabled": true}, {"type": "QLabel", "text": "KNOWLEDGE QUIZ APP", "enabled": true}, {"type": "QPushButton", "text": "Pause Updates", "enabled": true}, {"type": "QProgressBar", "text": "", "enabled": true}, {"type": "QLabel", "text": "📈", "enabled": true}, {"type": "QLabel", "text": "Batch: 0/0", "enabled": true}, {"type": "FIREProgressWidget", "text": "", "enabled": true}, {"type": "QProgressBar", "text": "", "enabled": true}, {"type": "QLabel", "text": "Confidence Range: Calculating...", "enabled": true}, {"type": "QLabel", "text": "Loss History: (Install pyqtgraph for charts)", "enabled": true}, {"type": "QLabel", "text": "⏳", "enabled": true}, {"type": "QLabel", "text": "🔥 FIRE Training Progress Monitor", "enabled": true}, {"type": "QStackedWidget", "text": "", "enabled": true}, {"type": "QStatusBar", "text": "", "enabled": true}, {"type": "QComboBox", "text": "", "enabled": true}, {"type": "ModernSplashScreen", "text": "", "enabled": true}, {"type": "QLabel", "text": "📊", "enabled": true}, {"type": "QGroupBox", "text": "", "enabled": true}, {"type": "QGroupBox", "text": "", "enabled": true}, {"type": "MainMenu", "text": "", "enabled": true}, {"type": "QPushButton", "text": "Start Quiz", "enabled": true}, {"type": "QStackedWidget", "text": "", "enabled": true}, {"type": "QTabWidget", "text": "", "enabled": true}, {"type": "QLabel", "text": "Confidence Level:", "enabled": true}, {"type": "QLabel", "text": "Predicted Final: 79.8% (95% confidence)", "enabled": true}, {"type": "QLabel", "text": "Accuracy: 0.0%", "enabled": true}, {"type": "QLabel", "text": "Knowledge App", "enabled": true}, {"type": "QLabel", "text": "📊", "enabled": true}, {"type": "QLabel", "text": "Remaining: 0.0 hours (95% confidence)", "enabled": true}, {"type": "QLabel", "text": "⏰", "enabled": true}, {"type": "QPushButton", "text": "Cancel Training", "enabled": true}, {"type": "QPushButton", "text": "📁 Manage Checkpoints", "enabled": true}, {"type": "AITrainingDialog", "text": "", "enabled": true}, {"type": "QPushButton", "text": "❌ Cancel Training", "enabled": true}, {"type": "QGroupBox", "text": "", "enabled": true}, {"type": "QSizeGrip", "text": "", "enabled": true}, {"type": "QLabel", "text": "Epoch: 0/0", "enabled": true}, {"type": "QPushButton", "text": "✅ Close", "enabled": true}, {"type": "QLabel", "text": "Total Estimated: Calculating...", "enabled": true}, {"type": "QLabel", "text": "Current: 0.0%", "enabled": true}, {"type": "QPushButton", "text": "Exit", "enabled": true}, {"type": "MainWindow", "text": "", "enabled": true}, {"type": "QLabel", "text": "Background Images Storage: 0 MB used", "enabled": true}, {"type": "QGroupBox", "text": "", "enabled": true}, {"type": "QLabel", "text": "Accuracy History: (Install pyqtgraph for charts)", "enabled": true}, {"type": "QLabel", "text": "Learning Rate: 0.000", "enabled": true}, {"type": "QGroupBox", "text": "", "enabled": true}, {"type": "QLabel", "text": "Welcome! Ready to start learning?", "enabled": true}, {"type": "QLabel", "text": "Initializing...", "enabled": true}, {"type": "QWidget", "text": "", "enabled": true}, {"type": "QWidget", "text": "", "enabled": true}, {"type": "QTabBar", "text": "", "enabled": true}, {"type": "QPushButton", "text": "Train AI Model (85% Target)", "enabled": true}, {"type": "QPushButton", "text": "Settings", "enabled": true}, {"type": "QLabel", "text": "Loss: 0.000", "enabled": true}, {"type": "QLabel", "text": "Status: Training Active 🔥", "enabled": true}, {"type": "QLabel", "text": "Elapsed: 00:00:00", "enabled": true}, {"type": "QProgressBar", "text": "0%", "enabled": true}, {"type": "QWidget", "text": "", "enabled": true}, {"type": "QLabel", "text": "🔮", "enabled": true}, {"type": "QLabel", "text": "Version 1.0.0", "enabled": true}], "visible_buttons": [{"type": "QPushButton", "text": "Pause Updates", "enabled": true}, {"type": "QPushButton", "text": "Start Quiz", "enabled": true}, {"type": "QPushButton", "text": "Cancel Training", "enabled": true}, {"type": "QPushButton", "text": "📁 Manage Checkpoints", "enabled": true}, {"type": "QPushButton", "text": "❌ Cancel Training", "enabled": true}, {"type": "QPushButton", "text": "✅ Close", "enabled": true}, {"type": "QPushButton", "text": "Exit", "enabled": true}, {"type": "QPushButton", "text": "Train AI Model (85% Target)", "enabled": true}, {"type": "QPushButton", "text": "Settings", "enabled": true}]}, "suggested_fix": "Check training data path, GPU availability, and configuration validation", "automation_state": "error_detected", "retry_count": 1}