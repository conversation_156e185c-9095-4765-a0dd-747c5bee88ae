#!/usr/bin/env python3
"""
Comprehensive Integration Tests
Tests real-world scenarios and catches runtime errors that unit tests miss
"""
import pytest
import sys
import os
import warnings
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# Suppress warnings for cleaner test output
warnings.filterwarnings("ignore")

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestUIIntegration:
    """Test UI component integration and dialog creation"""
    
    def test_training_dialog_creation(self):
        """Test that training dialog can be created without errors"""
        try:
            # Mock QApplication to avoid GUI requirements
            with patch('PyQt5.QtWidgets.QApplication.instance', return_value=Mock()):
                with patch('PyQt5.QtWidgets.QDialog.__init__', return_value=None):
                    from knowledge_app.ui.training_dialog import AITrainingDialog
                    
                    # Test dialog creation
                    dialog = AITrainingDialog.__new__(AITrainingDialog)
                    
                    # Test that all required methods exist
                    required_methods = [
                        'show_checkpoints', 'resume_from_checkpoint',
                        'show_info', 'show_error', 'select_real_7b_training',
                        'cancel_training'
                    ]
                    
                    for method in required_methods:
                        assert hasattr(AITrainingDialog, method), f"Missing method: {method}"
                        
        except Exception as e:
            pytest.fail(f"Training dialog creation failed: {e}")
    
    def test_checkpoint_dialog_integration(self):
        """Test checkpoint dialog integration"""
        try:
            with patch('PyQt5.QtWidgets.QApplication.instance', return_value=Mock()):
                with patch('PyQt5.QtWidgets.QDialog.__init__', return_value=None):
                    from knowledge_app.ui.checkpoint_dialog import CheckpointDialog
                    
                    # Test dialog creation with mock directory
                    dialog = CheckpointDialog.__new__(CheckpointDialog)
                    
                    # Test required attributes
                    required_attrs = [
                        'checkpoint_selected', 'setup_ui', 'load_checkpoints',
                        'resume_from_checkpoint', 'delete_checkpoint'
                    ]
                    
                    for attr in required_attrs:
                        assert hasattr(CheckpointDialog, attr), f"Missing attribute: {attr}"
                        
        except Exception as e:
            pytest.fail(f"Checkpoint dialog integration failed: {e}")

class TestTrainingIntegration:
    """Test training-related integration issues"""
    
    def test_training_arguments_compatibility(self):
        """Test TrainingArguments compatibility with current transformers version"""
        try:
            from transformers import TrainingArguments
            
            # Test the exact arguments that are causing issues
            test_args = {
                'output_dir': './test_output',
                'num_train_epochs': 1,
                'per_device_train_batch_size': 1,
                'learning_rate': 5e-5,
                'logging_steps': 10,
                'save_steps': 100,
                'eval_steps': 100,
                'warmup_steps': 10,
                'weight_decay': 0.01,
                'logging_dir': './test_logs',
                'report_to': 'none',
                'remove_unused_columns': False,
                'dataloader_pin_memory': False,
            }
            
            # Test deprecated vs new argument names
            try:
                # Try new argument name first
                test_args['eval_strategy'] = 'steps'
                training_args = TrainingArguments(**test_args)
                print("✅ Using 'eval_strategy' (new)")
            except TypeError as e:
                if 'eval_strategy' in str(e):
                    # Fall back to old argument name
                    test_args.pop('eval_strategy', None)
                    test_args['evaluation_strategy'] = 'steps'
                    training_args = TrainingArguments(**test_args)
                    print("✅ Using 'evaluation_strategy' (deprecated)")
                else:
                    raise e
                    
            assert training_args is not None
            
        except Exception as e:
            pytest.fail(f"TrainingArguments compatibility test failed: {e}")
    
    def test_real_7b_trainer_initialization(self):
        """Test Real7BTrainer can be initialized without errors"""
        try:
            from knowledge_app.core.real_7b_trainer import Real7BTrainer
            from knowledge_app.core.real_7b_config import get_real_7b_config
            
            # Get config
            config_manager = get_real_7b_config()
            config = config_manager.get_optimized_config_for_hardware()
            
            # Test trainer initialization with mock
            with patch('torch.cuda.is_available', return_value=True):
                with patch('transformers.AutoTokenizer.from_pretrained'):
                    with patch('transformers.AutoModelForCausalLM.from_pretrained'):
                        # Provide required training_data_path argument
                        trainer = Real7BTrainer(config, training_data_path="test_data.txt")
                        assert trainer is not None
                        assert hasattr(trainer, 'run')
                        assert hasattr(trainer, 'stop')
                        
        except Exception as e:
            pytest.fail(f"Real7BTrainer initialization failed: {e}")

class TestModelCompatibility:
    """Test model loading and compatibility issues"""
    
    def test_model_manager_initialization(self):
        """Test ModelManager can initialize without errors"""
        try:
            from knowledge_app.core.model_manager import ModelManager
            
            # Test with mock config
            mock_config = {
                'models_path': 'data/models',
                'cache_dir': 'data/cache',
                'base_path': 'data',
                'use_gpu': True,
                'gpu_memory_fraction': 0.8
            }
            
            with patch('torch.cuda.is_available', return_value=True):
                manager = ModelManager(mock_config)
                assert manager is not None
                assert hasattr(manager, 'load_model')
                assert hasattr(manager, 'list_available_models')
                
        except Exception as e:
            pytest.fail(f"ModelManager initialization failed: {e}")
    
    def test_transformers_version_compatibility(self):
        """Test transformers library version compatibility"""
        try:
            import transformers
            from packaging import version
            
            # Check transformers version
            transformers_version = version.parse(transformers.__version__)
            min_version = version.parse("4.20.0")
            
            assert transformers_version >= min_version, f"Transformers version {transformers.__version__} is too old"
            
            # Test key imports
            from transformers import (
                AutoTokenizer, AutoModelForCausalLM, 
                TrainingArguments, Trainer
            )
            
            print(f"✅ Transformers version: {transformers.__version__}")
            
        except Exception as e:
            pytest.fail(f"Transformers compatibility test failed: {e}")

class TestConfigurationIntegration:
    """Test configuration loading and validation"""
    
    def test_real_7b_config_loading(self):
        """Test Real 7B configuration loading"""
        try:
            from knowledge_app.core.real_7b_config import get_real_7b_config
            
            config_manager = get_real_7b_config()
            
            # Test all model configs
            models = ['mistral-7b', 'llama-8b', 'qwen-7b', 'gemma-7b']

            for model_name in models:
                try:
                    config = config_manager.get_model_config(model_name)
                    assert config is not None
                    assert hasattr(config, 'name')
                    assert hasattr(config, 'model_id')
                    print(f"✅ Config loaded for {model_name}")
                except Exception as e:
                    print(f"⚠️ Config issue for {model_name}: {e}")
                    
        except Exception as e:
            pytest.fail(f"Real 7B config loading failed: {e}")
    
    def test_app_config_integration(self):
        """Test AppConfig integration"""
        try:
            from knowledge_app.core.app_config import AppConfig
            
            config = AppConfig()
            
            # Test key settings
            essential_settings = [
                'training.output_dir',
                'training.batch_size',
                'training.learning_rate',
                'models.cache_dir'
            ]
            
            for setting in essential_settings:
                try:
                    value = config.get_setting(setting, 'default')
                    assert value is not None
                    print(f"✅ Setting {setting}: {value}")
                except Exception as e:
                    print(f"⚠️ Setting issue {setting}: {e}")
                    
        except Exception as e:
            pytest.fail(f"AppConfig integration failed: {e}")

class TestDependencyCompatibility:
    """Test dependency compatibility and version issues"""
    
    def test_pytorch_compatibility(self):
        """Test PyTorch compatibility"""
        try:
            import torch
            from packaging import version
            
            # Check PyTorch version
            torch_version = version.parse(torch.__version__.split('+')[0])  # Remove CUDA suffix
            min_version = version.parse("2.0.0")
            
            assert torch_version >= min_version, f"PyTorch version {torch.__version__} is too old"
            
            # Test CUDA availability
            cuda_available = torch.cuda.is_available()
            if cuda_available:
                print(f"✅ PyTorch {torch.__version__} with CUDA")
            else:
                print(f"✅ PyTorch {torch.__version__} (CPU only)")
                
        except Exception as e:
            pytest.fail(f"PyTorch compatibility test failed: {e}")
    
    def test_critical_imports(self):
        """Test all critical imports work"""
        critical_modules = [
            'transformers',
            'torch',
            'numpy',
            'PyQt5.QtWidgets',
            'PyQt5.QtCore',
            'peft',
            'datasets'
        ]
        
        failed_imports = []
        
        for module in critical_modules:
            try:
                __import__(module)
                print(f"✅ {module}")
            except ImportError as e:
                failed_imports.append((module, str(e)))
                print(f"❌ {module}: {e}")
        
        if failed_imports:
            pytest.fail(f"Critical imports failed: {failed_imports}")

def run_comprehensive_tests():
    """Run all comprehensive tests"""
    print("🧪 Running Comprehensive Integration Tests")
    print("=" * 60)
    
    # Run pytest with verbose output
    import subprocess
    result = subprocess.run([
        sys.executable, '-m', 'pytest', 
        __file__, 
        '-v', 
        '--tb=short',
        '--disable-warnings'
    ], capture_output=True, text=True)
    
    print(result.stdout)
    if result.stderr:
        print("STDERR:", result.stderr)
    
    return result.returncode == 0

if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
