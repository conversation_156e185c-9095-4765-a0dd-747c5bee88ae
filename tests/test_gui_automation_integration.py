"""
GUI Automation Integration Tests

This module integrates the GUI automation debugger with your existing test framework.
It provides pytest-compatible tests that can be run with your other tests.
"""

import pytest
import sys
import time
import logging
from pathlib import Path
from unittest.mock import Mock, patch
from PyQt5.QtWidgets import QMessageBox

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tests.gui_automation_debugger import GUIAutomationDebugger, AutomationState, ErrorInfo

logger = logging.getLogger(__name__)

class TestGUIAutomation:
    """Test suite for GUI automation functionality"""
    
    @pytest.fixture
    def automation_debugger(self):
        """Create automation debugger instance"""
        debugger = GUIAutomationDebugger()
        yield debugger
        # Cleanup after test
        debugger._cleanup()
        
    def test_automation_initialization(self, automation_debugger):
        """Test automation debugger initialization"""
        assert automation_debugger.state == AutomationState.STARTING
        assert automation_debugger.retry_count == 0
        assert automation_debugger.max_retries == 3
        assert automation_debugger.screenshots_dir.exists()
        assert len(automation_debugger.errors) == 0
        
    def test_error_handling(self, automation_debugger):
        """Test error handling and reporting"""
        # Simulate an error
        automation_debugger._handle_error(
            "test_error",
            "This is a test error",
            "Test stack trace",
            "Test suggested fix"
        )
        
        assert len(automation_debugger.errors) == 1
        error = automation_debugger.errors[0]
        assert error.error_type == "test_error"
        assert error.message == "This is a test error"
        assert error.suggested_fix == "Test suggested fix"
        assert automation_debugger.state == AutomationState.ERROR_DETECTED
        
    def test_suggest_fix_functionality(self, automation_debugger):
        """Test error fix suggestions"""
        # Test known error types
        fix = automation_debugger._suggest_fix("launch_failure")
        assert "dependencies" in fix.lower()
        
        fix = automation_debugger._suggest_fix("button_not_found")
        assert "ui layout" in fix.lower()
        
        fix = automation_debugger._suggest_fix("training_error")
        assert "configuration" in fix.lower()
        
        # Test unknown error type
        fix = automation_debugger._suggest_fix("unknown_error")
        assert "check logs" in fix.lower()
        
    def test_ui_state_capture(self, automation_debugger):
        """Test UI state capture functionality"""
        state = automation_debugger._capture_ui_state()
        
        assert "automation_state" in state
        assert "main_window_visible" in state
        assert "training_dialog_visible" in state
        assert "active_widgets" in state
        assert "visible_buttons" in state
        
    def test_screenshot_functionality(self, automation_debugger):
        """Test screenshot capture (mocked)"""
        with patch.object(automation_debugger, 'main_window') as mock_window:
            mock_pixmap = Mock()
            mock_window.grab.return_value = mock_pixmap
            
            screenshot_path = automation_debugger._take_screenshot("test_screenshot")
            
            # Should attempt to take screenshot
            mock_window.grab.assert_called_once()
            mock_pixmap.save.assert_called_once()
            
    def test_retry_mechanism(self, automation_debugger):
        """Test retry mechanism"""
        # Set retry count to max - 1
        automation_debugger.retry_count = automation_debugger.max_retries - 1
        
        # Mock the run_automation method to return False
        with patch.object(automation_debugger, 'run_automation', return_value=False):
            result = automation_debugger.retry_automation()
            assert result is False  # Should fail because max retries reached
            
        # Test with retries available
        automation_debugger.retry_count = 0
        with patch.object(automation_debugger, 'run_automation', return_value=True):
            result = automation_debugger.retry_automation()
            assert result is True
            
    def test_summary_report_generation(self, automation_debugger):
        """Test summary report generation"""
        # Test with no errors
        report = automation_debugger.generate_summary_report()
        assert "No errors detected" in report
        assert automation_debugger.state.value in report
        
        # Test with errors
        automation_debugger._handle_error("test_error", "Test message", "Test trace")
        report = automation_debugger.generate_summary_report()
        assert "ERRORS DETECTED" in report
        assert "test_error" in report
        assert "Test message" in report
        
    @pytest.mark.integration
    def test_full_automation_workflow_mock(self, automation_debugger):
        """Test full automation workflow with mocked components"""
        # Mock all the UI components and simulate state changes
        def mock_monitor_training():
            automation_debugger.state = AutomationState.COMPLETED
            return True

        with patch.object(automation_debugger, '_launch_application', return_value=True), \
             patch.object(automation_debugger, '_navigate_to_training', return_value=True), \
             patch.object(automation_debugger, '_select_mistral_model', return_value=True), \
             patch.object(automation_debugger, '_monitor_training', side_effect=mock_monitor_training):

            result = automation_debugger.run_automation()
            assert result is True
            assert automation_debugger.state == AutomationState.COMPLETED
            
    @pytest.mark.integration
    def test_automation_failure_handling(self, automation_debugger):
        """Test automation failure handling"""
        # Mock launch failure
        with patch.object(automation_debugger, '_launch_application', return_value=False):
            result = automation_debugger.run_automation()
            assert result is False
            
        # Mock navigation failure
        with patch.object(automation_debugger, '_launch_application', return_value=True), \
             patch.object(automation_debugger, '_navigate_to_training', return_value=False):
            result = automation_debugger.run_automation()
            assert result is False
            
    def test_error_dialog_detection(self, automation_debugger):
        """Test error dialog detection functionality"""
        # Test the method directly with a simpler approach
        automation_debugger.app = None  # No app means no dialogs found
        error_dialogs = automation_debugger._find_error_dialogs()
        assert len(error_dialogs) == 0

        # Test with app but no widgets
        automation_debugger.app = Mock()
        automation_debugger.app.allWidgets.return_value = []
        error_dialogs = automation_debugger._find_error_dialogs()
        assert len(error_dialogs) == 0
        
    def test_training_error_handling(self, automation_debugger):
        """Test specific training error handling"""
        # Mock error dialog with eval_strategy error
        mock_dialog = Mock()
        mock_dialog.text.return_value = "Training failed: You have set args.eval_strategy to steps but you didn't pass an eval_dataset"
        mock_dialog.accept = Mock()
        
        # Mock screenshot method
        with patch.object(automation_debugger, '_take_screenshot'):
            automation_debugger._handle_training_errors([mock_dialog])
            
        # Check that error was handled
        assert len(automation_debugger.errors) == 1
        error = automation_debugger.errors[0]
        assert error.error_type == "training_error"
        assert "eval_dataset" in error.suggested_fix
        
        # Check that dialog was dismissed
        mock_dialog.accept.assert_called_once()


@pytest.mark.slow
class TestRealGUIAutomation:
    """Tests that actually run the GUI (marked as slow)"""
    
    def test_real_automation_run(self):
        """Test GUI automation with comprehensive mocking"""
        debugger = GUIAutomationDebugger()

        # Mock all GUI operations
        with patch.object(debugger, '_launch_application', return_value=True), \
             patch.object(debugger, '_navigate_to_training', return_value=True), \
             patch.object(debugger, '_select_mistral_model', return_value=True), \
             patch.object(debugger, '_monitor_training') as mock_monitor:

            # Mock monitor training to simulate completion
            def mock_monitor_training():
                debugger.state = AutomationState.COMPLETED
                return True

            mock_monitor.side_effect = mock_monitor_training

            # Set shorter timeouts for testing
            debugger.max_retries = 1
            debugger.step_delay = 0.01

            # Run automation
            result = debugger.run_automation()

            # Verify results
            assert result is True
            assert debugger.state == AutomationState.COMPLETED

            # Verify all steps were called
            assert debugger._launch_application.called
            assert debugger._navigate_to_training.called
            assert debugger._select_mistral_model.called
            assert mock_monitor.called


def pytest_addoption(parser):
    """Add custom pytest options"""
    parser.addoption(
        "--run-gui-tests",
        action="store_true",
        default=False,
        help="Run actual GUI automation tests (slow)"
    )


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
