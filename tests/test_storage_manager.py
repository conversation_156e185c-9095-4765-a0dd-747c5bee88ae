"""
Storage Manager Tests
"""
import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch
from knowledge_app.core.storage_manager import StorageManager

class TestStorageManager:
    """Test storage management functionality"""

    @pytest.fixture
    def storage_config(self, temp_dir):
        """Default storage manager configuration"""
        return {
            'data_path': str(temp_dir),
            'max_cache_size': 1024 * 1024,  # 1MB
            'cleanup_threshold': 0.85,
            'cache_expiry': 3600,  # 1 hour
            'use_mmap': True
        }

    def test_initialization(self, storage_config):
        """Test StorageManager initialization"""
        storage = StorageManager(storage_config)
        try:
            assert storage.data_path == Path(storage_config['data_path'])
            assert storage.data_path.exists()
            assert storage.models_path.exists()
            assert storage.user_data_path.exists()
            assert storage.uploaded_books_path.exists()
        finally:
            storage.cleanup()
    
    def test_calculate_directory_size(self, sample_data_dir, storage_config):
        """Test directory size calculation"""
        storage = StorageManager(storage_config)
        try:
            size = storage.calculate_directory_size(sample_data_dir)

            assert isinstance(size, int)
            assert size >= 0

            # Should be greater than 0 since we have sample files
            assert size > 0
        finally:
            storage.cleanup()

    def test_ensure_space_available(self, storage_config):
        """Test ensuring space availability"""
        storage = StorageManager(storage_config)
        try:
            # Test with reasonable space requirement
            result = storage.ensure_space_available(1024)  # 1KB
            assert isinstance(result, bool)

            # Test with excessive space requirement should raise error
            with pytest.raises(OSError):
                storage.ensure_space_available(10 * 1024 * 1024 * 1024)  # 10GB
        finally:
            storage.cleanup()
    
    def test_add_file_to_cache(self, sample_data_dir, storage_config):
        """Test adding files to cache"""
        storage = StorageManager(storage_config)
        try:
            source_file = sample_data_dir / "sample.txt"

            # Test successful file addition
            cached_path = storage.add_file_to_cache(source_file, "test_category")

            if cached_path:  # If caching succeeded
                assert cached_path.exists()
                assert "test_category" in str(cached_path)

            # Test with non-existent file
            with pytest.raises(FileNotFoundError):
                storage.add_file_to_cache(storage.data_path / "nonexistent.txt", "test")

            # Test with empty category
            with pytest.raises(ValueError):
                storage.add_file_to_cache(source_file, "")
        finally:
            storage.cleanup()
    
    def test_get_cached_file(self, storage_config):
        """Test retrieving cached files"""
        storage = StorageManager(storage_config)
        try:
            # Create a test cached file
            cache_dir = storage.base_path / "test_category"
            cache_dir.mkdir(parents=True, exist_ok=True)
            test_file = cache_dir / "test_file.txt"
            test_file.write_text("test content")

            # Test successful retrieval
            cached_file = storage.get_cached_file("test_file.txt", "test_category")

            if cached_file:
                assert cached_file.exists()
                assert cached_file.name == "test_file.txt"

            # Test non-existent file
            non_existent = storage.get_cached_file("nonexistent.txt", "test_category")
            assert non_existent is None
        finally:
            storage.cleanup()
    
    def test_clear_cache(self, storage_config):
        """Test clearing cache"""
        storage = StorageManager(storage_config)
        try:
            # Create some test files
            test_dir = storage.base_path / "test_subdir"
            test_dir.mkdir(parents=True, exist_ok=True)
            test_file = test_dir / "test_file.txt"
            test_file.write_text("test content")

            # Verify files exist
            assert test_file.exists()

            # Clear cache
            result = storage.clear_cache()
            assert isinstance(result, bool)

            # Files should be removed (if clear_cache worked)
            if result:
                assert not test_file.exists()
        finally:
            storage.cleanup()
    
    def test_storage_info(self, storage_config):
        """Test getting storage information"""
        storage = StorageManager(storage_config)
        try:
            # Test that storage info methods exist
            assert hasattr(storage, 'get_storage_info')

            # Test storage info retrieval
            info = storage.get_storage_info()
            assert isinstance(info, dict)
        finally:
            storage.cleanup()

    def test_get_storage_usage(self, storage_config):
        """Test getting storage usage"""
        storage = StorageManager(storage_config)
        try:
            # Create a test file
            test_file = storage.base_path / "test_file.txt"
            test_file.write_text("test content for storage usage")

            usage = storage.get_storage_usage()

            assert isinstance(usage, int)
            assert usage >= 0

            # Should be greater than 0 since we created a file
            assert usage > 0
        finally:
            storage.cleanup()
    
    def test_error_handling_invalid_file(self, storage_config):
        """Test error handling with invalid files"""
        storage = StorageManager(storage_config)
        try:
            # Test with non-existent source file
            with pytest.raises(FileNotFoundError):
                storage.add_file_to_cache("/nonexistent/path/file.txt", "test")
        finally:
            storage.cleanup()

    def test_error_handling_invalid_category(self, sample_data_dir, storage_config):
        """Test error handling with invalid category"""
        storage = StorageManager(storage_config)
        try:
            source_file = sample_data_dir / "sample.txt"

            # Test with empty category
            with pytest.raises(ValueError):
                storage.add_file_to_cache(source_file, "")

            # Test with whitespace-only category
            with pytest.raises(ValueError):
                storage.add_file_to_cache(source_file, "   ")
        finally:
            storage.cleanup()
    
    @patch.object(StorageManager, 'calculate_directory_size')
    def test_error_handling_insufficient_space(self, mock_calc_size, storage_config):
        """Test error handling when insufficient space"""
        # Mock directory size to be larger than max_size
        mock_calc_size.return_value = 2 * 1024 * 1024  # 2MB

        storage = StorageManager(storage_config)
        try:
            # Should raise OSError due to insufficient space
            with pytest.raises(OSError):
                storage.ensure_space_available(1024)
        finally:
            storage.cleanup()

    def test_concurrent_access(self, storage_config):
        """Test concurrent access to storage manager"""
        import threading

        storage = StorageManager(storage_config)
        try:
            results = []

            def worker():
                try:
                    # Simulate concurrent operations
                    usage = storage.get_storage_usage()
                    results.append(usage)
                except Exception as e:
                    results.append(f"Error: {e}")

            # Create multiple threads
            threads = []
            for i in range(5):
                thread = threading.Thread(target=worker)
                threads.append(thread)
                thread.start()

            # Wait for all threads to complete
            for thread in threads:
                thread.join()

            # All operations should complete successfully
            assert len(results) == 5
            for result in results:
                assert isinstance(result, int) or "Error" in str(result)
        finally:
            storage.cleanup()
