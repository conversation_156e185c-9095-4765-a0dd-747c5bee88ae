"""
Core functionality tests - imports, basic setup, and essential features
"""
import pytest
import sys
import os
from pathlib import Path

class TestCoreImports:
    """Test that all essential imports work correctly"""
    
    def test_basic_python_imports(self):
        """Test basic Python library imports"""
        import json
        import sqlite3
        import threading
        import logging
        import tempfile
        import shutil
        assert True
    
    def test_third_party_imports(self):
        """Test third-party library imports"""
        import torch
        import numpy as np
        import PIL
        from PyQt5.QtCore import QObject
        assert True
    
    def test_torch_functionality(self):
        """Test PyTorch basic functionality"""
        import torch
        
        # Test tensor creation
        x = torch.randn(2, 3)
        assert x.shape == (2, 3)
        
        # Test device availability
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        assert device is not None
        
        # Test tensor operations
        y = torch.ones(2, 3)
        z = x + y
        assert z.shape == (2, 3)
    
    def test_project_structure(self):
        """Test that project structure is correct"""
        project_root = Path(__file__).parent.parent
        
        # Check essential directories exist
        assert (project_root / "src").exists()
        assert (project_root / "src" / "knowledge_app").exists()
        assert (project_root / "src" / "knowledge_app" / "core").exists()
        assert (project_root / "src" / "knowledge_app" / "ui").exists()
    
    def test_config_imports(self):
        """Test configuration system imports"""
        try:
            from knowledge_app.core.config_manager import ConfigManager, get_config
            assert True
        except ImportError as e:
            pytest.fail(f"Config import failed: {e}")
    
    def test_core_module_imports(self):
        """Test core module imports"""
        try:
            from knowledge_app.core.memory_manager import MemoryManager
            from knowledge_app.core.storage_manager import StorageManager
            from knowledge_app.core.model_manager import ModelManager
            assert True
        except ImportError as e:
            pytest.fail(f"Core module import failed: {e}")

class TestBasicFunctionality:
    """Test basic application functionality"""
    
    def test_python_version(self):
        """Test Python version compatibility"""
        assert sys.version_info >= (3, 8), "Python 3.8+ required"
    
    def test_app_directories(self):
        """Test application directory structure"""
        from knowledge_app.core.config_manager import get_config
        
        config = get_config()
        
        # Test that config can be loaded
        assert config is not None
        
        # Test basic config access
        try:
            base_dir = config.get_value('directories.base_dir', '.')
            assert isinstance(base_dir, str)
        except Exception:
            # If config access fails, that's still acceptable for basic test
            pass
    
    def test_memory_manager_basic(self):
        """Test basic memory manager functionality"""
        from knowledge_app.core.memory_manager import MemoryManager
        import tempfile

        # Create config for memory manager
        with tempfile.TemporaryDirectory() as temp_dir:
            config = {
                'memory_cache_path': temp_dir,
                'memory_cache_size': 1024*1024,  # 1MB
                'memory_threshold': 0.7,
                'cleanup_threshold': 0.7,
                'cache_expiry': 1800
            }

            try:
                manager = MemoryManager(config)

                # Test basic memory info
                memory_info = manager.get_memory_info()
                assert isinstance(memory_info, dict)
                assert 'available' in memory_info
                assert 'total' in memory_info

                # Cleanup
                manager.cleanup()

            except Exception as e:
                # If memory manager fails to initialize, that's acceptable for basic test
                pytest.skip(f"MemoryManager initialization failed: {e}")

    def test_storage_manager_basic(self):
        """Test basic storage manager functionality"""
        from knowledge_app.core.storage_manager import StorageManager
        import tempfile

        with tempfile.TemporaryDirectory() as temp_dir:
            config = {
                'data_path': temp_dir,
                'max_cache_size': 1024*1024,  # 1MB
                'cleanup_threshold': 0.7,
                'cache_expiry': 1800
            }

            try:
                storage = StorageManager(config)

                # Test initialization
                assert storage.data_path.exists()

                # Test directory size calculation
                size = storage.calculate_directory_size(temp_dir)
                assert isinstance(size, int)
                assert size >= 0

                # Cleanup
                storage.cleanup()

            except Exception as e:
                # If storage manager fails to initialize, that's acceptable for basic test
                pytest.skip(f"StorageManager initialization failed: {e}")
