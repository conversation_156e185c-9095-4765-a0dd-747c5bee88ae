"""
Configuration Manager Tests
"""
import pytest
import tempfile
import json
from pathlib import Path
from knowledge_app.core.config_manager import Config<PERSON>anager, get_config

class TestConfigManager:
    """Test configuration management functionality"""
    
    def test_singleton_pattern(self):
        """Test that ConfigManager follows singleton pattern"""
        config1 = ConfigManager()
        config2 = ConfigManager()
        assert config1 is config2
    
    def test_get_config_function(self):
        """Test the get_config convenience function"""
        config1 = get_config()
        config2 = get_config()
        assert config1 is config2
        assert isinstance(config1, ConfigManager)
    
    def test_default_config(self):
        """Test default configuration values"""
        config = ConfigManager()

        # Test that default config is loaded
        assert config._config is not None
        assert isinstance(config._config, dict)

        # Test some expected default values
        assert 'app_settings' in config._config
        assert 'paths' in config._config
        assert 'storage_config' in config._config
    
    def test_get_value(self):
        """Test getting configuration values"""
        config = ConfigManager()
        
        # Test getting existing value
        base_dir = config.get_value('directories.base_dir', 'default')
        assert isinstance(base_dir, str)
        
        # Test getting non-existent value with default
        non_existent = config.get_value('non.existent.key', 'default_value')
        assert non_existent == 'default_value'
        
        # Test nested key access
        try:
            training_config = config.get_value('training', {})
            assert isinstance(training_config, dict)
        except Exception:
            # If training config doesn't exist, that's acceptable
            pass
    
    def test_set_value(self):
        """Test setting configuration values"""
        config = ConfigManager()
        
        # Set a test value
        config.set_value('test.key', 'test_value')
        
        # Verify it was set
        retrieved_value = config.get_value('test.key')
        assert retrieved_value == 'test_value'
        
        # Test nested key setting
        config.set_value('test.nested.deep.key', 42)
        retrieved_nested = config.get_value('test.nested.deep.key')
        assert retrieved_nested == 42
    
    def test_update_config(self):
        """Test updating configuration with dictionary"""
        config = ConfigManager()

        update_data = {
            'test_section': {
                'key1': 'value1',
                'key2': 'value2'
            }
        }

        # Use the private method since public one doesn't exist
        config._update_config(update_data)

        # Verify updates
        assert config.get_value('test_section.key1') == 'value1'
        assert config.get_value('test_section.key2') == 'value2'
    
    def test_save_and_load_user_settings(self, temp_dir):
        """Test saving and loading user settings"""
        config = ConfigManager()
        
        # Create a temporary config file
        config_file = temp_dir / "test_config.json"
        
        # Set some test values
        test_data = {
            'user_settings': {
                'theme': 'dark',
                'language': 'en',
                'auto_save': True
            }
        }
        
        # Save to file
        with open(config_file, 'w') as f:
            json.dump(test_data, f)
        
        # Test that file was created
        assert config_file.exists()
        
        # Test loading
        with open(config_file, 'r') as f:
            loaded_data = json.load(f)
        
        assert loaded_data == test_data
    
    def test_create_directories(self, temp_dir):
        """Test directory creation functionality"""
        config = ConfigManager()
        
        # Test creating a directory structure
        test_dirs = [
            temp_dir / "models",
            temp_dir / "cache",
            temp_dir / "logs"
        ]
        
        for directory in test_dirs:
            directory.mkdir(parents=True, exist_ok=True)
            assert directory.exists()
            assert directory.is_dir()
    
    def test_reset_to_defaults(self):
        """Test resetting configuration to defaults"""
        config = ConfigManager()

        # Modify some values
        config.set_value('test.modified', 'modified_value')
        assert config.get_value('test.modified') == 'modified_value'

        # Reset to defaults (reload default config)
        config._config = config._load_default_config()

        # Test value should be gone
        assert config.get_value('test.modified', None) is None
    
    def test_file_size_limit_settings(self):
        """Test file size limit configuration"""
        config = ConfigManager()
        
        # Test setting file size limits
        config.set_value('limits.max_file_size', 10 * 1024 * 1024)  # 10MB
        config.set_value('limits.max_cache_size', 100 * 1024 * 1024)  # 100MB
        
        # Verify settings
        max_file_size = config.get_value('limits.max_file_size')
        max_cache_size = config.get_value('limits.max_cache_size')
        
        assert max_file_size == 10 * 1024 * 1024
        assert max_cache_size == 100 * 1024 * 1024
