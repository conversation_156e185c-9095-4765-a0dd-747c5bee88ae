"""
Hardware Acceleration Tests
"""
import pytest
import torch
import numpy as np
from PyQt5.QtCore import QObject

class TestHardwareAcceleration:
    """Test hardware acceleration functionality"""
    
    def test_torch_device_available(self):
        """Test that PyTorch device detection works"""
        # Test CUDA availability detection
        cuda_available = torch.cuda.is_available()
        assert isinstance(cuda_available, bool)
        
        # Test device creation
        if cuda_available:
            device = torch.device('cuda')
            assert device.type == 'cuda'
        
        # CPU device should always be available
        cpu_device = torch.device('cpu')
        assert cpu_device.type == 'cpu'
        
        # Test automatic device selection
        auto_device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        assert auto_device.type in ['cuda', 'cpu']
    
    def test_tensor_operations(self, torch_device):
        """Test basic tensor operations on available device"""
        # Create tensors
        x = torch.randn(3, 4, device=torch_device)
        y = torch.randn(3, 4, device=torch_device)

        # Test basic operations
        z = x + y
        assert z.device.type == torch_device.type
        assert z.shape == (3, 4)

        # Test matrix multiplication
        a = torch.randn(3, 4, device=torch_device)
        b = torch.randn(4, 5, device=torch_device)
        c = torch.mm(a, b)
        assert c.device.type == torch_device.type
        assert c.shape == (3, 5)

        # Test tensor movement between devices
        cpu_tensor = z.cpu()
        assert cpu_tensor.device.type == 'cpu'

        if torch.cuda.is_available():
            cuda_tensor = cpu_tensor.cuda()
            assert cuda_tensor.device.type == 'cuda'
    
    def test_numpy_interop(self):
        """Test NumPy and PyTorch interoperability"""
        # Create NumPy array
        np_array = np.random.randn(3, 4).astype(np.float32)
        
        # Convert to PyTorch tensor
        torch_tensor = torch.from_numpy(np_array)
        assert torch_tensor.shape == (3, 4)
        assert torch_tensor.dtype == torch.float32
        
        # Convert back to NumPy
        np_array_back = torch_tensor.numpy()
        assert np.array_equal(np_array, np_array_back)
        
        # Test with different data types
        int_array = np.array([1, 2, 3, 4], dtype=np.int64)
        int_tensor = torch.from_numpy(int_array)
        assert int_tensor.dtype == torch.int64
    
    def test_cuda_operations(self):
        """Test CUDA-specific operations if available"""
        if not torch.cuda.is_available():
            pytest.skip("CUDA not available")
        
        # Test CUDA device properties
        device_count = torch.cuda.device_count()
        assert device_count > 0
        
        current_device = torch.cuda.current_device()
        assert isinstance(current_device, int)
        assert 0 <= current_device < device_count
        
        # Test CUDA memory management
        if torch.cuda.is_available():
            # Get memory info
            memory_allocated = torch.cuda.memory_allocated()
            memory_reserved = torch.cuda.memory_reserved()
            
            assert isinstance(memory_allocated, int)
            assert isinstance(memory_reserved, int)
            assert memory_allocated >= 0
            assert memory_reserved >= memory_allocated
            
            # Test tensor on CUDA
            cuda_tensor = torch.randn(100, 100, device='cuda')
            assert cuda_tensor.is_cuda
            
            # Check memory increased
            new_memory_allocated = torch.cuda.memory_allocated()
            assert new_memory_allocated >= memory_allocated
            
            # Clean up
            del cuda_tensor
            torch.cuda.empty_cache()
            
            print(f"CUDA Device: {torch.cuda.get_device_name()}")
            print(f"CUDA Version: {torch.version.cuda}")
            print(f"Memory Allocated: {memory_allocated / 1024**2:.2f} MB")
    
    def test_qt_torch_interaction(self):
        """Test that Qt and PyTorch can coexist"""
        # Create a simple Qt object
        qt_obj = QObject()
        assert qt_obj is not None
        
        # Create a PyTorch tensor
        tensor = torch.randn(2, 3)
        assert tensor.shape == (2, 3)
        
        # Test that both can be used together
        # Move tensor to CPU if it's on CUDA before converting to numpy
        tensor_data = tensor.cpu().numpy().tolist()
        assert isinstance(tensor_data, list)
        assert len(tensor_data) == 2
        assert len(tensor_data[0]) == 3
        
        # Clean up Qt object
        qt_obj.deleteLater()
    
    def test_autocast_functionality(self):
        """Test automatic mixed precision if available"""
        if not torch.cuda.is_available():
            pytest.skip("CUDA not available for autocast testing")
        
        device = torch.device('cuda')
        
        # Test autocast context
        with torch.autocast(device_type='cuda'):
            x = torch.randn(10, 10, device=device)
            y = torch.randn(10, 10, device=device)
            z = torch.mm(x, y)
            
            assert z.device.type == device.type
            assert z.shape == (10, 10)

        # Test without autocast
        x = torch.randn(10, 10, device=device)
        y = torch.randn(10, 10, device=device)
        z = torch.mm(x, y)

        assert z.device.type == device.type
        assert z.shape == (10, 10)
    
    def test_gradient_computation(self, torch_device):
        """Test gradient computation functionality"""
        # Create tensors with gradient tracking
        x = torch.randn(3, 4, device=torch_device, requires_grad=True)
        y = torch.randn(3, 4, device=torch_device, requires_grad=True)
        
        # Perform operations
        z = x * y
        loss = z.sum()
        
        # Compute gradients
        loss.backward()
        
        # Check gradients exist
        assert x.grad is not None
        assert y.grad is not None
        assert x.grad.shape == x.shape
        assert y.grad.shape == y.shape
        assert x.grad.device.type == torch_device.type
        assert y.grad.device.type == torch_device.type
    
    def test_model_device_placement(self, torch_device):
        """Test model device placement"""
        # Create a simple model
        model = torch.nn.Linear(10, 5)
        
        # Move to device
        model = model.to(torch_device)
        
        # Check model parameters are on correct device
        for param in model.parameters():
            assert param.device.type == torch_device.type

        # Test forward pass
        input_tensor = torch.randn(3, 10, device=torch_device)
        output = model(input_tensor)

        assert output.device.type == torch_device.type
        assert output.shape == (3, 5)
