# Core dependencies
numpy>=1.21.0,<1.26.0
pandas>=2.0.0,<3.0.0
matplotlib>=3.7.0,<4.0.0
scikit-learn>=1.3.0,<1.4.0
PyQt5>=5.15.9,<6.0.0
PyQt5-sip>=12.17.0,<13.0.0

# Additional dependencies
tqdm>=4.65.0,<5.0.0
loguru>=0.7.0,<0.8.0
python-dotenv>=1.0.0,<2.0.0
requests>=2.31.0,<3.0.0
pillow>=10.0.0,<11.0.0
nltk>=3.8.1,<4.0.0
pdfplumber>=0.9.0,<1.0.0
python-docx>=0.8.11,<0.9.0
lxml>=5.0.0,<6.0.0
cryptography>=45.0.0,<46.0.0
aiofiles>=24.1.0,<25.0.0  # Added for async file operations in storage_manager
pywin32>=310; platform_system=="Windows"  # Added for Windows-specific file operations

# Development dependencies
pytest>=8.0.0,<9.0.0
black>=23.7.0,<24.0.0
flake8>=6.1.0,<7.0.0
mypy>=1.5.1,<2.0.0

# Document processing dependencies
pypdf>=5.6.0,<6.0.0
textstat>=0.7.3,<0.8.0
python-magic>=0.4.27,<0.5.0
charset-normalizer>=3.2.0,<4.0.0

# Image processing
opencv-python>=4.8.0,<5.0.0

# Utilities
psutil>=5.9.0,<6.0.0
keyring>=24.3.0,<25.0.0
py-cpuinfo>=9.0.0,<10.0.0

# Development dependencies
pytest-asyncio>=0.21.1,<0.22.0
pytest-qt>=4.2.0,<5.0.0
pre-commit>=3.3.3,<4.0.0

# Optional dependencies for enhanced features
rich>=13.6.0,<14.0.0
typer>=0.9.0,<1.0.0
pydantic>=2.11.0,<3.0.0
tenacity>=8.2.0,<9.0.0

# Legacy dependencies (maintain compatibility)
piexif>=1.1.3,<2.0.0
PyMuPDF>=1.23.8,<2.0.0

# ML/AI dependencies - Compatible versions for CUDA 12.1 and PyTorch 2.5.1
torch>=2.5.0,<2.6.0  # Match current PyTorch version
torchvision>=0.20.0,<1.0.0  # Compatible with PyTorch 2.5.x
torchaudio>=2.5.0,<2.6.0  # Match PyTorch version
transformers>=4.36.0,<5.0.0
huggingface_hub>=0.20.0,<1.0.0
tokenizers>=0.21.0,<0.22.0
datasets>=2.14.0,<3.0.0
accelerate>=0.20.0,<1.0.0
peft>=0.4.0,<1.0.0
bitsandbytes>=0.41.0,<1.0.0
safetensors>=0.3.0,<1.0.0

# Performance optimization for Hugging Face downloads
hf_xet>=0.1.0,<1.0.0  # Optimized Xet Storage for faster model downloads

# Additional requirements for training
sentence-transformers>=2.2.0,<3.0.0
faiss-cpu>=1.7.0,<2.0.0

# Expert optimization packages for 7B training
trl>=0.7.0,<1.0.0  # For SFTTrainer with packing support
packaging>=21.0  # For version checking

# Attention optimization packages - Compatible with PyTorch 2.5.1+cu121
# flash-attn>=2.0.0,<3.0.0  # Flash Attention 2 for Linux/WSL (install separately if needed)
# xFormers - Use compatible version with PyTorch 2.5.1+cu121
# Install manually: pip install xformers==0.0.28.post2 --index-url https://download.pytorch.org/whl/cu121

# Multimodal document processing dependencies
pdf2image>=1.16.0,<2.0.0
pytesseract>=0.3.10,<1.0.0
opencv-python>=4.8.0,<5.0.0

pandas>=2.0.0,<3.0.0
timm>=0.9.0,<1.0.0  # For table transformer models