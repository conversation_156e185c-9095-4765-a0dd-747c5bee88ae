#!/usr/bin/env python3
"""
Simple test script to verify the quiz generation fix.
This script tests only the instant generation pipeline without loading heavy models.
"""

import sys
import os
import logging

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_instant_mcq_generation():
    """Test instant MCQ generation only"""
    try:
        logger.info("🧪 Testing instant MCQ generation...")
        
        from knowledge_app.core.instant_mcq_generator import InstantMCQGenerator
        
        # Create instant generator
        instant_generator = InstantMCQGenerator()
        
        # Test synchronous generation
        import asyncio
        
        context = "Generate a multiple choice question about physics. The question should be suitable for medium difficulty learning."
        difficulty = "medium"
        
        # Test async generation
        result = asyncio.run(instant_generator.generate_quiz_async(context, difficulty))
        
        if result and result.get('question'):
            logger.info("✅ Instant MCQ generator successful!")
            logger.info(f"📝 Question: {result['question'][:100]}...")
            logger.info(f"🎯 Options: {len(result.get('options', []))} options")
            logger.info(f"✅ Correct answer: {result.get('correct_answer', 'N/A')}")
            return True
        else:
            logger.error("❌ Instant MCQ generator returned empty result")
            return False
            
    except Exception as e:
        logger.error(f"❌ Instant MCQ generation test failed: {e}")
        return False

def test_fallback_question_generation():
    """Test fallback question generation"""
    try:
        logger.info("🧪 Testing fallback question generation...")
        
        # Import Qt modules
        from PyQt5.QtWidgets import QApplication
        from knowledge_app.ui.main_window import MainWindow
        
        # Create minimal Qt application
        app = QApplication([])
        
        # Create main window
        main_window = MainWindow()
        
        # Test fallback question generation
        fallback_question = main_window._generate_fallback_question("physics", "Multiple Choice")
        
        if fallback_question and fallback_question.get('question'):
            logger.info("✅ Fallback question generation successful!")
            logger.info(f"📝 Question: {fallback_question['question'][:100]}...")
            logger.info(f"🎯 Options: {len(fallback_question.get('options', []))} options")
            logger.info(f"✅ Correct answer: {fallback_question.get('correct_answer', 'N/A')}")
            logger.info(f"🔧 Generation method: {fallback_question.get('generation_method', 'unknown')}")
            return True
        else:
            logger.error("❌ Fallback question generation returned empty result")
            return False
            
    except Exception as e:
        logger.error(f"❌ Fallback question generation test failed: {e}")
        return False

def test_quiz_controller_methods():
    """Test quiz controller method availability"""
    try:
        logger.info("🧪 Testing quiz controller method availability...")
        
        # Import Qt modules
        from PyQt5.QtWidgets import QApplication
        from knowledge_app.ui.main_window import MainWindow
        from knowledge_app.ui.controllers.quiz_controller import QuizController
        
        # Create minimal Qt application
        app = QApplication([])
        
        # Create main window
        main_window = MainWindow()
        
        # Set up quiz parameters
        main_window.current_topic = "biology"
        main_window.current_mode = "Serious"
        main_window.current_submode = "Multiple Choice"
        
        # Check if required methods exist
        methods_to_check = [
            'get_next_question_sync',
            'generate_next_question',
            'load_new_question',
            '_generate_fallback_question',
            '_generate_emergency_fallback_question'
        ]
        
        missing_methods = []
        for method_name in methods_to_check:
            if not hasattr(main_window, method_name):
                missing_methods.append(method_name)
            else:
                logger.info(f"✅ Method {method_name} exists")
        
        if missing_methods:
            logger.error(f"❌ Missing methods: {missing_methods}")
            return False
        
        # Test emergency fallback
        emergency_question = main_window._generate_emergency_fallback_question()
        if emergency_question and emergency_question.get('question'):
            logger.info("✅ Emergency fallback question generation successful!")
            logger.info(f"📝 Question: {emergency_question['question'][:100]}...")
            return True
        else:
            logger.error("❌ Emergency fallback question generation failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Quiz controller method test failed: {e}")
        return False

def test_quiz_controller_integration_simple():
    """Test quiz controller integration with simple fallback"""
    try:
        logger.info("🧪 Testing quiz controller integration (simple)...")
        
        # Import Qt modules
        from PyQt5.QtWidgets import QApplication
        from knowledge_app.ui.main_window import MainWindow
        from knowledge_app.ui.controllers.quiz_controller import QuizController
        
        # Create minimal Qt application
        app = QApplication([])
        
        # Create main window
        main_window = MainWindow()
        
        # Set up quiz parameters
        main_window.current_topic = "chemistry"
        main_window.current_mode = "Casual"
        main_window.current_submode = "Multiple Choice"
        
        # Create quiz controller with main window as parent
        quiz_controller = QuizController(main_window)
        
        # Test that the controller can find the sync method
        if hasattr(main_window, 'get_next_question_sync'):
            logger.info("✅ Quiz controller can find get_next_question_sync method")
            
            # Test the method directly
            question_data = main_window.get_next_question_sync()
            
            if question_data and question_data.get('question'):
                logger.info("✅ Quiz controller sync integration successful!")
                logger.info(f"📝 Question: {question_data['question'][:100]}...")
                logger.info(f"🎯 Options: {len(question_data.get('options', []))} options")
                logger.info(f"✅ Correct answer: {question_data.get('correct_answer', 'N/A')}")
                logger.info(f"🔧 Generation method: {question_data.get('generation_method', 'unknown')}")
                return True
            else:
                logger.error("❌ Quiz controller sync integration returned empty result")
                return False
        else:
            logger.error("❌ Quiz controller cannot find get_next_question_sync method")
            return False
            
    except Exception as e:
        logger.error(f"❌ Quiz controller integration test failed: {e}")
        return False

def main():
    """Run all tests"""
    logger.info("🚀 Starting simple quiz generation fix tests...")
    
    tests = [
        ("Instant MCQ Generation", test_instant_mcq_generation),
        ("Fallback Question Generation", test_fallback_question_generation),
        ("Quiz Controller Methods", test_quiz_controller_methods),
        ("Quiz Controller Integration (Simple)", test_quiz_controller_integration_simple),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 Running test: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"💥 {test_name}: CRASHED - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("📊 TEST SUMMARY")
    logger.info(f"{'='*60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status}: {test_name}")
    
    logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Quiz generation fix is working correctly.")
        return 0
    else:
        logger.error("💥 Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
