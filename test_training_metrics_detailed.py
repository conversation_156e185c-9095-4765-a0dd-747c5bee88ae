#!/usr/bin/env python3
"""
Training Metrics Detailed Import Test

This script investigates exactly why importing training_metrics causes torch to be imported.
"""

import sys
import os

# Add the src directory to path for local imports
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

def test_step_by_step():
    """Test step by step what causes torch import when importing training_metrics"""
    
    heavy_modules = ['torch', 'transformers', 'peft', 'datasets']
    
    print("🔍 Testing step-by-step training_metrics import...")
    
    # Step 1: Check initial state
    print("\n📦 Step 1: Initial state")
    before = [m for m in heavy_modules if m in sys.modules]
    print(f"Heavy modules before: {before}")
    print(f"Total modules loaded: {len(sys.modules)}")
    
    # Step 2: Import just the dataclass decorator
    print("\n📦 Step 2: Import dataclass")
    from dataclasses import dataclass
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after dataclass: {after}")
    if newly_imported:
        print(f"⚠️ dataclass imported: {newly_imported}")
    
    # Step 3: Import typing
    print("\n📦 Step 3: Import typing")
    before = after
    from typing import Optional
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after typing: {after}")
    if newly_imported:
        print(f"⚠️ typing imported: {newly_imported}")
    
    # Step 4: Try importing the training_metrics module directly
    print("\n📦 Step 4: Import training_metrics module")
    before = after
    
    # Check what modules are loaded when we import the package
    print("Modules before importing knowledge_app.core:")
    core_modules_before = [m for m in sys.modules.keys() if 'knowledge_app' in m]
    print(f"Knowledge app modules before: {core_modules_before}")
    
    # Import the training_metrics module
    import knowledge_app.core.training_metrics
    
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after training_metrics module: {after}")
    if newly_imported:
        print(f"⚠️ training_metrics module imported: {newly_imported}")
    
    # Check what knowledge_app modules were loaded
    core_modules_after = [m for m in sys.modules.keys() if 'knowledge_app' in m]
    new_core_modules = [m for m in core_modules_after if m not in core_modules_before]
    print(f"New knowledge_app modules: {new_core_modules}")
    
    # Step 5: Try importing the TrainingMetrics class specifically
    print("\n📦 Step 5: Import TrainingMetrics class")
    before = after
    
    from knowledge_app.core.training_metrics import TrainingMetrics
    
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after TrainingMetrics class: {after}")
    if newly_imported:
        print(f"⚠️ TrainingMetrics class imported: {newly_imported}")
    
    # Final check - what modules are now loaded?
    final_core_modules = [m for m in sys.modules.keys() if 'knowledge_app' in m]
    print(f"\nFinal knowledge_app modules loaded: {final_core_modules}")
    
    # Look for any torch-related modules
    torch_modules = [m for m in sys.modules.keys() if 'torch' in m.lower()]
    if torch_modules:
        print(f"\nTorch-related modules found: {torch_modules[:10]}...")  # Show first 10
    
    return newly_imported

def main():
    """Run the detailed test"""
    print("🚀 Starting training_metrics detailed import test...")
    print("=" * 80)
    
    culprits = test_step_by_step()
    
    print("\n" + "=" * 80)
    print("📊 DETAILED TEST RESULTS")
    print("=" * 80)
    
    if culprits:
        print(f"❌ Heavy modules imported: {culprits}")
        print("⚠️ Need to find the source of these imports")
        return 1
    else:
        print("✅ No heavy modules imported!")
        return 0

if __name__ == "__main__":
    sys.exit(main())
