#!/bin/bash
# Knowledge App Launcher for Linux/macOS
# This script sets up the environment properly before launching the application

echo "Starting Knowledge App..."

# Set CUDA environment variables BEFORE Python starts
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF="max_split_size_mb:256,expandable_segments:True"
export CUDA_LAUNCH_BLOCKING=0

# Set PyQt environment variables
export QT_AUTO_SCREEN_SCALE_FACTOR=1
export QT_ENABLE_HIGHDPI_SCALING=1

# Set Python environment variables
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
export PYTHONPATH="${SCRIPT_DIR}/src:${PYTHONPATH}"
export PYTHONUNBUFFERED=1

# Memory optimization
export PYTORCH_CUDA_ALLOC_CONF="max_split_size_mb:256"

# Launch the application
echo "Environment configured. Launching application..."
python main.py "$@"

# Check exit code
if [ $? -ne 0 ]; then
    echo ""
    echo "Application exited with error code $?"
    read -p "Press Enter to continue..."
fi
