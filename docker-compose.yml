version: '3.8'

services:
  knowledge-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: knowledge-app
    volumes:
      # Persistent storage for user data and models
      - ./data:/app/data
      - ./uploaded_books:/app/uploaded_books
      - ./models:/app/models
      - ./logs:/app/logs
    environment:
      # Environment configuration
      - KNOWLEDGE_APP_ENVIRONMENT=production
      - KNOWLEDGE_APP_LOGGING_LEVEL=INFO
      - KNOWLEDGE_APP_QT_HIGH_DPI_SCALING=true
      # Add other environment variables here
    restart: unless-stopped
    # Uncomment the following for GUI access on a local machine
    # environment:
    #   - DISPLAY=${DISPLAY}
    # volumes:
    #   - /tmp/.X11-unix:/tmp/.X11-unix
