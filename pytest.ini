[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Test configuration
addopts = 
    --verbose
    --capture=no
    --tb=short
    --showlocals
    --maxfail=1
    --strict-markers

# Custom markers for test categorization
markers =
    unit: Unit tests (fast, isolated)
    integration: Integration tests (slower, with dependencies)
    ui: UI tests (require display)
    slow: Slow tests (may take several minutes)
    enterprise: Enterprise refactoring tests
    mvc: Model-View-Controller tests
    styling: UI styling and theme tests
    ml: Machine learning related tests
    gpu: Tests requiring GPU
    cuda: Tests requiring CUDA
    network: Tests requiring network access

# Test discovery
norecursedirs = .* build dist CVS _darcs {arch} *.egg venv env virtualenv

# Qt configuration
qt_api = pyqt5

# Enterprise-grade warning suppression for clean test output
filterwarnings =
    ignore::DeprecationWarning
    ignore::UserWarning
    ignore::PendingDeprecationWarning
    ignore::FutureWarning
    ignore::ImportWarning
    ignore:sipPyTypeDict.*:DeprecationWarning
    ignore:.*sipPyTypeDict.*:DeprecationWarning
    ignore:sipPyTypeDict\(\) is deprecated.*:DeprecationWarning
    ignore:builtin type.*has no __module__ attribute:DeprecationWarning
    ignore:builtin type SwigPyPacked.*:DeprecationWarning
    ignore:builtin type SwigPyObject.*:DeprecationWarning
    ignore:builtin type swigvarlink.*:DeprecationWarning
    ignore:.*swigvarlink.*:DeprecationWarning
    ignore:.*SwigPyPacked.*:DeprecationWarning
    ignore:.*SwigPyObject.*:DeprecationWarning
    ignore:.*sip.*:DeprecationWarning
    ignore:.*PyQt5.*:DeprecationWarning
    ignore:.*Qt.*:DeprecationWarning