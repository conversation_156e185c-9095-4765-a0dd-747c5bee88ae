#!/usr/bin/env python3
"""
The Royal Inspector - Attention Backend Diagnostic Script
This script audits the environment and checks for available attention backends.
"""

import torch
import logging
import sys
import platform

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def check_environment():
    """The Audit - Check the battlefield conditions"""
    print("🏰 === THE ROYAL AUDIT - ENVIRONMENT INSPECTION ===")
    print(f"🖥️  Platform: {platform.system()} {platform.release()}")
    print(f"🐍 Python Version: {sys.version}")
    print(f"⚡ PyTorch Version: {torch.__version__}")
    
    if not torch.cuda.is_available():
        print("❌ CUDA is not available. Optimized backends will not work.")
        print("💡 Install CUDA-enabled PyTorch to proceed.")
        return False
    
    print(f"🔥 CUDA Version (used by PyTorch): {torch.version.cuda}")
    print(f"🎮 GPU: {torch.cuda.get_device_name(0)}")
    
    major, minor = torch.cuda.get_device_capability(0)
    compute_capability = f"{major}.{minor}"
    print(f"⚙️  GPU Compute Capability: {compute_capability}")
    
    if major < 8:
        print("⚠️  GPU Compute Capability is less than 8.0. Flash Attention 2 may not be supported.")
        print("💡 RTX 30xx/40xx series (Ampere/Ada Lovelace) recommended for Flash Attention 2.")
    else:
        print("✅ GPU supports Flash Attention 2 (Compute Capability >= 8.0)")
    
    # Check available GPU memory
    gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
    print(f"💾 GPU Memory: {gpu_memory:.1f} GB")
    
    return True

def check_backends():
    """Check availability of attention backends"""
    print("\n🗡️  === ATTENTION BACKEND AVAILABILITY ===")
    
    backends_available = []
    
    # Check xFormers
    try:
        import xformers
        import xformers.ops
        print(f"✅ xFormers is installed and accessible (Version: {xformers.__version__})")
        backends_available.append("xFormers")
        
        # Test if xFormers can actually be used
        try:
            # Simple test to see if xFormers operations work
            x = torch.randn(1, 8, 64, device='cuda', dtype=torch.float16)
            xformers.ops.memory_efficient_attention(x, x, x)
            print("✅ xFormers operations test: PASSED")
        except Exception as e:
            print(f"⚠️  xFormers operations test: FAILED ({e})")
            
    except ImportError:
        print("❌ xFormers is not installed or accessible.")
        print("💡 Install with: pip install xformers")

    # Check Flash Attention 2
    try:
        import flash_attn
        print(f"✅ Flash Attention 2 is installed and accessible (Version: {flash_attn.__version__})")
        backends_available.append("Flash Attention 2")
        
        # Test if Flash Attention can actually be used
        try:
            from flash_attn import flash_attn_func
            # Simple test
            q = torch.randn(1, 8, 64, device='cuda', dtype=torch.float16)
            k = torch.randn(1, 8, 64, device='cuda', dtype=torch.float16)
            v = torch.randn(1, 8, 64, device='cuda', dtype=torch.float16)
            flash_attn_func(q, k, v)
            print("✅ Flash Attention 2 operations test: PASSED")
        except Exception as e:
            print(f"⚠️  Flash Attention 2 operations test: FAILED ({e})")
            
    except ImportError:
        print("❌ Flash Attention 2 is not installed or accessible.")
        print("💡 Install with: pip install flash-attn --no-build-isolation")

    # Check PyTorch SDPA (always available in PyTorch 2.0+)
    pytorch_version = tuple(map(int, torch.__version__.split('.')[:2]))
    if pytorch_version >= (2, 0):
        print("✅ PyTorch SDPA (Scaled Dot-Product Attention) is available")
        backends_available.append("PyTorch SDPA")
        
        # Test SDPA
        try:
            import torch.nn.functional as F
            q = torch.randn(1, 8, 64, device='cuda', dtype=torch.float16)
            k = torch.randn(1, 8, 64, device='cuda', dtype=torch.float16)
            v = torch.randn(1, 8, 64, device='cuda', dtype=torch.float16)
            F.scaled_dot_product_attention(q, k, v)
            print("✅ PyTorch SDPA operations test: PASSED")
        except Exception as e:
            print(f"⚠️  PyTorch SDPA operations test: FAILED ({e})")
    else:
        print("❌ PyTorch SDPA requires PyTorch 2.0+")
        print(f"💡 Current version: {torch.__version__}")

    return backends_available

def test_transformers_attention():
    """Test transformers library attention implementation detection"""
    print("\n🤖 === TRANSFORMERS ATTENTION IMPLEMENTATION TEST ===")
    
    try:
        from transformers import AutoConfig
        
        # Test with a small model config
        config = AutoConfig.from_pretrained("microsoft/DialoGPT-small")
        
        # Test different attention implementations
        implementations = ["flash_attention_2", "sdpa", "eager"]
        
        for impl in implementations:
            try:
                print(f"🧪 Testing attn_implementation='{impl}'...")
                config._attn_implementation = impl
                print(f"✅ '{impl}' is supported by transformers")
            except Exception as e:
                print(f"❌ '{impl}' failed: {e}")
                
    except Exception as e:
        print(f"❌ Transformers test failed: {e}")

def main():
    """Main diagnostic function"""
    print("👑 THE SWIFT BLADE OF ATTENTION - ROYAL DIAGNOSTIC")
    print("=" * 60)
    
    if not check_environment():
        print("\n💥 CRITICAL: CUDA not available. Cannot proceed with optimized attention.")
        return 1
    
    backends = check_backends()
    test_transformers_attention()
    
    print("\n🏆 === ROYAL RECOMMENDATION ===")
    
    if "Flash Attention 2" in backends:
        print("👑 RECOMMENDED: Use Flash Attention 2 (attn_implementation='flash_attention_2')")
        print("   - Fastest available backend")
        print("   - Significant memory savings")
    elif "xFormers" in backends:
        print("👑 RECOMMENDED: Use xFormers (auto-detected when available)")
        print("   - Excellent performance on Windows")
        print("   - Good memory efficiency")
    elif "PyTorch SDPA" in backends:
        print("👑 RECOMMENDED: Use PyTorch SDPA (attn_implementation='sdpa')")
        print("   - Built into PyTorch 2.0+")
        print("   - Reliable fallback option")
    else:
        print("⚠️  FALLBACK: Standard eager attention only")
        print("   - Consider installing xFormers or Flash Attention 2")
    
    print("\n🗡️  The Royal Audit is complete. Proceed with implementation!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
