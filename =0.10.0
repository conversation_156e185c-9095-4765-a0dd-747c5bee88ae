Collecting torch
  Downloading torch-2.7.0-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (29 kB)
Collecting transformers
  Downloading transformers-4.52.4-py3-none-any.whl.metadata (38 kB)
Collecting peft
  Downloading peft-0.15.2-py3-none-any.whl.metadata (13 kB)
Requirement already satisfied: filelock in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (3.18.0)
Requirement already satisfied: typing-extensions>=4.10.0 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (4.14.0)
Requirement already satisfied: sympy>=1.13.3 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (1.14.0)
Requirement already satisfied: networkx in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (3.5)
Requirement already satisfied: jinja2 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (3.1.6)
Requirement already satisfied: fsspec in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (2025.3.0)
Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.6.77 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (12.6.77)
Requirement already satisfied: nvidia-cuda-runtime-cu12==12.6.77 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (12.6.77)
Requirement already satisfied: nvidia-cuda-cupti-cu12==12.6.80 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (12.6.80)
Requirement already satisfied: nvidia-cudnn-cu12==9.5.1.17 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (9.5.1.17)
Requirement already satisfied: nvidia-cublas-cu12==12.6.4.1 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (12.6.4.1)
Requirement already satisfied: nvidia-cufft-cu12==11.3.0.4 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (11.3.0.4)
Requirement already satisfied: nvidia-curand-cu12==10.3.7.77 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (10.3.7.77)
Requirement already satisfied: nvidia-cusolver-cu12==11.7.1.2 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (11.7.1.2)
Requirement already satisfied: nvidia-cusparse-cu12==12.5.4.2 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (12.5.4.2)
Requirement already satisfied: nvidia-cusparselt-cu12==0.6.3 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (0.6.3)
Requirement already satisfied: nvidia-nccl-cu12==2.26.2 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (2.26.2)
Requirement already satisfied: nvidia-nvtx-cu12==12.6.77 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (12.6.77)
Requirement already satisfied: nvidia-nvjitlink-cu12==12.6.85 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (12.6.85)
Requirement already satisfied: nvidia-cufile-cu12==1.11.1.6 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (1.11.1.6)
Requirement already satisfied: triton==3.3.0 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from torch) (3.3.0)
Requirement already satisfied: setuptools>=40.8.0 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from triton==3.3.0->torch) (80.9.0)
Requirement already satisfied: huggingface-hub<1.0,>=0.30.0 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from transformers) (0.32.4)
Requirement already satisfied: numpy>=1.17 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from transformers) (2.2.6)
Requirement already satisfied: packaging>=20.0 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from transformers) (25.0)
Requirement already satisfied: pyyaml>=5.1 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from transformers) (6.0.2)
Requirement already satisfied: regex!=2019.12.17 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from transformers) (2024.11.6)
Requirement already satisfied: requests in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from transformers) (2.32.3)
Collecting tokenizers<0.22,>=0.21 (from transformers)
  Downloading tokenizers-0.21.1-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.8 kB)
Requirement already satisfied: safetensors>=0.4.3 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from transformers) (0.5.3)
Requirement already satisfied: tqdm>=4.27 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from transformers) (4.67.1)
Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (1.1.2)
Requirement already satisfied: psutil in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from peft) (7.0.0)
Requirement already satisfied: accelerate>=0.21.0 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from peft) (1.7.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from sympy>=1.13.3->torch) (1.3.0)
Requirement already satisfied: MarkupSafe>=2.0 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from jinja2->torch) (3.0.2)
Requirement already satisfied: charset-normalizer<4,>=2 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from requests->transformers) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from requests->transformers) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from requests->transformers) (2.4.0)
Requirement already satisfied: certifi>=2017.4.17 in /home/<USER>/llm-env-py311/lib/python3.11/site-packages (from requests->transformers) (2025.4.26)
Downloading torch-2.7.0-cp311-cp311-manylinux_2_28_x86_64.whl (865.2 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 865.2/865.2 MB 4.0 MB/s eta 0:00:00
Downloading transformers-4.52.4-py3-none-any.whl (10.5 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 10.5/10.5 MB 4.3 MB/s eta 0:00:00
Downloading tokenizers-0.21.1-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 3.0/3.0 MB 4.2 MB/s eta 0:00:00
Downloading peft-0.15.2-py3-none-any.whl (411 kB)
Installing collected packages: torch, tokenizers, transformers, peft
  Attempting uninstall: tokenizers
    Found existing installation: tokenizers 0.15.2
    Uninstalling tokenizers-0.15.2:
      Successfully uninstalled tokenizers-0.15.2

Successfully installed peft-0.15.2 tokenizers-0.21.1 torch-2.7.0 transformers-4.52.4
