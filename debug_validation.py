#!/usr/bin/env python3
"""
Debug Validation Issues

This script debugs exactly why the real 7B training validation is failing
and falling back to simulation mode.
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def debug_real_7b_validation():
    """Debug the Real7B validation process step by step"""
    print("🔍 Debugging Real 7B Training Validation")
    print("=" * 50)
    
    try:
        # Import the real 7B config manager
        from src.knowledge_app.core.real_7b_config import get_real_7b_config
        
        print("✅ Successfully imported Real7BConfigManager")
        
        # Get the config manager
        config_manager = get_real_7b_config()
        print("✅ Created config manager instance")
        
        # Update model selection to Mistral
        print("\n🔥 Testing Mistral model selection...")
        config_manager.update_model_selection("mistral-7b")
        print("✅ Updated model selection to mistral-7b")
        
        # Get the current config
        config = config_manager.config
        print(f"\n📋 Current configuration:")
        print(f"  Selected model: {config['model']['selected_model']}")
        print(f"  Training data path: {config['data']['train_data_path']}")
        print(f"  Output directory: {config['training']['output_dir']}")
        
        # Check if training data path exists
        train_data_path = config['data']['train_data_path']
        print(f"\n🔍 Checking training data path: {train_data_path}")
        print(f"  Absolute path: {os.path.abspath(train_data_path)}")
        print(f"  Current working directory: {os.getcwd()}")
        print(f"  Path exists: {os.path.exists(train_data_path)}")
        
        if os.path.exists(train_data_path):
            size = os.path.getsize(train_data_path)
            print(f"  File size: {size} bytes")
            
            # Read first few lines
            try:
                with open(train_data_path, 'r', encoding='utf-8') as f:
                    first_lines = f.read(500)
                print(f"  First 500 characters: {repr(first_lines[:100])}...")
            except Exception as e:
                print(f"  Error reading file: {e}")
        else:
            print("  ❌ File does not exist!")
            
            # Check alternative paths
            alternative_paths = [
                os.path.join(os.getcwd(), train_data_path),
                os.path.join(project_root, train_data_path),
                "lora_adapters_mistral/default/training_data_augmented_default.txt"
            ]
            
            print("  🔍 Checking alternative paths:")
            for alt_path in alternative_paths:
                exists = os.path.exists(alt_path)
                print(f"    {alt_path}: {'✅' if exists else '❌'}")
        
        # Run the actual validation
        print(f"\n🧪 Running validation...")
        issues = config_manager.validate_config()
        
        if issues:
            print(f"❌ Validation failed with {len(issues)} issues:")
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. {issue}")
        else:
            print("✅ Validation passed!")
            
        # Test hardware optimization
        print(f"\n⚙️ Testing hardware optimization...")
        optimized_config = config_manager.get_optimized_config_for_hardware()
        print(f"  Batch size: {optimized_config['training']['per_device_train_batch_size']}")
        print(f"  Gradient accumulation: {optimized_config['training']['gradient_accumulation_steps']}")
        print(f"  Use QLoRA: {optimized_config['quantization']['use_qlora']}")
        print(f"  Use 4-bit: {optimized_config['quantization']['use_4bit']}")
        
        # Test model config
        print(f"\n🤖 Testing model configuration...")
        model_config = config_manager.get_model_config("mistral-7b")
        print(f"  Model name: {model_config.name}")
        print(f"  Model ID: {model_config.model_id}")
        print(f"  Context length: {model_config.context_length}")
        print(f"  LoRA targets: {model_config.lora_target_modules}")
        
        return len(issues) == 0
        
    except Exception as e:
        print(f"❌ Error during validation debug: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_training_dialog_flow():
    """Debug the training dialog flow"""
    print("\n🎭 Debugging Training Dialog Flow")
    print("=" * 50)
    
    try:
        # Test the training dialog logic
        from src.knowledge_app.core.real_7b_config import get_real_7b_config
        from src.knowledge_app.core.real_7b_trainer import Real7BConfig
        
        config_manager = get_real_7b_config()
        config_manager.update_model_selection("mistral-7b")
        
        # Simulate the exact flow from select_real_7b_training
        print("🔥 Simulating select_real_7b_training flow...")
        
        # Step 1: Validate configuration
        issues = config_manager.validate_config()
        print(f"Step 1 - Validation: {'✅ PASS' if not issues else '❌ FAIL'}")
        if issues:
            print(f"  Issues: {issues}")
            return False
            
        # Step 2: Get optimized config
        config_dict = config_manager.get_optimized_config_for_hardware()
        model_config = config_manager.get_model_config("mistral-7b")
        print("Step 2 - Config generation: ✅ PASS")
        
        # Step 3: Create Real7BConfig
        real_config = Real7BConfig(
            model_name=model_config.model_id,
            use_qlora=config_dict["quantization"]["use_qlora"],
            use_4bit=config_dict["quantization"]["use_4bit"],
            bnb_4bit_compute_dtype=config_dict["quantization"]["bnb_4bit_compute_dtype"],
            bnb_4bit_quant_type=config_dict["quantization"]["bnb_4bit_quant_type"],
            use_nested_quant=config_dict["quantization"]["use_nested_quant"],
            lora_r=config_dict["lora"]["r"],
            lora_alpha=config_dict["lora"]["alpha"],
            lora_dropout=config_dict["lora"]["dropout"],
            lora_target_modules=config_dict["lora"]["target_modules"],
            output_dir=config_dict["training"]["output_dir"],
            num_train_epochs=config_dict["training"]["num_train_epochs"],
        )
        print("Step 3 - Real7BConfig creation: ✅ PASS")
        print(f"  Model: {real_config.model_name}")
        print(f"  QLoRA: {real_config.use_qlora}")
        print(f"  Output: {real_config.output_dir}")
        
        # Step 4: Check training data path
        training_data_path = config_dict["data"]["train_data_path"]
        print(f"Step 4 - Training data: {training_data_path}")
        if os.path.exists(training_data_path):
            print("  ✅ Training data exists")
        else:
            print("  ❌ Training data missing")
            return False
            
        print("🎉 All steps would succeed - Real7BTrainer should start!")
        return True
        
    except Exception as e:
        print(f"❌ Error in training dialog flow: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debugging function"""
    print("🐛 Real 7B Training Validation Debugger")
    print("This will help identify why training falls back to simulation")
    print()
    
    # Debug validation
    validation_ok = debug_real_7b_validation()
    
    # Debug training flow
    flow_ok = debug_training_dialog_flow()
    
    print("\n" + "=" * 50)
    print("🏁 SUMMARY:")
    print(f"  Validation: {'✅ PASS' if validation_ok else '❌ FAIL'}")
    print(f"  Training Flow: {'✅ PASS' if flow_ok else '❌ FAIL'}")
    
    if validation_ok and flow_ok:
        print("\n🎉 Everything should work! The issue might be elsewhere.")
        print("💡 Possible causes:")
        print("  - Error handling is catching exceptions silently")
        print("  - Training dialog is using wrong code path")
        print("  - Real7BTrainer is not being instantiated")
    else:
        print("\n🔧 Issues found that need fixing!")
        print("💡 Fix the validation issues above and try again.")

if __name__ == "__main__":
    main()
