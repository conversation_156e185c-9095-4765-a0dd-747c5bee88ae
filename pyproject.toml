[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "knowledge_app"
version = "0.1.0"
authors = [
    { name="Your Name", email="<EMAIL>" },
]
description = "An AI-powered knowledge quiz application"
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Education",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
]
keywords = ["quiz", "education", "ai", "machine-learning"]

# Core dependencies (always installed)
dependencies = [
    "PyQt5>=5.15.9",
    "PyQt5-Qt5>=5.15.2",
    "PyQt5-sip>=12.11.0",
    "numpy>=1.21.0",
    "pandas>=1.3.0",
    "requests>=2.25.0",
    "Pillow>=8.0.0",
    "matplotlib>=3.3.0",
    "scikit-learn>=1.0.0",
    "nltk>=3.6",
    "transformers>=4.20.0",
    "datasets>=2.0.0",
    "accelerate>=0.20.0",
    "peft>=0.4.0",
    "bitsandbytes>=0.39.0",
    "sentencepiece>=0.1.97",
    "protobuf>=3.20.0",
    "tqdm>=4.64.0",
    "psutil>=5.8.0",
    "certifi>=2021.10.8",
]

[project.optional-dependencies]
# CUDA support for NVIDIA GPUs
cuda = [
    "torch>=2.0.0+cu118",
    "torchvision>=0.15.0+cu118",
    "torchaudio>=2.0.0+cu118",
    "xformers>=0.0.20",
]

# CPU-only support
cpu = [
    "torch>=2.0.0+cpu",
    "torchvision>=0.15.0+cpu",
    "torchaudio>=2.0.0+cpu",
]

# macOS Metal support
metal = [
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    "torchaudio>=2.0.0",
]

# Development dependencies
dev = [
    "pytest>=7.0.0",
    "pytest-qt>=4.0.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=0.991",
    "pre-commit>=2.20.0",
]

# Testing dependencies
test = [
    "pytest>=7.0.0",
    "pytest-qt>=4.0.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.8.0",
    "pytest-timeout>=2.1.0",
]

[project.urls]
"Homepage" = "https://github.com/yourusername/knowledge_app"
"Bug Tracker" = "https://github.com/yourusername/knowledge_app/issues"

[tool.setuptools]
packages = ["knowledge_app"]
package-dir = {"" = "src"}

[tool.black]
line-length = 100
target-version = ['py38']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 100

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
    "--cov=src/knowledge_app",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-fail-under=80",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "ui: marks tests as UI tests (require display)",
    "gpu: marks tests as requiring GPU",
    "training: marks tests as training-related",
    "model: marks tests as model-related",
    "network: marks tests requiring network access",
]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
    "ignore::UserWarning:transformers.*",
]

[tool.coverage.run]
source = ["knowledge_app"]
omit = ["tests/*", "setup.py"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "pass",
    "raise ImportError",
]

[tool.bandit]
exclude_dirs = ["tests"]
skips = ["B101"] 