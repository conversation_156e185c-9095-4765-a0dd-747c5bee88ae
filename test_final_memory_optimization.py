#!/usr/bin/env python3
"""
Final Memory Optimization Verification

This script provides a comprehensive test of the memory optimization implementation,
verifying that the application now has fast startup with minimal memory usage.
"""

import sys
import os
import psutil
import time

# Add the src directory to path for local imports
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

def get_memory_usage():
    """Get current memory usage in MB"""
    process = psutil.Process()
    return process.memory_info().rss / (1024 * 1024)

def test_final_optimization():
    """Test the final memory optimization results"""
    
    heavy_modules = ['torch', 'transformers', 'peft', 'datasets']
    
    print("🎯 FINAL MEMORY OPTIMIZATION VERIFICATION")
    print("=" * 80)
    print("Testing the aggressive lazy loading implementation...")
    print()
    
    # Step 1: Baseline measurement
    start_time = time.time()
    initial_memory = get_memory_usage()
    print(f"📊 Baseline memory usage: {initial_memory:.1f} MB")
    
    # Step 2: Test core application startup
    print("\n🚀 Testing core application startup...")
    
    # Import essential components
    from knowledge_app.core.config_manager import ConfigManager
    config_memory = get_memory_usage()
    print(f"   📦 Config manager: {config_memory:.1f} MB (+{config_memory - initial_memory:.1f} MB)")
    
    from knowledge_app.ui.enterprise_design_system import get_design_system
    design_memory = get_memory_usage()
    print(f"   🎨 Design system: {design_memory:.1f} MB (+{design_memory - config_memory:.1f} MB)")
    
    from knowledge_app.ui.enterprise_style_manager import get_style_manager
    style_memory = get_memory_usage()
    print(f"   🎨 Style manager: {style_memory:.1f} MB (+{style_memory - design_memory:.1f} MB)")
    
    from knowledge_app.ui.mvc.main_window_mvc import MainWindowModel, MainWindowView, MainWindowController
    mvc_memory = get_memory_usage()
    print(f"   🏗️ MVC components: {mvc_memory:.1f} MB (+{mvc_memory - style_memory:.1f} MB)")
    
    from knowledge_app.ui.enterprise_main_window import create_enterprise_main_window
    final_memory = get_memory_usage()
    print(f"   🏢 Enterprise window: {final_memory:.1f} MB (+{final_memory - mvc_memory:.1f} MB)")
    
    startup_time = time.time() - start_time
    
    # Step 3: Check heavy modules
    print(f"\n📦 Heavy module import status:")
    heavy_imported = [m for m in heavy_modules if m in sys.modules]
    heavy_not_imported = [m for m in heavy_modules if m not in sys.modules]
    
    for module in heavy_not_imported:
        print(f"   ✅ {module}: Not imported (good)")
    
    for module in heavy_imported:
        print(f"   ⚠️ {module}: Imported during startup (unexpected)")
    
    # Step 4: Test lazy loading functionality
    print(f"\n🔄 Testing lazy loading functionality...")
    
    # Test that we can import the trainer class without triggering heavy imports
    lazy_start_memory = get_memory_usage()
    from knowledge_app.core.real_7b_trainer import Real7BTrainer
    lazy_end_memory = get_memory_usage()
    lazy_overhead = lazy_end_memory - lazy_start_memory
    
    print(f"   📊 Trainer class import: {lazy_end_memory:.1f} MB (+{lazy_overhead:.1f} MB)")
    
    # Check if heavy modules are still not imported
    heavy_after_lazy = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in heavy_after_lazy if m not in heavy_imported]
    
    if newly_imported:
        print(f"   ⚠️ Heavy modules imported during lazy test: {newly_imported}")
    else:
        print(f"   ✅ No heavy modules imported during lazy test")
    
    # Step 5: Calculate results
    total_overhead = final_memory - initial_memory
    
    print(f"\n📊 PERFORMANCE METRICS")
    print("=" * 50)
    print(f"Startup time: {startup_time:.2f} seconds")
    print(f"Memory overhead: {total_overhead:.1f} MB")
    print(f"Final memory usage: {final_memory:.1f} MB")
    print(f"Heavy modules deferred: {len(heavy_not_imported)}/{len(heavy_modules)}")
    
    # Step 6: Evaluate success criteria
    print(f"\n🎯 SUCCESS CRITERIA EVALUATION")
    print("=" * 50)
    
    success_count = 0
    total_criteria = 5
    
    # Criterion 1: Fast startup
    if startup_time < 2.0:
        print(f"✅ Fast startup: {startup_time:.2f}s < 2.0s")
        success_count += 1
    else:
        print(f"❌ Slow startup: {startup_time:.2f}s >= 2.0s")
    
    # Criterion 2: Low memory usage
    if final_memory < 100:
        print(f"✅ Low memory usage: {final_memory:.1f} MB < 100 MB")
        success_count += 1
    else:
        print(f"❌ High memory usage: {final_memory:.1f} MB >= 100 MB")
    
    # Criterion 3: Minimal overhead
    if total_overhead < 50:
        print(f"✅ Minimal overhead: {total_overhead:.1f} MB < 50 MB")
        success_count += 1
    else:
        print(f"❌ High overhead: {total_overhead:.1f} MB >= 50 MB")
    
    # Criterion 4: Heavy modules deferred
    if len(heavy_not_imported) >= 3:
        print(f"✅ Heavy modules deferred: {len(heavy_not_imported)}/4 modules")
        success_count += 1
    else:
        print(f"❌ Heavy modules not deferred: {len(heavy_not_imported)}/4 modules")
    
    # Criterion 5: Lazy loading works
    if lazy_overhead < 10:
        print(f"✅ Lazy loading efficient: {lazy_overhead:.1f} MB < 10 MB")
        success_count += 1
    else:
        print(f"❌ Lazy loading inefficient: {lazy_overhead:.1f} MB >= 10 MB")
    
    # Final verdict
    success_rate = (success_count / total_criteria) * 100
    
    print(f"\n🏆 FINAL RESULTS")
    print("=" * 50)
    print(f"Success rate: {success_count}/{total_criteria} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("🎉 MEMORY OPTIMIZATION: EXCELLENT SUCCESS!")
        print("The application now has professional-grade startup performance.")
        return 0
    elif success_rate >= 60:
        print("✅ MEMORY OPTIMIZATION: GOOD SUCCESS!")
        print("The application startup is significantly improved.")
        return 0
    else:
        print("⚠️ MEMORY OPTIMIZATION: NEEDS IMPROVEMENT")
        print("Some optimization goals were not met.")
        return 1

def main():
    """Run the final optimization test"""
    return test_final_optimization()

if __name__ == "__main__":
    sys.exit(main())
