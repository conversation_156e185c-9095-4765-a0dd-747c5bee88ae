
#!/usr/bin/env python3
"""
Python Code Collector for Knowledge App

This script collects ALL pure Python source code from the project and dumps it
into a single massive .txt file. Excludes:
- Test files, cache, logs, and other waste
- Virtual environments and build artifacts
- But includes ALL the actual Python source code

The output is pure Python code that can be copy-pasted and analyzed.
"""

import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Set
from datetime import datetime

class PythonCodeCollector:
    """Collects all Python source code into one massive file"""

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.total_files = 0
        self.total_lines = 0
        self.collected_code = []

        # Directories to exclude (waste)
        self.exclude_dirs = {
            '__pycache__', '.pytest_cache', 'cache', 'logs', 'data',
            '.git', '.vscode', '.idea', 'node_modules', 'venv', '.venv',
            'env', '.env', 'build', 'dist', '*.egg-info', 'htmlcov',
            '.coverage', '.mypy_cache', '.tox', 'migrations', 'wheels',
            'automation_screenshots', 'generated_images', 'uploaded_books',
            'user_data', 'quiz_storage', 'models', 'lora_adapters_mistral',
            'nltk_data_fixed', 'seducedevata'
        }

        # Files to exclude (waste)
        self.exclude_files = {
            'setup.py', 'setup.cfg', 'pyproject.toml', 'requirements.txt',
            '.gitignore', '.gitattributes', 'README.md', 'LICENSE',
            'Dockerfile', 'docker-compose.yml', '.dockerignore',
            'Makefile', 'tox.ini', 'pytest.ini', '.coveragerc',
            'rustup-init.exe', 'python311_installer.exe'
        }

        # Test file patterns to exclude
        self.test_patterns = {
            r'^test_.*\.py$',
            r'.*_test\.py$',
            r'^tests?\.py$',
            r'.*conftest\.py$',
            r'.*debug.*\.py$',
            r'.*validate.*\.py$'
        }

    def is_excluded_dir(self, dir_path: Path) -> bool:
        """Check if directory should be excluded"""
        dir_name = dir_path.name.lower()
        return any(
            dir_name == exclude or
            dir_name.startswith(exclude.rstrip('*'))
            for exclude in self.exclude_dirs
        )

    def is_excluded_file(self, file_path: Path) -> bool:
        """Check if file should be excluded"""
        file_name = file_path.name.lower()

        # Check exact matches
        if file_name in self.exclude_files:
            return True

        # Check test patterns
        for pattern in self.test_patterns:
            if re.match(pattern, file_name):
                return True

        # Only count .py files
        if not file_name.endswith('.py'):
            return True

        return False

    def collect_file_content(self, file_path: Path) -> str:
        """
        Collect the full content of a Python file.
        Returns the raw Python source code.
        """
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            return content
        except Exception as e:
            print(f"⚠️ Error reading {file_path}: {e}")
            return ""

    def scan_directory(self, directory: Path) -> None:
        """Recursively scan directory for Python files"""
        try:
            for item in directory.iterdir():
                if item.is_dir():
                    if not self.is_excluded_dir(item):
                        self.scan_directory(item)
                elif item.is_file():
                    if not self.is_excluded_file(item):
                        self.process_file(item)
        except PermissionError:
            print(f"⚠️ Permission denied: {directory}")
        except Exception as e:
            print(f"⚠️ Error scanning {directory}: {e}")

    def process_file(self, file_path: Path) -> None:
        """Process a single Python file and collect its content"""
        content = self.collect_file_content(file_path)

        if content.strip():  # Only collect non-empty files
            self.total_files += 1
            self.total_lines += len(content.splitlines())

            # Get relative path for header
            rel_path = file_path.relative_to(self.project_root)

            # Add file header and content to collection
            file_header = f"\n{'='*80}\n# FILE: {rel_path}\n{'='*80}\n\n"
            self.collected_code.append(file_header + content)

    def generate_code_dump(self) -> str:
        """Generate the massive Python code dump"""
        header = f"""{'='*80}
🐍 KNOWLEDGE APP - COMPLETE PYTHON SOURCE CODE DUMP
{'='*80}
📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📁 Project Root: {self.project_root}
📄 Total Files: {self.total_files:,}
📝 Total Lines: {self.total_lines:,}

🚫 EXCLUDED:
• Test files (test_*.py, *_test.py, conftest.py)
• Cache/build directories (__pycache__, .pytest_cache, etc.)
• Virtual environments (venv, .venv, env)
• Configuration files (setup.py, requirements.txt, etc.)
• Debug/validation scripts
• Data and asset directories

{'='*80}
🎯 PURE PYTHON SOURCE CODE BELOW
{'='*80}

"""

        # Combine header with all collected code
        return header + "\n".join(self.collected_code)

    def run(self) -> str:
        """Run the code collection"""
        print("🔍 Scanning for Python files...")
        self.scan_directory(self.project_root)

        print(f"✅ Collection complete!")
        print(f"📄 Files collected: {self.total_files:,}")
        print(f"📝 Total lines: {self.total_lines:,}")

        return self.generate_code_dump()

def main():
    """Main function"""
    # Get project root (current directory)
    project_root = os.getcwd()

    print("🐍 Knowledge App Python Code Collector")
    print("=" * 60)
    print(f"📁 Project: {project_root}")
    print("🎯 Collecting ALL Python source code into one massive file...")
    print()

    # Run collection
    collector = PythonCodeCollector(project_root)
    code_dump = collector.run()

    # Save to current directory first
    output_file = Path("knowledge_app_complete_python_code.txt")

    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(code_dump)
        print(f"💾 Complete Python code saved to: {output_file.absolute()}")
        print(f"📊 File size: {len(code_dump):,} characters")
    except Exception as e:
        print(f"❌ Error saving code dump: {e}")

    # Also try to save to shared folder
    try:
        shared_folder = Path("C:/shared folder")
        if not shared_folder.exists():
            shared_folder.mkdir(parents=True, exist_ok=True)

        shared_file = shared_folder / "knowledge_app_complete_python_code.txt"
        with open(shared_file, 'w', encoding='utf-8') as f:
            f.write(code_dump)
        print(f"💾 Code also saved to: {shared_file}")
    except Exception as e:
        print(f"⚠️ Could not save to shared folder: {e}")

    # Print summary
    print("\n" + "=" * 60)
    print("📊 COLLECTION SUMMARY")
    print("=" * 60)
    print(f"📄 Python Files Collected: {collector.total_files:,}")
    print(f"📝 Total Lines: {collector.total_lines:,}")
    print(f"💾 Output File Size: {len(code_dump):,} characters")
    print(f"🎯 Raw Python Code: READY FOR ANALYSIS!")
    print("=" * 60)

if __name__ == "__main__":
    main()
