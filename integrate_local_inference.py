#!/usr/bin/env python3
"""
Integration script to add local inference capabilities to the main application.

This script modifies the main UI to include local model management and
switches the question generation system to use local models by default.
"""

import os
import sys
import logging
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def add_local_model_tab_to_main_ui():
    """Add local model management tab to the main UI"""
    print("🔧 Adding Local Model Management to Main UI...")
    
    try:
        # Find the main UI file
        main_ui_files = [
            "src/knowledge_app/ui/main_window.py",
            "src/knowledge_app/ui/main_ui.py", 
            "src/knowledge_app/main.py",
            "src/main.py"
        ]
        
        main_ui_file = None
        for file_path in main_ui_files:
            if os.path.exists(file_path):
                main_ui_file = file_path
                break
        
        if not main_ui_file:
            print("⚠️ Main UI file not found. You'll need to manually integrate the LocalModelManager.")
            print("💡 Add this import to your main UI file:")
            print("   from knowledge_app.ui.local_model_manager import LocalModelManager")
            print("💡 Then add the widget to your main interface:")
            print("   local_model_tab = LocalModelManager()")
            print("   your_tab_widget.addTab(local_model_tab, '🧠 Local Models')")
            return False
        
        print(f"✅ Found main UI file: {main_ui_file}")
        
        # Read the file
        with open(main_ui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if already integrated
        if "LocalModelManager" in content:
            print("✅ Local model manager already integrated!")
            return True
        
        # Add import
        if "from knowledge_app.ui.local_model_manager import LocalModelManager" not in content:
            # Find a good place to add the import
            import_lines = []
            other_lines = []
            in_imports = True
            
            for line in content.split('\n'):
                if line.strip().startswith('from ') or line.strip().startswith('import '):
                    import_lines.append(line)
                elif line.strip() == '':
                    if in_imports:
                        import_lines.append(line)
                    else:
                        other_lines.append(line)
                else:
                    in_imports = False
                    other_lines.append(line)
            
            # Add our import
            import_lines.append("from knowledge_app.ui.local_model_manager import LocalModelManager")
            
            # Reconstruct content
            content = '\n'.join(import_lines + other_lines)
            print("✅ Added LocalModelManager import")
        
        # Save the modified file
        backup_file = main_ui_file + ".backup"
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Created backup: {backup_file}")
        
        with open(main_ui_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Main UI file updated successfully!")
        print("💡 You'll need to manually add the LocalModelManager widget to your tab widget")
        print("💡 Example code:")
        print("   self.local_model_tab = LocalModelManager()")
        print("   self.tab_widget.addTab(self.local_model_tab, '🧠 Local Models')")
        
        return True
        
    except Exception as e:
        print(f"❌ Error integrating with main UI: {e}")
        return False

def update_config_for_local_inference():
    """Update application config to enable local inference by default"""
    print("\n⚙️ Updating Configuration for Local Inference...")
    
    try:
        config_files = [
            "src/knowledge_app/config.py",
            "src/config.py",
            "config.py"
        ]
        
        config_file = None
        for file_path in config_files:
            if os.path.exists(file_path):
                config_file = file_path
                break
        
        if not config_file:
            print("⚠️ Config file not found. Creating default configuration...")
            
            # Create a basic config file
            config_content = '''"""
Application Configuration with Local Inference Support
"""

import os
from dataclasses import dataclass
from typing import Optional

@dataclass
class AppConfig:
    """Application configuration"""
    
    # Local inference settings
    use_local_inference: bool = True
    local_model_path: Optional[str] = None
    local_base_model: Optional[str] = "microsoft/DialoGPT-medium"
    
    # Cloud inference settings (fallback)
    cloud_inference_api_key: Optional[str] = None
    
    # Generation parameters
    max_new_tokens: int = 512
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 50
    
    def __post_init__(self):
        """Initialize configuration from environment variables"""
        # Override with environment variables if available
        self.cloud_inference_api_key = os.environ.get('GROQ_API_KEY', self.cloud_inference_api_key)
        self.local_model_path = os.environ.get('LOCAL_MODEL_PATH', self.local_model_path)
        self.local_base_model = os.environ.get('LOCAL_BASE_MODEL', self.local_base_model)

# Global config instance
app_config = AppConfig()
'''
            
            with open("src/knowledge_app/config.py", 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            print("✅ Created default configuration with local inference enabled")
            return True
        
        # Read existing config
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if already has local inference settings
        if "use_local_inference" in content:
            print("✅ Configuration already has local inference settings!")
            return True
        
        # Add local inference settings to AppConfig class
        if "class AppConfig" in content:
            # Find the class definition and add our settings
            lines = content.split('\n')
            new_lines = []
            in_appconfig = False
            added_settings = False
            
            for line in lines:
                new_lines.append(line)
                
                if "class AppConfig" in line:
                    in_appconfig = True
                elif in_appconfig and line.strip().startswith('def ') and not added_settings:
                    # Add our settings before the first method
                    new_lines.insert(-1, "")
                    new_lines.insert(-1, "    # Local inference settings")
                    new_lines.insert(-1, "    use_local_inference: bool = True")
                    new_lines.insert(-1, "    local_model_path: Optional[str] = None")
                    new_lines.insert(-1, "    local_base_model: Optional[str] = 'microsoft/DialoGPT-medium'")
                    new_lines.insert(-1, "")
                    added_settings = True
            
            if not added_settings and in_appconfig:
                # Add at the end of the class
                new_lines.append("")
                new_lines.append("    # Local inference settings")
                new_lines.append("    use_local_inference: bool = True")
                new_lines.append("    local_model_path: Optional[str] = None")
                new_lines.append("    local_base_model: Optional[str] = 'microsoft/DialoGPT-medium'")
            
            # Save backup and update
            backup_file = config_file + ".backup"
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(new_lines))
            
            print("✅ Updated configuration with local inference settings")
            print(f"✅ Created backup: {backup_file}")
            
        else:
            print("⚠️ Could not find AppConfig class. Manual configuration needed.")
            print("💡 Add these settings to your AppConfig class:")
            print("   use_local_inference: bool = True")
            print("   local_model_path: Optional[str] = None")
            print("   local_base_model: Optional[str] = 'microsoft/DialoGPT-medium'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error updating configuration: {e}")
        return False

def create_local_inference_demo():
    """Create a demo script showing how to use local inference"""
    print("\n📝 Creating Local Inference Demo...")
    
    demo_content = '''#!/usr/bin/env python3
"""
Local Inference Demo

This script demonstrates how to use the local inference system
to generate MCQ questions using your fine-tuned models.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def main():
    """Demo the local inference system"""
    print("🧠 Local Inference Demo")
    print("=" * 50)
    
    try:
        # Import local question generator
        from knowledge_app.core.local_question_generator import get_local_question_generator, initialize_local_generator
        
        # Initialize the system
        print("🔄 Initializing local inference...")
        success = initialize_local_generator()
        
        if success:
            print("✅ Local inference initialized!")
        else:
            print("⚠️ Using base model (no fine-tuned models found)")
        
        # Get the generator
        generator = get_local_question_generator()
        
        # Sample content for question generation
        sample_content = """
        Machine learning is a subset of artificial intelligence that enables computers 
        to learn and improve from experience without being explicitly programmed. 
        It focuses on the development of computer programs that can access data and 
        use it to learn for themselves.
        """
        
        print("\\n📚 Sample Content:")
        print(sample_content.strip())
        
        # Generate questions
        print("\\n🧠 Generating MCQ questions...")
        questions = await generator.generate_questions(
            content=sample_content,
            count=2,
            difficulty="medium"
        )
        
        # Display results
        print(f"\\n✅ Generated {len(questions)} questions:")
        print("=" * 50)
        
        for i, question in enumerate(questions, 1):
            print(f"\\nQuestion {i}:")
            print(f"📝 {question.text}")
            print("\\nOptions:")
            for letter, option in question.options.items():
                marker = "✅" if letter == question.correct_answer else "  "
                print(f"  {marker} {letter}) {option}")
            print(f"\\n💡 Explanation: {question.explanation}")
            print("-" * 30)
        
        # Test different difficulties
        print("\\n🎯 Testing different difficulty levels...")
        
        for difficulty in ["easy", "medium", "hard"]:
            print(f"\\n{difficulty.upper()} Question:")
            result = generator.generate_quiz_question("science", difficulty)
            if result and result.get('question'):
                print(f"📝 {result['question']}")
                print(f"✅ Correct: {result.get('correct', 'N/A')}")
            else:
                print(f"⚠️ Failed to generate {difficulty} question")
        
        print("\\n🎉 Demo completed successfully!")
        print("\\n💡 You can now use local inference in your application:")
        print("   - No external API dependencies")
        print("   - Complete privacy and control")
        print("   - Uses your fine-tuned models")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    try:
        with open("demo_local_inference.py", 'w', encoding='utf-8') as f:
            f.write(demo_content)
        
        print("✅ Created demo_local_inference.py")
        print("💡 Run: python demo_local_inference.py")
        return True
        
    except Exception as e:
        print(f"❌ Error creating demo: {e}")
        return False

def create_usage_guide():
    """Create a usage guide for the local inference system"""
    print("\n📖 Creating Usage Guide...")
    
    guide_content = '''# Local Inference System - Usage Guide

## Overview

The Local Inference System allows you to generate MCQ questions using your fine-tuned models instead of external APIs like Groq. This provides:

- ✅ **Complete Privacy**: No data sent to external services
- ✅ **Cost Savings**: No API fees
- ✅ **Customization**: Uses your specifically trained models
- ✅ **Offline Operation**: Works without internet connection

## Quick Start

### 1. Basic Usage

```python
from knowledge_app.core.local_question_generator import get_local_question_generator, initialize_local_generator

# Initialize the system
initialize_local_generator()

# Get the generator
generator = get_local_question_generator()

# Generate questions
questions = await generator.generate_questions(
    content="Your educational content here",
    count=5,
    difficulty="medium"
)
```

### 2. Using with Existing CloudInference

```python
from knowledge_app.core.inference import CloudInference
from knowledge_app.config import AppConfig

# Configure for local inference
config = AppConfig()
config.use_local_inference = True

# Create inference instance
inference = CloudInference(config)

# Generate quiz (will use local model automatically)
result = await inference.generate_quiz_async("Your content", "medium")
```

## Available Models

The system automatically detects:

- **LoRA Adapters**: Fine-tuned adapters in `data/lora_adapters_mistral/`
- **Full Models**: Complete fine-tuned models in `data/fine_tuned_models/`
- **Base Models**: Fallback to base models if no trained models found

## Configuration

### Model Selection

```python
# Use specific model
generator = LocalQuestionGenerator(
    model_path="data/lora_adapters_mistral/my_model",
    base_model="mistralai/Mistral-7B-Instruct-v0.1"
)

# Switch models
generator.switch_model("path/to/other/model")
```

### Generation Parameters

```python
from knowledge_app.core.local_model_inference import LocalModelConfig

config = LocalModelConfig(
    max_new_tokens=600,
    temperature=0.7,
    top_p=0.9,
    top_k=50,
    repetition_penalty=1.1
)
```

## UI Integration

### Adding to Main Interface

```python
from knowledge_app.ui.local_model_manager import LocalModelManager

# Add to your main window
local_model_tab = LocalModelManager()
your_tab_widget.addTab(local_model_tab, "🧠 Local Models")

# Connect signals
local_model_tab.model_changed.connect(self.on_model_changed)
local_model_tab.inference_mode_changed.connect(self.on_mode_changed)
```

## API Reference

### LocalQuestionGenerator

- `initialize()`: Initialize the generator
- `generate_questions(content, count, difficulty)`: Generate multiple questions
- `generate_quiz_question(category, difficulty)`: Generate single question
- `switch_model(model_path, base_model)`: Switch to different model
- `get_model_info()`: Get current model information
- `cleanup()`: Clean up resources

### LocalModelInference

- `load_model(model_path, base_model)`: Load a model
- `generate_text(prompt, **kwargs)`: Generate text
- `generate_mcq_questions(content, num_questions, difficulty)`: Generate MCQs
- `find_available_models()`: List available models
- `unload_model()`: Unload current model

## Troubleshooting

### Common Issues

1. **No models found**
   - Check `data/lora_adapters_mistral/` directory
   - Ensure models are properly trained and saved
   - System will fallback to base model

2. **Out of memory errors**
   - Use smaller models (DialoGPT-small/medium)
   - Enable 4-bit quantization
   - Reduce max_new_tokens

3. **Slow generation**
   - Use GPU if available
   - Reduce model size
   - Optimize generation parameters

### Performance Tips

- **GPU Usage**: Ensure CUDA is available for faster inference
- **Model Size**: Use appropriate model size for your hardware
- **Caching**: Questions are cached to avoid regeneration
- **Batch Processing**: Generate multiple questions at once

## Examples

### Generate Questions from Text File

```python
# Read content from file
with open("textbook_chapter.txt", "r") as f:
    content = f.read()

# Generate questions
questions = await generator.generate_questions(content, count=10)

# Save to JSON
import json
with open("generated_questions.json", "w") as f:
    json.dump([q.__dict__ for q in questions], f, indent=2)
```

### Batch Processing

```python
# Process multiple documents
documents = ["doc1.txt", "doc2.txt", "doc3.txt"]
all_questions = []

for doc_path in documents:
    with open(doc_path, "r") as f:
        content = f.read()
    
    questions = await generator.generate_questions(content, count=5)
    all_questions.extend(questions)

print(f"Generated {len(all_questions)} total questions")
```

## Integration with Training

After training a new model:

1. Model is automatically saved to `data/lora_adapters_mistral/`
2. Refresh the local model manager UI
3. Select and load your new model
4. Test question generation
5. Switch inference mode to local

## Support

For issues or questions:
- Check the test scripts: `test_local_inference_system.py`
- Run the demo: `demo_local_inference.py`
- Review logs for detailed error information
'''
    
    try:
        with open("LOCAL_INFERENCE_GUIDE.md", 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print("✅ Created LOCAL_INFERENCE_GUIDE.md")
        return True
        
    except Exception as e:
        print(f"❌ Error creating guide: {e}")
        return False

def main():
    """Run the integration process"""
    print("🚀 Integrating Local Inference System")
    print("=" * 50)
    
    tasks = [
        ("Add Local Model Tab to Main UI", add_local_model_tab_to_main_ui),
        ("Update Configuration", update_config_for_local_inference),
        ("Create Demo Script", create_local_inference_demo),
        ("Create Usage Guide", create_usage_guide),
    ]
    
    results = []
    for task_name, task_func in tasks:
        print(f"\n{task_name}...")
        try:
            result = task_func()
            results.append((task_name, result))
        except Exception as e:
            print(f"❌ {task_name} failed: {e}")
            results.append((task_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("INTEGRATION SUMMARY")
    print('='*50)
    
    passed = 0
    for task_name, result in results:
        status = "✅ COMPLETED" if result else "❌ FAILED"
        print(f"{task_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tasks completed")
    
    if passed == len(results):
        print("\n🎉 LOCAL INFERENCE SYSTEM FULLY INTEGRATED!")
        print("\n📋 Next Steps:")
        print("1. Run: python test_local_inference_system.py")
        print("2. Run: python demo_local_inference.py")
        print("3. Train some models using your training system")
        print("4. Use the Local Model Manager in your UI")
        print("5. Enjoy offline MCQ generation! 🧠")
        return 0
    else:
        print(f"\n⚠️ {len(results) - passed} tasks failed.")
        print("Some manual integration may be required.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''
