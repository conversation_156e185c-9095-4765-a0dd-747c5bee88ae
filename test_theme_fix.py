#!/usr/bin/env python3
"""
Test script to verify theme functionality is working properly.

This script tests:
1. Theme loading from configuration
2. Theme saving to configuration  
3. Theme switching between dark and light
4. Theme persistence across application restarts
"""

import sys
import os
import json
import tempfile
from pathlib import Path

# Add src directory to path
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

def test_config_manager_theme():
    """Test theme functionality in ConfigManager"""
    print("🧪 Testing ConfigManager theme functionality...")
    
    try:
        from knowledge_app.core.config_manager import ConfigManager
        
        # Create a temporary config file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_config_path = f.name
            # Write initial config with dark theme
            initial_config = {
                'app_settings': {
                    'theme': 'dark',
                    'language': 'en'
                }
            }
            json.dump(initial_config, f, indent=4)
        
        # Test loading theme
        config = ConfigManager()
        theme = config.get_value('app_settings.theme', 'light')
        print(f"✅ Loaded theme from config: {theme}")
        assert theme == 'dark', f"Expected 'dark', got '{theme}'"
        
        # Test saving theme
        config.set_value('app_settings.theme', 'light')
        config.save_config()
        print("✅ Saved light theme to config")
        
        # Test loading the saved theme
        config2 = ConfigManager()
        theme2 = config2.get_value('app_settings.theme', 'dark')
        print(f"✅ Loaded saved theme: {theme2}")
        assert theme2 == 'light', f"Expected 'light', got '{theme2}'"
        
        # Cleanup
        os.unlink(temp_config_path)
        
        print("✅ ConfigManager theme tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ ConfigManager theme test failed: {e}")
        return False

def test_enterprise_design_system():
    """Test EnterpriseDesignSystem theme switching"""
    print("\n🧪 Testing EnterpriseDesignSystem theme switching...")
    
    try:
        from knowledge_app.ui.enterprise_design_system import EnterpriseDesignSystem, Theme
        
        # Create design system
        design_system = EnterpriseDesignSystem()
        
        # Test initial theme (should be dark)
        assert design_system.current_theme == Theme.DARK
        print("✅ Initial theme is dark")
        
        # Test getting dark theme colors
        dark_bg = design_system.color('bg_primary')
        print(f"✅ Dark theme background: {dark_bg}")
        assert dark_bg is not None and dark_bg != '#000000'
        
        # Test switching to light theme
        design_system.set_theme(Theme.LIGHT)
        assert design_system.current_theme == Theme.LIGHT
        print("✅ Switched to light theme")
        
        # Test getting light theme colors
        light_bg = design_system.color('bg_primary')
        print(f"✅ Light theme background: {light_bg}")
        assert light_bg is not None and light_bg != dark_bg
        
        # Test switching back to dark
        design_system.set_theme(Theme.DARK)
        assert design_system.current_theme == Theme.DARK
        print("✅ Switched back to dark theme")
        
        print("✅ EnterpriseDesignSystem theme tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ EnterpriseDesignSystem theme test failed: {e}")
        return False

def test_style_manager():
    """Test EnterpriseStyleManager theme functionality"""
    print("\n🧪 Testing EnterpriseStyleManager theme functionality...")
    
    try:
        from knowledge_app.ui.enterprise_style_manager import EnterpriseStyleManager
        from knowledge_app.ui.enterprise_design_system import Theme
        
        # Create style manager
        style_manager = EnterpriseStyleManager()
        
        # Test setting theme
        style_manager.set_theme(Theme.DARK)
        print("✅ Set dark theme in style manager")
        
        # Test getting styles
        button_style = style_manager.get_style('button')
        print(f"✅ Got button style: {len(button_style)} characters")
        assert len(button_style) > 0
        
        # Test switching theme
        style_manager.set_theme(Theme.LIGHT)
        print("✅ Switched to light theme in style manager")
        
        # Test that styles change with theme
        button_style_light = style_manager.get_style('button')
        print(f"✅ Got light button style: {len(button_style_light)} characters")
        
        print("✅ EnterpriseStyleManager theme tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ EnterpriseStyleManager theme test failed: {e}")
        return False

def test_main_window_theme_loading():
    """Test MainWindow theme loading functionality"""
    print("\n🧪 Testing MainWindow theme loading...")
    
    try:
        # This test requires PyQt5, so we'll skip it if not available
        try:
            from PyQt5.QtWidgets import QApplication
        except ImportError:
            print("⚠️ PyQt5 not available, skipping MainWindow test")
            return True
            
        from knowledge_app.ui.main_window import MainWindow
        
        # Create a minimal QApplication if none exists
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create main window (this should load theme from config)
        main_window = MainWindow()
        
        # Test that theme loading method exists
        assert hasattr(main_window, '_load_theme_from_config')
        print("✅ MainWindow has _load_theme_from_config method")
        
        # Test theme loading
        theme = main_window._load_theme_from_config()
        print(f"✅ MainWindow loaded theme: {theme}")
        assert theme in ['dark', 'light']
        
        # Test theme saving method exists
        assert hasattr(main_window, '_save_theme_to_config')
        print("✅ MainWindow has _save_theme_to_config method")
        
        # Test theme saving
        main_window._save_theme_to_config('light')
        print("✅ MainWindow saved theme to config")
        
        print("✅ MainWindow theme tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ MainWindow theme test failed: {e}")
        return False

def main():
    """Run all theme tests"""
    print("🎨 Testing Knowledge App Theme System")
    print("=" * 50)
    
    tests = [
        test_config_manager_theme,
        test_enterprise_design_system,
        test_style_manager,
        test_main_window_theme_loading
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎨 Theme System Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All theme tests passed! Theme system is working correctly.")
        return 0
    else:
        print("❌ Some theme tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
