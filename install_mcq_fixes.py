#!/usr/bin/env python3
"""
MCQ Fixes Installation Script

This script installs and configures all the fixes for critical MCQ generation issues:
1. CUDA memory configuration for RTX 3060 12GB
2. Compatible package versions
3. hf_xet for optimized downloads
4. xFormers compatibility fixes
5. Pydantic configuration for DataFrame support
"""

import os
import sys
import subprocess
import logging
import platform
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("❌ Python 3.8+ required. Current version: {}.{}.{}".format(
            version.major, version.minor, version.micro))
        return False
    
    logger.info(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def check_cuda_availability():
    """Check CUDA availability and version"""
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✅ NVIDIA GPU detected")
            # Extract CUDA version from nvidia-smi output
            lines = result.stdout.split('\n')
            for line in lines:
                if 'CUDA Version:' in line:
                    cuda_version = line.split('CUDA Version:')[1].strip().split()[0]
                    logger.info(f"✅ CUDA Version: {cuda_version}")
                    return True
        else:
            logger.warning("⚠️ nvidia-smi not found or failed")
            return False
    except FileNotFoundError:
        logger.warning("⚠️ nvidia-smi not found - CUDA may not be available")
        return False

def install_package(package_name, upgrade=False):
    """Install a package using pip"""
    try:
        cmd = [sys.executable, '-m', 'pip', 'install']
        if upgrade:
            cmd.append('--upgrade')
        cmd.append(package_name)
        
        logger.info(f"🔄 Installing {package_name}...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"✅ Successfully installed {package_name}")
            return True
        else:
            logger.error(f"❌ Failed to install {package_name}: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error installing {package_name}: {e}")
        return False

def install_hf_xet():
    """Install hf_xet for optimized Hugging Face downloads"""
    logger.info("🔄 Installing hf_xet for optimized downloads...")
    
    # Try different installation methods
    packages_to_try = [
        "hf_xet",
        "hf-xet",
        "huggingface-xet"
    ]
    
    for package in packages_to_try:
        if install_package(package):
            logger.info(f"✅ Successfully installed {package}")
            return True
    
    logger.warning("⚠️ Could not install hf_xet - downloads may be slower")
    return False

def install_compatible_pytorch():
    """Install compatible PyTorch version for CUDA 12.1"""
    logger.info("🔄 Installing compatible PyTorch for CUDA 12.1...")
    
    # PyTorch 2.5.1 with CUDA 12.1 support
    pytorch_packages = [
        "torch==2.5.1",
        "torchvision==0.20.1", 
        "torchaudio==2.5.1"
    ]
    
    # Install with CUDA 12.1 index
    cuda_index = "--index-url https://download.pytorch.org/whl/cu121"
    
    for package in pytorch_packages:
        cmd = [sys.executable, '-m', 'pip', 'install', package, cuda_index]
        logger.info(f"🔄 Installing {package} with CUDA 12.1...")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✅ Successfully installed {package}")
            else:
                logger.warning(f"⚠️ Failed to install {package}: {result.stderr}")
                # Try without CUDA index as fallback
                if install_package(package):
                    logger.info(f"✅ Installed {package} (fallback)")
        except Exception as e:
            logger.error(f"❌ Error installing {package}: {e}")
    
    return True

def install_xformers_compatible():
    """Install xFormers compatible with PyTorch 2.5.1"""
    logger.info("🔄 Checking xFormers compatibility...")
    
    # Check if xFormers is already installed and compatible
    try:
        import xformers
        logger.info(f"✅ xFormers already installed: {xformers.__version__}")
        return True
    except ImportError:
        logger.info("xFormers not installed, attempting installation...")
    except Exception as e:
        logger.warning(f"⚠️ xFormers import error: {e}")
    
    # Try to install compatible xFormers
    xformers_packages = [
        "xformers>=0.0.28",
        "xformers"
    ]
    
    for package in xformers_packages:
        if install_package(package):
            try:
                import xformers
                logger.info(f"✅ Successfully installed xFormers: {xformers.__version__}")
                return True
            except ImportError:
                logger.warning(f"⚠️ xFormers installed but not importable")
                continue
    
    logger.warning("⚠️ Could not install compatible xFormers - using fallback attention")
    return False

def configure_cuda_environment():
    """Configure CUDA environment variables"""
    logger.info("🔄 Configuring CUDA environment...")

    # Set platform-specific CUDA configuration for RTX 3060 12GB
    import platform

    cuda_config = {
        'CUDA_VISIBLE_DEVICES': '0',
        'CUDA_LAUNCH_BLOCKING': '1'
    }

    # Platform-specific CUDA memory configuration
    if platform.system() == "Windows":
        # Windows-compatible configuration (expandable_segments not supported)
        cuda_config['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:256,garbage_collection_threshold:0.8,roundup_power2_divisions:16'
        logger.info("🪟 Using Windows-compatible CUDA configuration")
    else:
        # Linux/Unix configuration with expandable segments
        cuda_config['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:256,expandable_segments:True,garbage_collection_threshold:0.8'
        logger.info("🐧 Using Linux-compatible CUDA configuration")
    
    # Create or update .env file
    env_file = Path('.env')
    env_content = []
    
    # Read existing .env if it exists
    if env_file.exists():
        with open(env_file, 'r') as f:
            env_content = f.readlines()
    
    # Update or add CUDA configurations
    updated_vars = set()
    for i, line in enumerate(env_content):
        for var, value in cuda_config.items():
            if line.startswith(f"{var}="):
                env_content[i] = f"{var}={value}\n"
                updated_vars.add(var)
                logger.info(f"✅ Updated {var}={value}")
                break
    
    # Add new variables
    for var, value in cuda_config.items():
        if var not in updated_vars:
            env_content.append(f"{var}={value}\n")
            logger.info(f"✅ Added {var}={value}")
    
    # Write updated .env file
    with open(env_file, 'w') as f:
        f.writelines(env_content)
    
    logger.info(f"✅ CUDA configuration saved to {env_file}")
    return True

def install_core_dependencies():
    """Install core dependencies from requirements.txt"""
    logger.info("🔄 Installing core dependencies...")
    
    requirements_file = Path('requirements.txt')
    if not requirements_file.exists():
        logger.error("❌ requirements.txt not found")
        return False
    
    cmd = [sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✅ Successfully installed core dependencies")
            return True
        else:
            logger.error(f"❌ Failed to install dependencies: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"❌ Error installing dependencies: {e}")
        return False

def verify_installation():
    """Verify that all components are installed correctly"""
    logger.info("🔄 Verifying installation...")
    
    # Test imports
    test_imports = [
        ('torch', 'PyTorch'),
        ('transformers', 'Transformers'),
        ('accelerate', 'Accelerate'),
        ('peft', 'PEFT'),
        ('bitsandbytes', 'BitsAndBytes')
    ]
    
    success_count = 0
    for module, name in test_imports:
        try:
            __import__(module)
            logger.info(f"✅ {name} import successful")
            success_count += 1
        except ImportError as e:
            logger.error(f"❌ {name} import failed: {e}")
    
    # Test CUDA if available
    try:
        import torch
        if torch.cuda.is_available():
            logger.info(f"✅ CUDA available: {torch.cuda.get_device_name(0)}")
            logger.info(f"✅ CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
        else:
            logger.warning("⚠️ CUDA not available")
    except Exception as e:
        logger.error(f"❌ CUDA test failed: {e}")
    
    # Test pydantic configuration
    try:
        from knowledge_app.core.pydantic_config import PYDANTIC_AVAILABLE
        if PYDANTIC_AVAILABLE:
            logger.info("✅ Pydantic configuration successful")
        else:
            logger.warning("⚠️ Pydantic not available")
    except Exception as e:
        logger.warning(f"⚠️ Pydantic configuration test failed: {e}")
    
    return success_count == len(test_imports)

def main():
    """Main installation function"""
    logger.info("🚀 Starting MCQ fixes installation...")
    
    # Check prerequisites
    if not check_python_version():
        return 1
    
    # Check CUDA
    cuda_available = check_cuda_availability()
    
    # Installation steps
    steps = [
        ("Configure CUDA environment", configure_cuda_environment),
        ("Install core dependencies", install_core_dependencies),
        ("Install compatible PyTorch", install_compatible_pytorch),
        ("Install hf_xet", install_hf_xet),
        ("Install xFormers", install_xformers_compatible),
        ("Verify installation", verify_installation)
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        logger.info(f"\n{'='*50}")
        logger.info(f"Step: {step_name}")
        logger.info(f"{'='*50}")
        
        try:
            if step_func():
                logger.info(f"✅ {step_name} completed successfully")
            else:
                logger.error(f"❌ {step_name} failed")
                failed_steps.append(step_name)
        except Exception as e:
            logger.error(f"❌ {step_name} failed with error: {e}")
            failed_steps.append(step_name)
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("INSTALLATION SUMMARY")
    logger.info(f"{'='*50}")
    
    if not failed_steps:
        logger.info("🎉 All installation steps completed successfully!")
        logger.info("\nNext steps:")
        logger.info("1. Run 'python test_mcq_fixes.py' to verify fixes")
        logger.info("2. Test offline MCQ generation in the application")
        logger.info("3. Monitor GPU memory usage during generation")
        return 0
    else:
        logger.warning(f"⚠️ {len(failed_steps)} step(s) failed:")
        for step in failed_steps:
            logger.warning(f"  - {step}")
        logger.info("\nYou can still proceed, but some features may not work optimally.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
