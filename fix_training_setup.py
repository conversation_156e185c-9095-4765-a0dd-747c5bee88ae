#!/usr/bin/env python3
"""
Training Setup Fixer

This script fixes common issues that prevent real 7B model training:
1. Ensures training data exists and is accessible
2. Validates configuration paths
3. Checks GPU availability
4. Creates missing directories
5. Fixes path issues
"""

import os
import sys
import json
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def check_and_fix_training_data():
    """Check and fix training data issues"""
    logger.info("🔍 Checking training data setup...")
    
    # Expected training data path
    training_data_path = "lora_adapters_mistral/default/training_data_augmented_default.txt"
    
    if os.path.exists(training_data_path):
        size = os.path.getsize(training_data_path)
        logger.info(f"✅ Training data found: {training_data_path} ({size} bytes)")
        
        # Check if file has content
        if size == 0:
            logger.warning("⚠️ Training data file is empty!")
            create_sample_training_data(training_data_path)
        elif size < 1000:
            logger.warning("⚠️ Training data file is very small - may not be sufficient")
            
        return True
    else:
        logger.error(f"❌ Training data not found: {training_data_path}")
        logger.info("🔧 Creating sample training data...")
        create_sample_training_data(training_data_path)
        return True

def create_sample_training_data(path):
    """Create sample training data if missing"""
    try:
        # Ensure directory exists
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # Create sample training data
        sample_data = """Question: What is the capital of France?
Answer: The capital of France is Paris.

Question: Explain the concept of machine learning.
Answer: Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed.

Question: What is the formula for calculating the area of a circle?
Answer: The area of a circle is calculated using the formula A = πr², where r is the radius of the circle.

Question: Describe the process of photosynthesis.
Answer: Photosynthesis is the process by which plants convert light energy into chemical energy, using carbon dioxide and water to produce glucose and oxygen.

Question: What are the three states of matter?
Answer: The three states of matter are solid, liquid, and gas.

Question: Explain Newton's first law of motion.
Answer: Newton's first law states that an object at rest stays at rest and an object in motion stays in motion with the same speed and in the same direction unless acted upon by an unbalanced force.

Question: What is the difference between DNA and RNA?
Answer: DNA is double-stranded and contains genetic information, while RNA is single-stranded and plays roles in protein synthesis and gene regulation.

Question: How does the water cycle work?
Answer: The water cycle involves evaporation, condensation, precipitation, and collection, continuously moving water through the environment.

Question: What is the periodic table?
Answer: The periodic table is a systematic arrangement of chemical elements organized by their atomic number and chemical properties.

Question: Explain the concept of gravity.
Answer: Gravity is a fundamental force that attracts objects with mass toward each other, with strength proportional to their masses and inversely proportional to the square of the distance between them.
"""
        
        with open(path, 'w', encoding='utf-8') as f:
            f.write(sample_data)
            
        logger.info(f"✅ Created sample training data: {path}")
        logger.info(f"📊 File size: {os.path.getsize(path)} bytes")
        
    except Exception as e:
        logger.error(f"❌ Failed to create training data: {e}")

def check_gpu_setup():
    """Check GPU availability and configuration"""
    logger.info("🎮 Checking GPU setup...")
    
    try:
        import torch
        
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            logger.info(f"✅ CUDA available with {gpu_count} GPU(s)")
            
            for i in range(gpu_count):
                props = torch.cuda.get_device_properties(i)
                memory_gb = props.total_memory / 1e9
                logger.info(f"  GPU {i}: {props.name} ({memory_gb:.1f}GB)")
                
                if memory_gb < 6:
                    logger.warning(f"⚠️ GPU {i} has low memory for 7B training")
                else:
                    logger.info(f"✅ GPU {i} has sufficient memory for 7B training")
                    
        else:
            logger.warning("❌ CUDA not available - will use CPU training (very slow)")
            
    except ImportError:
        logger.error("❌ PyTorch not installed")
    except Exception as e:
        logger.error(f"❌ Error checking GPU: {e}")

def check_required_directories():
    """Check and create required directories"""
    logger.info("📁 Checking required directories...")
    
    required_dirs = [
        "data",
        "lora_adapters_mistral",
        "lora_adapters_mistral/default",
        "lora_adapters_mistral/default/extracted_training_images",
        "data/lora_adapters_mistral",
        "data/lora_adapters_mistral/real_7b"
    ]
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            logger.info(f"✅ Directory exists: {dir_path}")
        else:
            try:
                os.makedirs(dir_path, exist_ok=True)
                logger.info(f"✅ Created directory: {dir_path}")
            except Exception as e:
                logger.error(f"❌ Failed to create directory {dir_path}: {e}")

def check_config_file():
    """Check and fix configuration file"""
    logger.info("⚙️ Checking configuration...")
    
    config_file = "data/real_7b_config.json"
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            logger.info("✅ Configuration file found and valid")
            
            # Check training data path in config
            train_data_path = config.get("data", {}).get("train_data_path", "")
            if train_data_path and not os.path.exists(train_data_path):
                logger.warning(f"⚠️ Config points to missing training data: {train_data_path}")
                
        except Exception as e:
            logger.error(f"❌ Error reading config file: {e}")
    else:
        logger.info("ℹ️ Configuration file will be created on first run")

def check_dependencies():
    """Check required dependencies"""
    logger.info("📦 Checking dependencies...")
    
    required_packages = [
        "torch",
        "transformers",
        "peft",
        "bitsandbytes",
        "accelerate",
        "datasets"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✅ {package} installed")
        except ImportError:
            logger.error(f"❌ {package} not installed")
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"❌ Missing packages: {', '.join(missing_packages)}")
        logger.info("💡 Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True

def main():
    """Main function to run all checks and fixes"""
    print("🔧 Training Setup Fixer")
    print("=" * 50)
    
    logger.info(f"📁 Working directory: {os.getcwd()}")
    
    # Run all checks
    checks = [
        ("Training Data", check_and_fix_training_data),
        ("Required Directories", check_required_directories),
        ("GPU Setup", check_gpu_setup),
        ("Configuration", check_config_file),
        ("Dependencies", check_dependencies)
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        print(f"\n🔍 {check_name}:")
        try:
            result = check_func()
            if result is False:
                all_passed = False
        except Exception as e:
            logger.error(f"❌ Error in {check_name}: {e}")
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✅ All checks passed! Your setup should work for real 7B training.")
        print("🚀 You can now run the GUI automation with confidence!")
    else:
        print("⚠️ Some issues were found. Please fix them before running training.")
        print("💡 Check the messages above for specific fixes needed.")
    
    print("\n🔥 Next steps:")
    print("1. Run: python run_gui_automation.py")
    print("2. Or run: run_automation.bat")
    print("3. Watch for 'REAL 7B model training' messages (not simulation)")

if __name__ == "__main__":
    main()
