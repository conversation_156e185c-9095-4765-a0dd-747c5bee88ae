# 🤖 GUI Automation Debugger for Knowledge App

This powerful automation system automatically navigates your GUI, tests the training workflow, detects errors in real-time, and provides comprehensive debugging information.

## 🚀 Quick Start

### Option 1: Simple Run (Windows)
```bash
# Double-click or run:
run_automation.bat
```

### Option 2: Python Script
```bash
python run_gui_automation.py
```

### Option 3: With Pytest Integration
```bash
# Run automation tests
pytest tests/test_gui_automation_integration.py -v

# Run with actual GUI (slow)
pytest tests/test_gui_automation_integration.py --run-gui-tests -v
```

## 🎯 What It Does

The automation system will:

1. **🚀 Launch your app** - Automatically starts the Knowledge App
2. **🎯 Navigate to training** - Finds and clicks "Train AI Model" button
3. **🔥 Select Mistral model** - Clicks the "Mistral-7B QLoRA" button
4. **👁️ Monitor training** - Watches for errors and progress updates
5. **📸 Take screenshots** - Captures every step for debugging
6. **🚨 Detect errors** - Automatically finds error dialogs and messages
7. **🔧 Suggest fixes** - Provides specific solutions for common issues
8. **📋 Generate reports** - Creates detailed error reports with debugging info

## 🔍 Error Detection & Debugging

### Automatic Error Detection
- **Training Configuration Errors**: `args.eval_strategy` issues, missing datasets
- **Model Loading Errors**: GPU memory, CUDA availability, model compatibility
- **UI Navigation Errors**: Missing buttons, dialog failures
- **System Resource Errors**: Memory, disk space, dependencies

### Smart Error Suggestions
The system provides specific fixes for common errors:

```
❌ Error: "args.eval_strategy to steps but you didn't pass an eval_dataset"
🔧 Fix: Add 'eval_dataset' parameter to training configuration or set 'eval_strategy' to 'no'

❌ Error: "REAL 7B training failed"
🔧 Fix: Check model availability and GPU memory. Ensure proper environment setup.
```

## 📁 Output Files

All outputs are saved to `automation_screenshots/` directory:

### Screenshots
- `app_launched.png` - App startup
- `before_train_click.png` - Before clicking train button
- `training_dialog_opened.png` - Training dialog
- `before_mistral_click.png` - Before model selection
- `mistral_selected.png` - After model selection
- `monitoring_X.png` - Periodic monitoring screenshots
- `error_*.png` - Error state screenshots

### Reports
- `automation_summary.txt` - Overall automation summary
- `error_report_TIMESTAMP.json` - Detailed error reports
- `gui_automation_debug.log` - Complete debug log

### Error Report Example
```json
{
  "timestamp": "2024-01-15T10:30:45",
  "error_type": "training_error",
  "message": "Training failed: args.eval_strategy error",
  "suggested_fix": "Add 'eval_dataset' parameter or set 'eval_strategy' to 'no'",
  "screenshot_path": "automation_screenshots/error_training_error.png",
  "ui_state": {
    "automation_state": "monitoring_training",
    "visible_buttons": [...],
    "active_widgets": [...]
  }
}
```

## 🔧 Configuration

### Timeouts & Retries
```python
# In gui_automation_debugger.py
self.max_retries = 3              # Maximum retry attempts
max_monitoring_time = 300         # 5 minutes monitoring
timeout = 10                      # Window/dialog timeouts
```

### Custom Error Handling
Add your own error patterns:
```python
def _handle_training_errors(self, error_dialogs):
    for dialog in error_dialogs:
        error_text = dialog.text()
        
        # Add your custom error detection
        if "your_specific_error" in error_text:
            suggested_fix = "Your specific solution"
```

## 🧪 Integration with Existing Tests

The automation integrates seamlessly with your existing test framework:

```python
# Run as part of your test suite
pytest tests/test_gui_automation_integration.py

# Include in your comprehensive tests
pytest tests/ -k "automation"
```

### Test Categories
- **Unit Tests**: Test individual automation components
- **Integration Tests**: Test full workflow with mocks
- **GUI Tests**: Actual GUI automation (use `--run-gui-tests`)

## 🚨 Troubleshooting

### Common Issues

**App doesn't launch:**
```bash
# Check dependencies
pip install -r requirements.txt

# Check Python path
python -c "import sys; print(sys.path)"
```

**Button not found:**
- Check if ML features are enabled in config
- Verify UI layout hasn't changed
- Check button text matches exactly

**Training dialog doesn't appear:**
- Increase timeout values
- Check for blocking dialogs
- Verify window focus

**Screenshots are blank:**
- Check window visibility
- Verify screen resolution
- Check permissions

### Debug Mode
Enable verbose logging:
```python
logging.getLogger().setLevel(logging.DEBUG)
```

## 🔄 Retry Logic

The system automatically retries failed operations:

1. **First attempt**: Normal execution
2. **Retry 1**: Increased timeouts
3. **Retry 2**: Additional error handling
4. **Retry 3**: Final attempt with maximum timeouts

Each retry includes:
- Clean application restart
- Extended wait times
- Additional error detection
- Detailed logging

## 📊 Monitoring Features

### Real-time Progress Tracking
- Training progress percentages
- Accuracy improvements
- Loss reduction
- Epoch/batch information

### System Resource Monitoring
- GPU memory usage
- CPU utilization
- Memory consumption
- Disk space

### Error Pattern Recognition
- Common training errors
- Configuration issues
- Resource limitations
- Model compatibility problems

## 🎛️ Advanced Usage

### Custom Automation Workflows
```python
debugger = GUIAutomationDebugger()

# Customize for your needs
debugger.max_retries = 5
debugger.monitoring_active = True

# Add custom error handlers
debugger.custom_error_patterns = {
    "your_pattern": "your_solution"
}

# Run automation
success = debugger.run_automation()
```

### Integration with CI/CD
```yaml
# GitHub Actions example
- name: Run GUI Automation Tests
  run: |
    python run_gui_automation.py
    pytest tests/test_gui_automation_integration.py --run-gui-tests
```

## 📈 Performance Optimization

### Speed Improvements
- Parallel error detection
- Asynchronous screenshot capture
- Optimized widget searching
- Smart retry delays

### Memory Management
- Automatic cleanup
- Resource monitoring
- Memory leak detection
- Garbage collection

## 🤝 Contributing

To extend the automation system:

1. Add new error patterns in `_suggest_fix()`
2. Extend UI element detection in `_find_*_button()`
3. Add custom monitoring in `_check_training_progress()`
4. Create new test cases in `test_gui_automation_integration.py`

## 📞 Support

If you encounter issues:

1. Check the `automation_screenshots/` directory for visual debugging
2. Review `gui_automation_debug.log` for detailed logs
3. Examine error reports in JSON format
4. Run with increased verbosity: `python run_gui_automation.py --verbose`

The automation system is designed to be your debugging companion - it will help you identify and fix those pesky training errors quickly and efficiently! 🔥
