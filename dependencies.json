{"metadata": {"version": "1.0", "description": "Externalized dependency metadata for advanced_dependency_manager.py", "last_updated": "2024-12-11", "maintainer": "Knowledge App Team"}, "constants": {"python_min_version": [3, 8], "max_retry_count": 3, "pyqt_version": "5.15.9", "max_download_threads": 4}, "fixed_dependencies": {"pyqt5": "5.15.9", "pyqt5-qt5": "5.15.2", "pyqt5-sip": "12.11.0", "pyqt5-tools": "********.3"}, "installation_priority": ["wheel", "setuptools", "pip", "pyqt5-sip", "pyqt5-qt5", "pyqt5", "torch", "torchvision", "<PERSON><PERSON><PERSON>"], "pytorch_wheels": {"cu118": {"torch": "https://download.pytorch.org/whl/cu118/torch-2.0.1%2Bcu118-cp310-cp310-win_amd64.whl", "torchvision": "https://download.pytorch.org/whl/cu118/torchvision-0.15.2%2Bcu118-cp310-cp310-win_amd64.whl", "torchaudio": "https://download.pytorch.org/whl/cu118/torchaudio-2.0.2%2Bcu118-cp310-cp310-win_amd64.whl"}, "cu121": {"torch": "https://download.pytorch.org/whl/cu121/torch-2.1.0%2Bcu121-cp310-cp310-win_amd64.whl", "torchvision": "https://download.pytorch.org/whl/cu121/torchvision-0.16.0%2Bcu121-cp310-cp310-win_amd64.whl", "torchaudio": "https://download.pytorch.org/whl/cu121/torchaudio-2.1.0%2Bcu121-cp310-cp310-win_amd64.whl"}, "cu122": {"torch": "https://download.pytorch.org/whl/cu122/torch-2.2.0%2Bcu122-cp310-cp310-win_amd64.whl", "torchvision": "https://download.pytorch.org/whl/cu122/torchvision-0.17.0%2Bcu122-cp310-cp310-win_amd64.whl", "torchaudio": "https://download.pytorch.org/whl/cu122/torchaudio-2.2.0%2Bcu122-cp310-cp310-win_amd64.whl"}, "cu128": {"torch": "https://download.pytorch.org/whl/cu128/torch-2.4.0%2Bcu128-cp310-cp310-win_amd64.whl", "torchvision": "https://download.pytorch.org/whl/cu128/torchvision-0.19.0%2Bcu128-cp310-cp310-win_amd64.whl", "torchaudio": "https://download.pytorch.org/whl/cu128/torchaudio-2.4.0%2Bcu128-cp310-cp310-win_amd64.whl"}, "cpu": {"torch": "https://download.pytorch.org/whl/cpu/torch-2.1.0%2Bcpu-cp310-cp310-win_amd64.whl", "torchvision": "https://download.pytorch.org/whl/cpu/torchvision-0.16.0%2Bcpu-cp310-cp310-win_amd64.whl", "torchaudio": "https://download.pytorch.org/whl/cpu/torchaudio-2.1.0%2Bcpu-cp310-cp310-win_amd64.whl"}, "mps": {"torch": "https://download.pytorch.org/whl/nightly/torch-2.1.0.dev20230706-cp310-cp310-macosx_11_0_arm64.whl", "torchvision": "https://download.pytorch.org/whl/nightly/torchvision-0.16.0.dev20230706-cp310-cp310-macosx_11_0_arm64.whl", "torchaudio": "https://download.pytorch.org/whl/nightly/torchaudio-2.1.0.dev20230706-cp310-cp310-macosx_11_0_arm64.whl"}}, "cuda_toolkit": {"11.8.0": {"windows": {"url": "https://developer.download.nvidia.com/compute/cuda/11.8.0/local_installers/cuda_11.8.0_522.06_windows.exe", "sha256": "62eef4128a31a8e767bf6160f8496e5c7f1e5f1749d7d461e9e082c9fd59e61b"}, "linux": {"url": "https://developer.download.nvidia.com/compute/cuda/11.8.0/local_installers/cuda_11.8.0_520.61.05_linux.run", "sha256": "629fe3f8752f170a62218eda4bb8b84d6f14b0babd3ac641b9c851acec539222"}}, "12.1.0": {"windows": {"url": "https://developer.download.nvidia.com/compute/cuda/12.1.0/local_installers/cuda_12.1.0_531.14_windows.exe", "sha256": "1a3ea5579ff8f46c5ca70d332a113314c87dae94be119b24a4e538086c89cfcb"}, "linux": {"url": "https://developer.download.nvidia.com/compute/cuda/12.1.0/local_installers/cuda_12.1.0_530.30.02_linux.run", "sha256": "b5fc82258c67545f8c1757d8358a3e553a4f8294a0b3e79be61938ecdf62ab27"}}, "12.8.0": {"windows": {"url": "https://developer.download.nvidia.com/compute/cuda/12.8.0/local_installers/cuda_12.8.0_560.76_windows.exe", "sha256": "a4b8c8e8e7f8f9f0f1f2f3f4f5f6f7f8f9f0f1f2f3f4f5f6f7f8f9f0f1f2f3f4"}, "linux": {"url": "https://developer.download.nvidia.com/compute/cuda/12.8.0/local_installers/cuda_12.8.0_560.28.03_linux.run", "sha256": "b4c8d8e8e7f8f9f0f1f2f3f4f5f6f7f8f9f0f1f2f3f4f5f6f7f8f9f0f1f2f3f4"}}}, "prebuilt_wheels": {"windows": {"pyqt5": "https://download.lfd.uci.edu/pythonlibs/archived/PyQt5-5.15.9-cp310-cp310-win_amd64.whl", "pyqt5_sip": "https://download.lfd.uci.edu/pythonlibs/archived/PyQt5_sip-12.11.0-cp310-cp310-win_amd64.whl", "pyqt5_qt5": "https://download.lfd.uci.edu/pythonlibs/archived/PyQt5_Qt5-5.15.2-py3-none-win_amd64.whl"}, "linux": {}, "macos": {}}, "xformers_wheels": {"cu118": {"url": "https://github.com/facebookresearch/xformers/releases/download/v0.0.22/xformers-0.0.22+cu118-cp310-cp310-win_amd64.whl", "sha256": "example_sha256_hash_for_cu118"}, "cu121": {"url": "https://github.com/facebookresearch/xformers/releases/download/v0.0.22/xformers-0.0.22+cu121-cp310-cp310-win_amd64.whl", "sha256": "example_sha256_hash_for_cu121"}, "cu122": {"url": "https://github.com/facebookresearch/xformers/releases/download/v0.0.22/xformers-0.0.22+cu122-cp310-cp310-win_amd64.whl", "sha256": "example_sha256_hash_for_cu122"}, "cu128": {"url": "https://github.com/facebookresearch/xformers/releases/download/v0.0.26/xformers-0.0.26+cu128-cp310-cp310-win_amd64.whl", "sha256": "example_sha256_hash_for_cu128"}}, "model_urls": {"mistral_7b_instruct": "https://huggingface.co/mistralai/Mistral-7B-Instruct-v0.2", "llama3_8b_instruct": "https://huggingface.co/meta-llama/Meta-Llama-3-8B-Instruct", "qwen2_5_7b_instruct": "https://huggingface.co/Qwen/Qwen2.5-7B-Instruct", "gemma2_9b_it": "https://huggingface.co/google/gemma-2-9b-it", "mpt_7b_instruct": "https://huggingface.co/mosaicml/mpt-7b-instruct"}, "compatibility_matrix": {"python_versions": ["3.8", "3.9", "3.10", "3.11"], "cuda_versions": ["11.8", "12.1", "12.2", "12.8"], "pytorch_versions": ["2.0.1", "2.1.0", "2.2.0", "2.4.0"], "transformers_versions": ["4.35.0", "4.36.0", "4.37.0", "4.38.0"]}}