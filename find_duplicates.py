#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to find duplicate Python files in the knowledge_app project.
This will identify files with identical content and similar names.
"""

import os
import hashlib
from pathlib import Path
from collections import defaultdict
import difflib

def get_file_hash(file_path):
    """Get MD5 hash of file content"""
    try:
        with open(file_path, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return None

def get_file_content(file_path):
    """Get file content as string"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='latin-1') as f:
                return f.read()
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
            return ""
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return ""

def find_duplicate_files():
    """Find duplicate Python files"""
    project_root = Path(__file__).parent
    python_files = []
    
    # Get all .py files recursively
    for py_file in project_root.rglob("*.py"):
        if "__pycache__" not in str(py_file):
            python_files.append(py_file)
    
    print(f"Analyzing {len(python_files)} Python files for duplicates...")
    
    # Group files by hash (exact duplicates)
    hash_groups = defaultdict(list)
    
    for py_file in python_files:
        file_hash = get_file_hash(py_file)
        if file_hash:
            hash_groups[file_hash].append(py_file)
    
    # Find exact duplicates
    exact_duplicates = {h: files for h, files in hash_groups.items() if len(files) > 1}
    
    print(f"\n=== EXACT DUPLICATES (identical content) ===")
    if exact_duplicates:
        for file_hash, files in exact_duplicates.items():
            print(f"\nDuplicate group (hash: {file_hash[:8]}...):")
            for file in files:
                rel_path = file.relative_to(project_root)
                size = file.stat().st_size
                print(f"  - {rel_path} ({size} bytes)")
    else:
        print("No exact duplicates found!")
    
    # Find similar files by name
    print(f"\n=== SIMILAR FILE NAMES ===")
    similar_names = defaultdict(list)
    
    for py_file in python_files:
        name = py_file.name.lower()
        # Group by similar names
        base_name = name.replace('_', '').replace('-', '')
        similar_names[base_name].append(py_file)
    
    similar_groups = {name: files for name, files in similar_names.items() if len(files) > 1}
    
    if similar_groups:
        for base_name, files in similar_groups.items():
            if len(files) > 1:
                print(f"\nSimilar names group '{base_name}':")
                for file in files:
                    rel_path = file.relative_to(project_root)
                    size = file.stat().st_size
                    print(f"  - {rel_path} ({size} bytes)")
    else:
        print("No similar file names found!")
    
    # Find files with very similar content (90%+ similarity)
    print(f"\n=== SIMILAR CONTENT (90%+ similarity) ===")
    
    file_contents = {}
    for py_file in python_files:
        content = get_file_content(py_file)
        if content and len(content) > 100:  # Only check files with substantial content
            file_contents[py_file] = content
    
    similar_content = []
    files_list = list(file_contents.keys())
    
    for i, file1 in enumerate(files_list):
        for file2 in files_list[i+1:]:
            content1 = file_contents[file1]
            content2 = file_contents[file2]
            
            # Calculate similarity
            similarity = difflib.SequenceMatcher(None, content1, content2).ratio()
            
            if similarity > 0.9:  # 90% similar
                similar_content.append((file1, file2, similarity))
    
    if similar_content:
        for file1, file2, similarity in similar_content:
            rel_path1 = file1.relative_to(project_root)
            rel_path2 = file2.relative_to(project_root)
            print(f"\n{similarity:.1%} similar:")
            print(f"  - {rel_path1}")
            print(f"  - {rel_path2}")
    else:
        print("No files with 90%+ similar content found!")
    
    return exact_duplicates, similar_groups, similar_content

def generate_removal_script(exact_duplicates):
    """Generate a script to remove duplicate files"""
    if not exact_duplicates:
        return
    
    script_content = "#!/usr/bin/env python3\n"
    script_content += '"""\nScript to remove duplicate files\n"""\n\n'
    script_content += "import os\nfrom pathlib import Path\n\n"
    script_content += "def remove_duplicates():\n"
    script_content += '    """Remove duplicate files (keeping the first one in each group)"""\n'
    script_content += "    project_root = Path(__file__).parent\n\n"
    
    for file_hash, files in exact_duplicates.items():
        script_content += f"    # Duplicate group {file_hash[:8]}...\n"
        script_content += f"    # Keeping: {files[0].name}\n"
        for file_to_remove in files[1:]:
            rel_path = file_to_remove.relative_to(Path(__file__).parent)
            script_content += f'    duplicate_file = project_root / "{rel_path}"\n'
            script_content += f'    if duplicate_file.exists():\n'
            script_content += f'        print(f"Removing duplicate: {{duplicate_file}}")\n'
            script_content += f'        duplicate_file.unlink()\n'
        script_content += "\n"
    
    script_content += 'if __name__ == "__main__":\n'
    script_content += '    remove_duplicates()\n'
    script_content += '    print("Duplicate removal completed!")\n'
    
    with open("remove_duplicates.py", "w") as f:
        f.write(script_content)
    
    print(f"\nGenerated removal script: remove_duplicates.py")

if __name__ == "__main__":
    exact_duplicates, similar_groups, similar_content = find_duplicate_files()
    
    if exact_duplicates:
        generate_removal_script(exact_duplicates)
        print(f"\nFound {len(exact_duplicates)} groups of exact duplicates!")
        print("Run 'python remove_duplicates.py' to remove them.")
    else:
        print("\nNo exact duplicates found - your codebase is clean!")
