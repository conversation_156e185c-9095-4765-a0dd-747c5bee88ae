#!/usr/bin/env python3
"""
Main Window MVC Specific Import Test

This script investigates exactly what in main_window_mvc is causing torch to be imported.
"""

import sys
import os

# Add the src directory to path for local imports
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

def test_main_window_mvc_step_by_step():
    """Test main_window_mvc imports step by step"""
    
    heavy_modules = ['torch', 'transformers', 'peft', 'datasets']
    
    print("🔍 Testing main_window_mvc imports step by step...")
    
    # Step 1: Check initial state
    print("\n📦 Step 1: Initial state")
    before = [m for m in heavy_modules if m in sys.modules]
    print(f"Heavy modules before: {before}")
    
    # Step 2: Import base MVC
    print("\n📦 Step 2: Import base MVC")
    before = [m for m in heavy_modules if m in sys.modules]
    from knowledge_app.ui.mvc.base_mvc import BaseModel, BaseView, BaseController
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after base_mvc: {after}")
    if newly_imported:
        print(f"⚠️ base_mvc imported: {newly_imported}")
    
    # Step 3: Import design system
    print("\n📦 Step 3: Import design system")
    before = after
    from knowledge_app.ui.enterprise_design_system import get_design_system, Theme
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after design_system: {after}")
    if newly_imported:
        print(f"⚠️ design_system imported: {newly_imported}")
    
    # Step 4: Import style manager
    print("\n📦 Step 4: Import style manager")
    before = after
    from knowledge_app.ui.enterprise_style_manager import get_style_manager, set_app_theme
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after style_manager: {after}")
    if newly_imported:
        print(f"⚠️ style_manager imported: {newly_imported}")
    
    # Step 5: Import MainWindowModel specifically
    print("\n📦 Step 5: Import MainWindowModel")
    before = after
    from knowledge_app.ui.mvc.main_window_mvc import MainWindowModel
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after MainWindowModel: {after}")
    if newly_imported:
        print(f"⚠️ MainWindowModel imported: {newly_imported}")
    
    # Step 6: Import MainWindowView
    print("\n📦 Step 6: Import MainWindowView")
    before = after
    from knowledge_app.ui.mvc.main_window_mvc import MainWindowView
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after MainWindowView: {after}")
    if newly_imported:
        print(f"⚠️ MainWindowView imported: {newly_imported}")
    
    # Step 7: Import MainWindowController
    print("\n📦 Step 7: Import MainWindowController")
    before = after
    from knowledge_app.ui.mvc.main_window_mvc import MainWindowController
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after MainWindowController: {after}")
    if newly_imported:
        print(f"⚠️ MainWindowController imported: {newly_imported}")
    
    return newly_imported

def test_main_window_mvc_imports():
    """Test what imports main_window_mvc is doing"""
    
    heavy_modules = ['torch', 'transformers', 'peft', 'datasets']
    
    print("🔍 Testing what main_window_mvc imports...")
    
    # Check what modules are loaded when we import the module
    print("Modules before importing main_window_mvc:")
    before_modules = set(sys.modules.keys())
    before_heavy = [m for m in heavy_modules if m in sys.modules]
    print(f"Heavy modules before: {before_heavy}")
    
    # Import the module
    import knowledge_app.ui.mvc.main_window_mvc
    
    after_modules = set(sys.modules.keys())
    after_heavy = [m for m in heavy_modules if m in sys.modules]
    newly_imported_heavy = [m for m in after_heavy if m not in before_heavy]
    
    print(f"Heavy modules after: {after_heavy}")
    if newly_imported_heavy:
        print(f"⚠️ main_window_mvc imported: {newly_imported_heavy}")
    
    # Check what new modules were loaded
    new_modules = after_modules - before_modules
    knowledge_app_modules = [m for m in new_modules if 'knowledge_app' in m]
    print(f"New knowledge_app modules: {knowledge_app_modules}")
    
    # Look for any torch-related modules
    torch_modules = [m for m in new_modules if 'torch' in m.lower()]
    if torch_modules:
        print(f"Torch-related modules found: {torch_modules[:10]}...")  # Show first 10
    
    return newly_imported_heavy

def main():
    """Run the specific test"""
    print("🚀 Starting main_window_mvc specific import test...")
    print("=" * 80)
    
    # Test step by step
    step_by_step_culprits = test_main_window_mvc_step_by_step()
    
    print("\n" + "=" * 40)
    
    # Test module imports
    module_import_culprits = test_main_window_mvc_imports()
    
    print("\n" + "=" * 80)
    print("📊 MAIN WINDOW MVC TEST RESULTS")
    print("=" * 80)
    
    if step_by_step_culprits or module_import_culprits:
        print(f"❌ Heavy modules imported:")
        if step_by_step_culprits:
            print(f"   Step by step: {step_by_step_culprits}")
        if module_import_culprits:
            print(f"   Module import: {module_import_culprits}")
        return 1
    else:
        print("✅ No heavy modules imported!")
        return 0

if __name__ == "__main__":
    sys.exit(main())
