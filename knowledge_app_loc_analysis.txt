================================================================================
🐍 KNOWLEDGE APP - PYTHON LINES OF CODE ANALYSIS
================================================================================
📅 Generated: 2025-06-12 10:01:12
📁 Project Root: C:\shared folder\knowledge_app

📊 SUMMARY
----------------------------------------
📄 Total Python Files: 200
📝 Total Lines: 58,176
💻 Pure Code Lines: 35,276
💬 Comment/Docstring Lines: 12,846
⬜ Blank Lines: 10,054

📈 BREAKDOWN
----------------------------------------
💻 Code: 60.6%
💬 Comments: 22.1%
⬜ Blank: 17.3%

🏆 TOP 20 FILES BY CODE LINES
----------------------------------------
 1. src\knowledge_app\ui\settings_menu\settings_menu.py
    💻 1,191 code | 💬 703 comments | 📝 2,201 total
 2. src\knowledge_app\ui\main_window.py
    💻 1,113 code | 💬 371 comments | 📝 1,792 total
 3. src\knowledge_app\core\fire_estimator.py
    💻 1,069 code | 💬 220 comments | 📝 1,557 total
 4. src\knowledge_app\core\local_model_inference.py
    💻 787 code | 💬 208 comments | 📝 1,198 total
 5. advanced_dependency_manager.py
    💻 785 code | 💬 158 comments | 📝 1,140 total
 6. src\knowledge_app\core\real_7b_trainer.py
    💻 761 code | 💬 154 comments | 📝 1,120 total
 7. src\knowledge_app\core\model_manager.py
    💻 688 code | 💬 209 comments | 📝 1,083 total
 8. src\knowledge_app\ui\training_dialog.py
    💻 611 code | 💬 271 comments | 📝 1,081 total
 9. src\knowledge_app\quiz_screen.py
    💻 590 code | 💬 188 comments | 📝 952 total
10. src\knowledge_app\ui\professional_settings_screen.py
    💻 546 code | 💬 225 comments | 📝 918 total
11. tests\gui_automation_debugger.py
    💻 503 code | 💬 92 comments | 📝 728 total
12. src\knowledge_app\core\instant_mcq_generator.py
    💻 467 code | 💬 94 comments | 📝 677 total
13. src\knowledge_app\core\real_7b_config.py
    💻 442 code | 💬 67 comments | 📝 570 total
14. src\knowledge_app\core\offline_mcq_generator.py
    💻 401 code | 💬 107 comments | 📝 617 total
15. src\knowledge_app\core\document_processor.py
    💻 382 code | 💬 58 comments | 📝 543 total
16. src\knowledge_app\core\inference.py
    💻 382 code | 💬 112 comments | 📝 583 total
17. src\knowledge_app\core\attention_optimizer.py
    💻 357 code | 💬 92 comments | 📝 542 total
18. src\knowledge_app\ui\settings_menu\model_download_dialog.py
    💻 355 code | 💬 136 comments | 📝 559 total
19. src\knowledge_app\ui\seductive_main_menu.py
    💻 353 code | 💬 193 comments | 📝 652 total
20. src\knowledge_app\ui\mvc\main_window_mvc.py
    💻 351 code | 💬 125 comments | 📝 594 total

🚫 EXCLUDED FROM COUNT
----------------------------------------
• Test files (test_*.py, *_test.py, conftest.py)
• Cache/build directories (__pycache__, .pytest_cache, etc.)
• Virtual environments (venv, .venv, env)
• Configuration files (setup.py, requirements.txt, etc.)
• Empty __init__.py files
• Documentation and data directories

================================================================================
🎯 PURE PYTHON CODE LINES: 35,276
================================================================================