#!/usr/bin/env python3
"""
Test script to verify the quiz generation fix.
This script tests the synchronous question generation pipeline.
"""

import sys
import os
import logging

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_mcq_manager_instant_generation():
    """Test MCQ manager instant generation"""
    try:
        logger.info("🧪 Testing MCQ manager instant generation...")
        
        from knowledge_app.core.mcq_manager import get_mcq_manager
        from knowledge_app.core.config_manager import get_config
        
        # Get configuration and MCQ manager
        config = get_config()
        mcq_manager = get_mcq_manager(config)
        
        # Enable instant mode
        mcq_manager.set_instant_mode(True)
        
        # Check if instant generation is available
        instant_available = mcq_manager.is_instant_available()
        logger.info(f"⚡ Instant generation available: {instant_available}")
        
        if instant_available:
            # Test synchronous generation
            import asyncio
            
            context = "Generate a multiple choice question about physics. The question should be suitable for medium difficulty learning."
            difficulty = "medium"
            
            try:
                # Test async generation
                result = asyncio.run(mcq_manager.generate_quiz_async(context, difficulty))
                
                if result and result.get('question'):
                    logger.info("✅ MCQ manager generated question successfully!")
                    logger.info(f"📝 Question: {result['question'][:100]}...")
                    logger.info(f"🎯 Options: {len(result.get('options', []))} options")
                    logger.info(f"✅ Correct answer: {result.get('correct_answer', 'N/A')}")
                    return True
                else:
                    logger.error("❌ MCQ manager returned empty result")
                    return False
                    
            except Exception as e:
                logger.error(f"❌ MCQ manager generation failed: {e}")
                return False
        else:
            logger.error("❌ Instant generation not available")
            return False
            
    except Exception as e:
        logger.error(f"❌ MCQ manager test failed: {e}")
        return False

def test_main_window_sync_generation():
    """Test main window synchronous question generation"""
    try:
        logger.info("🧪 Testing main window sync generation...")
        
        # Import Qt modules
        from PyQt5.QtWidgets import QApplication
        from knowledge_app.ui.main_window import MainWindow
        
        # Create minimal Qt application
        app = QApplication([])
        
        # Create main window
        main_window = MainWindow()
        
        # Set up quiz parameters
        main_window.current_topic = "physics"
        main_window.current_mode = "Casual"
        main_window.current_submode = "Multiple Choice"
        
        # Test synchronous question generation
        question_data = main_window.get_next_question_sync()
        
        if question_data and question_data.get('question'):
            logger.info("✅ Main window sync generation successful!")
            logger.info(f"📝 Question: {question_data['question'][:100]}...")
            logger.info(f"🎯 Options: {len(question_data.get('options', []))} options")
            logger.info(f"✅ Correct answer: {question_data.get('correct_answer', 'N/A')}")
            logger.info(f"🔧 Generation method: {question_data.get('generation_method', 'unknown')}")
            return True
        else:
            logger.error("❌ Main window sync generation returned empty result")
            return False
            
    except Exception as e:
        logger.error(f"❌ Main window sync generation test failed: {e}")
        return False

def test_quiz_controller_integration():
    """Test quiz controller integration with main window"""
    try:
        logger.info("🧪 Testing quiz controller integration...")
        
        # Import Qt modules
        from PyQt5.QtWidgets import QApplication
        from knowledge_app.ui.main_window import MainWindow
        from knowledge_app.ui.controllers.quiz_controller import QuizController
        
        # Create minimal Qt application
        app = QApplication([])
        
        # Create main window
        main_window = MainWindow()
        
        # Set up quiz parameters
        main_window.current_topic = "biology"
        main_window.current_mode = "Serious"
        main_window.current_submode = "Multiple Choice"
        
        # Create quiz controller with main window as parent
        quiz_controller = QuizController(main_window)
        
        # Test question generation through controller
        question_data = quiz_controller._get_next_question()
        
        if question_data and question_data.get('question'):
            logger.info("✅ Quiz controller integration successful!")
            logger.info(f"📝 Question: {question_data['question'][:100]}...")
            logger.info(f"🎯 Options: {len(question_data.get('options', []))} options")
            logger.info(f"✅ Correct answer: {question_data.get('correct_answer', 'N/A')}")
            logger.info(f"🔧 Generation method: {question_data.get('generation_method', 'unknown')}")
            return True
        else:
            logger.error("❌ Quiz controller integration returned empty result")
            return False
            
    except Exception as e:
        logger.error(f"❌ Quiz controller integration test failed: {e}")
        return False

def main():
    """Run all tests"""
    logger.info("🚀 Starting quiz generation fix tests...")
    
    tests = [
        ("MCQ Manager Instant Generation", test_mcq_manager_instant_generation),
        ("Main Window Sync Generation", test_main_window_sync_generation),
        ("Quiz Controller Integration", test_quiz_controller_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 Running test: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"💥 {test_name}: CRASHED - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("📊 TEST SUMMARY")
    logger.info(f"{'='*60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status}: {test_name}")
    
    logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Quiz generation fix is working correctly.")
        return 0
    else:
        logger.error("💥 Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
