#!/usr/bin/env python3
"""
Comprehensive test to verify complete elimination of numpy array pydantic schema warnings
"""

import sys
import os
import logging
import warnings
from io import StringIO

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Capture all warnings
warnings.simplefilter("always")
warning_capture = StringIO()

# Set up logging to capture warnings
logging.basicConfig(
    level=logging.DEBUG,
    format='%(levelname)s - %(name)s - %(message)s',
    stream=warning_capture
)

def test_numpy_schema_comprehensive():
    """Comprehensive test for numpy array schema configuration"""
    print("🔧 Testing Comprehensive NumPy Array Schema Fix")
    print("=" * 60)
    
    # Capture initial state
    initial_warnings = warning_capture.getvalue()
    
    try:
        # Test 1: Basic pydantic configuration
        print("📋 Test 1: Basic Pydantic Configuration")
        from knowledge_app.core.pydantic_config import configure_pydantic_for_dataframes, NumpyArrayWrapper
        
        config_result = configure_pydantic_for_dataframes()
        print(f"✅ Configuration result: {config_result}")
        
        # Test 2: Numpy array wrapper
        print("\n📋 Test 2: NumpyArrayWrapper")
        import numpy as np
        test_array = np.array([1, 2, 3, 4, 5])
        
        wrapper = NumpyArrayWrapper(test_array)
        print(f"✅ Wrapper created: {type(wrapper)}")
        print(f"✅ Array access: {wrapper.array.shape}")
        
        # Test 3: Pydantic model with numpy array
        print("\n📋 Test 3: Pydantic Model with NumPy Array")
        from pydantic import BaseModel
        from knowledge_app.core.pydantic_config import RAGModelConfig
        
        class TestModel(RAGModelConfig):
            name: str
            data: np.ndarray = None
            wrapper_data: NumpyArrayWrapper = None
        
        # Test with numpy array
        model1 = TestModel(name="test1", data=test_array)
        print(f"✅ Model with numpy array: {model1.name}")
        
        # Test with wrapper
        model2 = TestModel(name="test2", wrapper_data=wrapper)
        print(f"✅ Model with wrapper: {model2.name}")
        
        # Test 4: RAG MCQ Generator initialization
        print("\n📋 Test 4: RAG MCQ Generator Initialization")
        from knowledge_app.core.rag_mcq_generator import RAGMCQGenerator
        
        generator = RAGMCQGenerator()
        print("✅ RAG MCQ generator created without warnings")
        
        # Test 5: Check for schema warnings
        print("\n📋 Test 5: Warning Analysis")
        all_output = warning_capture.getvalue()
        
        # Look for specific numpy schema warnings
        numpy_warnings = [
            line for line in all_output.split('\n') 
            if 'numpy.ndarray' in line and 'schema' in line.lower()
        ]
        
        pydantic_warnings = [
            line for line in all_output.split('\n')
            if 'pydantic' in line.lower() and 'schema' in line.lower() and 'numpy' in line.lower()
        ]
        
        print(f"📊 NumPy schema warnings found: {len(numpy_warnings)}")
        print(f"📊 Pydantic schema warnings found: {len(pydantic_warnings)}")
        
        if numpy_warnings:
            print("⚠️ NumPy schema warnings detected:")
            for warning in numpy_warnings[:3]:  # Show first 3
                print(f"   {warning}")
        
        if pydantic_warnings:
            print("⚠️ Pydantic schema warnings detected:")
            for warning in pydantic_warnings[:3]:  # Show first 3
                print(f"   {warning}")
        
        # Test 6: Direct numpy array schema test
        print("\n📋 Test 6: Direct NumPy Array Schema Test")
        try:
            from pydantic import TypeAdapter
            
            # This should not produce warnings
            adapter = TypeAdapter(np.ndarray)
            validated = adapter.validate_python(test_array)
            print(f"✅ TypeAdapter validation successful: {validated.shape}")
            
        except Exception as e:
            print(f"⚠️ TypeAdapter test failed: {e}")
        
        # Final assessment
        schema_warnings_found = len(numpy_warnings) > 0 or len(pydantic_warnings) > 0
        
        return not schema_warnings_found
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rag_engine_initialization():
    """Test RAG engine initialization without warnings"""
    print("\n🔧 Testing RAG Engine Initialization")
    print("=" * 60)
    
    # Capture warnings during RAG engine initialization
    warning_buffer = StringIO()
    warning_handler = logging.StreamHandler(warning_buffer)
    warning_handler.setLevel(logging.WARNING)
    
    rag_logger = logging.getLogger('knowledge_app.core.rag_mcq_generator')
    rag_logger.addHandler(warning_handler)
    
    try:
        # Initialize RAG MCQ generator
        from knowledge_app.core.rag_mcq_generator import RAGMCQGenerator
        
        generator = RAGMCQGenerator()
        result = generator.initialize()
        
        print(f"✅ RAG MCQ generator initialized: {result}")
        
        # Check for warnings
        warnings_output = warning_buffer.getvalue()
        schema_warnings = [
            line for line in warnings_output.split('\n')
            if 'numpy.ndarray' in line and 'schema' in line.lower()
        ]
        
        print(f"📊 Schema warnings during initialization: {len(schema_warnings)}")
        
        if schema_warnings:
            print("⚠️ Schema warnings found:")
            for warning in schema_warnings:
                print(f"   {warning}")
            return False
        else:
            print("✅ No schema warnings detected during RAG initialization")
            return True
            
    except Exception as e:
        print(f"❌ RAG engine test failed: {e}")
        return False
    finally:
        rag_logger.removeHandler(warning_handler)

def main():
    """Run all numpy schema fix tests"""
    print("🚀 Testing Complete NumPy Array Schema Fix")
    print("=" * 70)
    
    tests = [
        ("Comprehensive Schema Fix", test_numpy_schema_comprehensive),
        ("RAG Engine Initialization", test_rag_engine_initialization),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running: {test_name}")
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 70)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 70)
    if all_passed:
        print("🎉 All tests passed! NumPy array schema warnings eliminated!")
        return 0
    else:
        print("⚠️ Some tests failed. Schema warnings may still be present.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
