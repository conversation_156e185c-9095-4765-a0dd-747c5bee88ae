#!/usr/bin/env python3
"""
Training Metrics Import Test

This script tests exactly what's happening when we import training_metrics.
"""

import sys
import os

# Add src directory to path
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

def test_training_metrics_import():
    """Test what happens when we import training_metrics"""
    
    heavy_modules = ['torch', 'transformers', 'peft', 'datasets']
    
    print("🔍 Testing training_metrics import in detail...")
    
    # Check initial state
    print("\n📦 Initial state:")
    before = [m for m in heavy_modules if m in sys.modules]
    print(f"Heavy modules before: {before}")
    
    # Check what modules are already loaded
    print(f"Total modules loaded: {len(sys.modules)}")
    
    # Import training_metrics
    print("\n📦 Importing training_metrics...")
    
    try:
        from knowledge_app.core.training_metrics import TrainingMetrics
        print("✅ Successfully imported TrainingMetrics")
        
        # Check what happened
        after = [m for m in heavy_modules if m in sys.modules]
        newly_imported = [m for m in after if m not in before]
        
        print(f"Heavy modules after: {after}")
        if newly_imported:
            print(f"⚠️ Newly imported heavy modules: {newly_imported}")
            
            # Check which modules were loaded
            print(f"Total modules loaded now: {len(sys.modules)}")
            
            # Look for torch-related modules
            torch_modules = [m for m in sys.modules.keys() if 'torch' in m.lower()]
            print(f"Torch-related modules: {torch_modules}")
            
            # Look for fire-related modules
            fire_modules = [m for m in sys.modules.keys() if 'fire' in m.lower()]
            print(f"Fire-related modules: {fire_modules}")
            
            # Look for knowledge_app modules
            app_modules = [m for m in sys.modules.keys() if 'knowledge_app' in m]
            print(f"Knowledge app modules: {app_modules}")
            
        else:
            print("✅ No heavy modules imported")
            
    except Exception as e:
        print(f"❌ Failed to import training_metrics: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Run the test"""
    print("🚀 Starting training_metrics import test...")
    print("=" * 60)
    
    test_training_metrics_import()
    
    print("\n" + "=" * 60)
    print("📊 TEST COMPLETE")
    print("=" * 60)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
