#!/usr/bin/env python3
"""
Test script to verify critical UI and system fixes in Knowledge App.

This script tests:
1. QPainter threading violations fixes
2. Screen navigation system fixes
3. RAG engine initialization fixes
4. Quiz screen availability fixes
5. Memory management improvements
"""

import sys
import os
import logging
import traceback
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_painter_threading_fixes():
    """Test QPainter threading fixes"""
    logger.info("🎨 Testing QPainter threading fixes...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QWidget
        from PyQt5.QtCore import QThread
        
        app = QApplication.instance() or QApplication(sys.argv)
        
        # Test that we're on the main thread
        current_thread = QThread.currentThread()
        main_thread = app.thread()
        
        if current_thread == main_thread:
            logger.info("✅ QPainter threading fixes verified - running on main thread")
            return True
        else:
            logger.error("❌ QPainter threading issue - not on main thread")
            return False
            
    except Exception as e:
        logger.error(f"❌ QPainter threading test failed: {e}")
        return False

def test_screen_navigation_fixes():
    """Test screen navigation system fixes"""
    logger.info("🧭 Testing screen navigation fixes...")

    try:
        # Test that the MVC main window can be imported
        from src.knowledge_app.ui.mvc.main_window_mvc import MainWindowView
        logger.info("✅ MainWindowView import successful")

        # Test that the default screen creation method exists
        main_window_class = MainWindowView
        if hasattr(main_window_class, '_create_default_screens'):
            logger.info("✅ Default screen creation method available")
        else:
            logger.error("❌ Default screen creation method missing")
            return False

        # Test that the initialize method has proper error handling
        if hasattr(main_window_class, 'initialize'):
            logger.info("✅ Initialize method available")
        else:
            logger.error("❌ Initialize method missing")
            return False

        return True

    except Exception as e:
        logger.error(f"❌ Screen navigation test failed: {e}")
        logger.error(traceback.format_exc())
        return False

def test_rag_engine_fixes():
    """Test RAG engine initialization fixes"""
    logger.info("🔍 Testing RAG engine fixes...")
    
    try:
        from src.knowledge_app.core.rag_mcq_generator import RAGMCQGenerator
        
        # Test RAG MCQ generator initialization
        rag_generator = RAGMCQGenerator()
        
        # Test initialization with error handling
        if rag_generator.initialize():
            logger.info("✅ RAG MCQ generator initialized successfully")
        else:
            logger.info("✅ RAG MCQ generator handled initialization failure gracefully")
        
        # Test that it's marked as initialized even if components failed
        if rag_generator.is_initialized:
            logger.info("✅ RAG generator marked as initialized for graceful degradation")
            return True
        else:
            logger.error("❌ RAG generator not marked as initialized")
            return False
            
    except Exception as e:
        logger.error(f"❌ RAG engine test failed: {e}")
        logger.error(traceback.format_exc())
        return False

def test_quiz_screen_fixes():
    """Test quiz screen availability fixes"""
    logger.info("🎯 Testing quiz screen fixes...")
    
    try:
        from src.knowledge_app.quiz_screen import QuizScreen
        from PyQt5.QtWidgets import QApplication, QWidget
        
        app = QApplication.instance() or QApplication(sys.argv)
        
        # Create a dummy parent
        parent = QWidget()
        
        # Create quiz screen
        quiz_screen = QuizScreen(parent=parent)
        
        # Test quiz availability
        if hasattr(quiz_screen, 'quiz_available') and quiz_screen.quiz_available:
            logger.info("✅ Quiz screen marked as available")
        else:
            logger.error("❌ Quiz screen not marked as available")
            return False
        
        # Test start_quiz method
        if hasattr(quiz_screen, 'start_quiz'):
            result = quiz_screen.start_quiz()
            logger.info("✅ Quiz screen has start_quiz method")
            return True
        else:
            logger.error("❌ Quiz screen missing start_quiz method")
            return False
            
    except Exception as e:
        logger.error(f"❌ Quiz screen test failed: {e}")
        logger.error(traceback.format_exc())
        return False

def test_memory_management():
    """Test memory management improvements"""
    logger.info("🧠 Testing memory management...")
    
    try:
        import psutil
        import gc
        
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        logger.info(f"Initial memory usage: {initial_memory:.1f} MB")
        
        # Force garbage collection
        gc.collect()
        
        # Get memory after cleanup
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        logger.info(f"Memory after cleanup: {final_memory:.1f} MB")
        
        # Check if memory usage is reasonable (less than 500MB for testing)
        if final_memory < 500:
            logger.info("✅ Memory usage within reasonable limits")
            return True
        else:
            logger.warning(f"⚠️ High memory usage: {final_memory:.1f} MB")
            return True  # Not critical failure for testing
            
    except Exception as e:
        logger.error(f"❌ Memory management test failed: {e}")
        return False

def test_accessibility_singleton():
    """Test accessibility manager singleton pattern"""
    logger.info("♿ Testing accessibility manager singleton...")

    try:
        from src.knowledge_app.ui.accessibility_manager import get_accessibility_manager, reset_accessibility_manager

        # Reset for clean test
        reset_accessibility_manager()

        # Get first instance
        manager1 = get_accessibility_manager()

        # Get second instance
        manager2 = get_accessibility_manager()

        # They should be the same object
        if manager1 is manager2:
            logger.info("✅ Accessibility manager singleton working correctly")
            return True
        else:
            logger.error("❌ Accessibility manager singleton failed - different instances")
            return False

    except Exception as e:
        logger.error(f"❌ Accessibility singleton test failed: {e}")
        return False

def test_pydantic_configuration():
    """Test pydantic configuration singleton"""
    logger.info("📋 Testing pydantic configuration...")

    try:
        from src.knowledge_app.core.pydantic_config import ensure_pydantic_configured

        # This should not cause duplicate initialization
        ensure_pydantic_configured()
        ensure_pydantic_configured()
        ensure_pydantic_configured()

        logger.info("✅ Pydantic configuration singleton working")
        return True

    except Exception as e:
        logger.error(f"❌ Pydantic configuration test failed: {e}")
        return False

def main():
    """Run all critical fix tests"""
    logger.info("🚀 Starting critical fixes verification...")

    tests = [
        ("QPainter Threading Fixes", test_painter_threading_fixes),
        ("Screen Navigation Fixes", test_screen_navigation_fixes),
        ("RAG Engine Fixes", test_rag_engine_fixes),
        ("Quiz Screen Fixes", test_quiz_screen_fixes),
        ("Accessibility Singleton", test_accessibility_singleton),
        ("Pydantic Configuration", test_pydantic_configuration),
        ("Memory Management", test_memory_management),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: CRASHED - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All critical fixes verified successfully!")
        return 0
    else:
        logger.error(f"⚠️ {total - passed} tests failed - review needed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
