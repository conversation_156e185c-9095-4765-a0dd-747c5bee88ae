Collecting sentence-transformers
  Using cached sentence_transformers-4.1.0-py3-none-any.whl (345 kB)
Collecting faiss-cpu
  Using cached faiss_cpu-1.11.0-cp310-cp310-win_amd64.whl (15.0 MB)
Requirement already satisfied: tqdm in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from sentence-transformers) (4.65.0)
Requirement already satisfied: Pillow in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from sentence-transformers) (10.0.0)
Requirement already satisfied: scikit-learn in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from sentence-transformers) (1.3.0)
Requirement already satisfied: scipy in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from sentence-transformers) (1.15.3)
Requirement already satisfied: typing_extensions>=4.5.0 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from sentence-transformers) (4.14.0)
Requirement already satisfied: torch>=1.11.0 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from sentence-transformers) (2.1.2)
Requirement already satisfied: huggingface-hub>=0.20.0 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from sentence-transformers) (0.32.4)
Requirement already satisfied: transformers<5.0.0,>=4.41.0 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from sentence-transformers) (4.52.4)
Requirement already satisfied: numpy<3.0,>=1.25.0 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from faiss-cpu) (1.26.4)
Requirement already satisfied: packaging in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from faiss-cpu) (25.0)
Requirement already satisfied: pyyaml>=5.1 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (6.0.2)
Requirement already satisfied: requests in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (2.31.0)
Requirement already satisfied: fsspec>=2023.5.0 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (2025.5.1)
Requirement already satisfied: filelock in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (3.18.0)
Requirement already satisfied: jinja2 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from torch>=1.11.0->sentence-transformers) (3.1.6)
Requirement already satisfied: networkx in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from torch>=1.11.0->sentence-transformers) (3.4.2)
Requirement already satisfied: sympy in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from torch>=1.11.0->sentence-transformers) (1.14.0)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from tqdm->sentence-transformers) (0.4.6)
Requirement already satisfied: regex!=2019.12.17 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (2024.11.6)
Requirement already satisfied: tokenizers<0.22,>=0.21 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (0.21.1)
Requirement already satisfied: safetensors>=0.4.3 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (0.5.3)
Requirement already satisfied: MarkupSafe>=2.0 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from jinja2->torch>=1.11.0->sentence-transformers) (3.0.2)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from requests->huggingface-hub>=0.20.0->sentence-transformers) (2025.4.26)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from requests->huggingface-hub>=0.20.0->sentence-transformers) (3.10)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from requests->huggingface-hub>=0.20.0->sentence-transformers) (3.2.0)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from requests->huggingface-hub>=0.20.0->sentence-transformers) (2.4.0)
Requirement already satisfied: joblib>=1.1.1 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from scikit-learn->sentence-transformers) (1.5.1)
Requirement already satisfied: threadpoolctl>=2.0.0 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from scikit-learn->sentence-transformers) (3.6.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages (from sympy->torch>=1.11.0->sentence-transformers) (1.3.0)
Installing collected packages: sentence-transformers, faiss-cpu
Successfully installed faiss-cpu-1.11.0 sentence-transformers-4.1.0
