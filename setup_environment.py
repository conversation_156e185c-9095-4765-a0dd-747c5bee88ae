#!/usr/bin/env python3
"""
Environment Setup Script for Knowledge App

This script performs automatic setup of the Knowledge App environment:
1. Detects the Python environment
2. Creates and configures virtual environment
3. Installs all required dependencies using pre-compiled wheels when available
4. Verifies the installation
"""

import os
import sys
import subprocess
import platform
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler(),
              logging.FileHandler("setup_environment.log", encoding='utf-8')]
)
logger = logging.getLogger(__name__)

def run_pip_install(package, description=None):
    """Run pip install for a package and log the result."""
    if description is None:
        description = f"Installing {package}"
    
    logger.info(description)
    cmd = [sys.executable, "-m", "pip", "install", package, "--prefer-binary"]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info(f"{description} - Success")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"{description} - Failed")
        logger.error(f"Error output: {e.stderr}")
        return False

def setup_virtual_environment():
    """Create and configure virtual environment."""
    venv_path = Path(".venv")
    
    if not venv_path.exists():
        logger.info("Creating virtual environment...")
        try:
            subprocess.run(
                [sys.executable, "-m", "venv", str(venv_path)],
                check=True,
                capture_output=True,
                text=True
            )
            logger.info("Virtual environment created successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to create virtual environment: {e}")
            return False
    return True

def setup_project_structure():
    """Set up the project directory structure."""
    directories = [
        "data/cache/images",
        "data/user_data",
        "data/models",
        "data/uploaded_books",
        "logs",
        "models/local",
        "models/cloud"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"Created directory: {directory}")

def install_dependencies():
    """Install all required dependencies."""
    logger.info("Installing dependencies...")
    
    # First install basic dependencies
    basic_deps = [
        "wheel",  # Required for building some packages
        "setuptools>=65.5.1",  # Required for building some packages
        "PyQt5==5.15.9",  # UI framework
        "PyQt5-sip==12.11.0",  # Required for PyQt5
        "numpy==1.26.4",  # Core numerical computations
        "pandas==2.2.1",  # Data manipulation
        "matplotlib==3.9.0",  # Plotting
        "scikit-learn==1.3.2",  # Machine learning
        "transformers==4.31.0",  # NLP models
    ]
    
    success = True
    for dep in basic_deps:
        if not run_pip_install(dep):
            success = False
    
    # Install PyTorch CPU version separately
    torch_cmd = [sys.executable, "-m", "pip", "install",
                "--index-url", "https://download.pytorch.org/whl/cpu",
                "torch==2.1.0+cpu",
                "torchvision==0.12.0+cpu"]
    
    try:
        logger.info("Installing PyTorch (CPU version)...")
        result = subprocess.run(torch_cmd, check=True, capture_output=True, text=True)
        logger.info("Installing PyTorch (CPU version) - Success")
    except subprocess.CalledProcessError as e:
        logger.error("Installing PyTorch (CPU version) - Failed")
        logger.error(f"Error output: {e.stderr}")
        success = False
    
    # Install remaining packages from requirements.txt
    requirements_path = Path("requirements.txt")
    if requirements_path.exists():
        if not run_pip_install(f"-r {requirements_path}", "Installing remaining packages"):
            success = False
    
    return success

def verify_installation():
    """Verify that critical packages are properly installed."""
    logger.info("Verifying installation")
    
    critical_packages = ["numpy", "PyQt5", "torch", "transformers"]
    all_installed = True
    
    for package in critical_packages:
        logger.info(f"Checking {package} installation...")
        try:
            __import__(package.lower())
            logger.info(f"[OK] {package} installed successfully")
        except ImportError as e:
            logger.error(f"[ERROR] {package} not properly installed: {e}")
            all_installed = False
    
    return all_installed

def main():
    """Main entry point for the setup script."""
    logger.info("Starting Knowledge App environment setup")
    logger.info(f"Python version: {platform.python_version()}")
    logger.info(f"Platform: {platform.system()} {platform.version()}")
    
    # Set up project structure
    setup_project_structure()
    
    # Set up virtual environment
    if not setup_virtual_environment():
        logger.error("Failed to set up virtual environment")
        return False
    
    # Install dependencies
    if not install_dependencies():
        logger.error("Failed to install dependencies")
        return False
    
    # Verify installation
    if verify_installation():
        logger.info("[OK] Environment setup completed successfully!")
        logger.info("You can now run 'python main.py' to start the Knowledge App.")
        return True
    else:
        logger.warning("[WARNING] Some packages may not be properly installed.")
        logger.warning("Try running the app anyway with 'python main.py', or run this script again.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
