# Byte-compiled / cache
__pycache__/
*.py[cod]
*.pyo

# Virtual environments
venv/
env/
ENV/
.venv/

# Jupyter Notebooks Checkpoints
.ipynb_checkpoints

# System Files
.DS_Store
Thumbs.db

# Logs & local settings
*.log
*.sqlite3
*.db
*.pid

# IDEs
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Python testing
coverage/
*.cover
*.py,cover
.hypothesis/

# Environment files
.env
.env.*

# Compressed files
*.zip
*.tar.gz
*.rar

# Build artifacts
build/
dist/
*.egg-info/
.eggs/

# Image caching folders
image_cache/
extracted_training_images/

# Mac specific
*.DS_Store

q u i z _ m o d e l / * . s a f e t e n s o r s 
 
 u p l o a d e d _ b o o k s / * . p d f 
 
 