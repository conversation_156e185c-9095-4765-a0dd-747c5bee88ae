#!/usr/bin/env python3
"""
Import Tracking Test

This script tracks exactly which modules are importing heavy ML libraries
during startup to identify the remaining sources of eager imports.
"""

import sys
import os
import logging

# Add src directory to path
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def track_heavy_imports():
    """Track when heavy ML libraries get imported"""
    
    # Track which modules are imported
    heavy_modules = ['torch', 'transformers', 'peft', 'datasets']
    
    logger.info("🔍 Starting import tracking...")
    logger.info(f"📦 Initial heavy modules in sys.modules: {[m for m in heavy_modules if m in sys.modules]}")
    
    # Test importing specific modules one by one
    test_modules = [
        'knowledge_app.core.fire_estimator',
        'knowledge_app.core.config_manager', 
        'knowledge_app.ui.mvc.main_window_mvc',
        'knowledge_app.core.rag_mcq_generator',
        'knowledge_app.core.pydantic_config',
        'knowledge_app.ui.enterprise_main_window',
    ]
    
    for module_name in test_modules:
        logger.info(f"\n🔄 Testing import of: {module_name}")
        
        # Check before import
        before = [m for m in heavy_modules if m in sys.modules]
        logger.info(f"  📦 Heavy modules before: {before}")
        
        try:
            # Import the module
            __import__(module_name)
            
            # Check after import
            after = [m for m in heavy_modules if m in sys.modules]
            newly_imported = [m for m in after if m not in before]
            
            logger.info(f"  📦 Heavy modules after: {after}")
            if newly_imported:
                logger.warning(f"  ⚠️ {module_name} imported: {newly_imported}")
            else:
                logger.info(f"  ✅ {module_name} did not import heavy modules")
                
        except Exception as e:
            logger.error(f"  ❌ Failed to import {module_name}: {e}")
    
    # Final check
    final_heavy = [m for m in heavy_modules if m in sys.modules]
    logger.info(f"\n📊 Final heavy modules imported: {final_heavy}")
    
    return final_heavy

def main():
    """Run import tracking test"""
    logger.info("🚀 Starting import tracking test...")
    logger.info("=" * 60)
    
    heavy_imports = track_heavy_imports()
    
    logger.info("\n" + "=" * 60)
    logger.info("📊 IMPORT TRACKING RESULTS")
    logger.info("=" * 60)
    
    if heavy_imports:
        logger.error(f"❌ Heavy modules imported during startup: {heavy_imports}")
        logger.error("⚠️ Need to find and fix the source of these imports")
        return 1
    else:
        logger.info("✅ No heavy modules imported during startup!")
        logger.info("🎉 Lazy loading is working correctly!")
        return 0

if __name__ == "__main__":
    sys.exit(main())
