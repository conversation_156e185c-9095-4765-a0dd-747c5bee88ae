#!/usr/bin/env python3
"""
Professional Accessibility Demo - Phase 1C Implementation

This script demonstrates the comprehensive accessibility features including:
- Complete keyboard navigation with Tab key support
- Screen reader optimization with rich accessible names
- High-contrast theme for visual accessibility
- Enhanced focus indicators and management
- WCAG 2.1 AA/AAA compliance features

Run this to experience the fully accessible application!
"""

import sys
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QPushButton, QFrame, QGridLayout, QLineEdit, QTextEdit,
    QComboBox, QCheckBox, QSpinBox, QSlider, QTabWidget, QGroupBox
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QKeySequence

# Import our accessibility components
from knowledge_app.ui.accessibility_manager import (
    ProfessionalAccessibilityManager, AccessibilityFeature, AccessibilityLevel
)
from knowledge_app.ui.professional_buttons import ProfessionalButton
from knowledge_app.ui.seductive_transitions import ProfessionalToastNotification

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProfessionalAccessibilityDemo(QMainWindow):
    """Demo window showcasing comprehensive accessibility features"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Professional Accessibility Demo - Phase 1C")
        self.setMinimumSize(1200, 800)
        
        # Initialize accessibility components
        self.accessibility_manager = None
        self.toast_system = None
        
        self.setup_ui()
        self.setup_accessibility()
        
    def setup_ui(self):
        """Set up the demo UI with comprehensive accessibility"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Title
        title = QLabel("♿ Professional Accessibility Demo - Phase 1C")
        title.setFont(QFont("Arial", 24, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #3B82F6; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # Instructions
        instructions = QLabel("""
        🎯 Try these accessibility features:
        • Tab/Shift+Tab: Navigate between elements
        • Enter/Space: Activate buttons
        • Ctrl+Alt+H: Toggle high-contrast theme
        • Ctrl+Alt+F: Toggle focus indicators
        • Ctrl+Alt+T: Toggle large text
        • F1: Show accessibility help
        """)
        instructions.setStyleSheet("background: #F3F4F6; padding: 16px; border-radius: 8px; color: #374151;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Create demo sections
        self.create_keyboard_navigation_demo(layout)
        self.create_accessibility_controls_demo(layout)
        self.create_form_accessibility_demo(layout)
        self.create_compliance_demo(layout)
        
    def create_keyboard_navigation_demo(self, parent_layout):
        """Create keyboard navigation demo section"""
        section = self.create_demo_section("⌨️ Keyboard Navigation & Focus Management")
        parent_layout.addWidget(section)
        
        content_layout = QGridLayout()
        section.layout().addLayout(content_layout)
        
        # Navigation buttons with proper tab order
        nav_buttons = [
            ("🏠 Main Menu", "Navigate to main menu", 0, 0),
            ("📝 Quiz Setup", "Set up a new quiz", 0, 1),
            ("⚙️ Settings", "Open application settings", 0, 2),
            ("🧠 Training", "Start AI model training", 1, 0),
            ("📊 Analytics", "View learning analytics", 1, 1),
            ("❓ Help", "Get help and support", 1, 2),
        ]
        
        previous_button = None
        for text, description, row, col in nav_buttons:
            button = ProfessionalButton(text, button_type="secondary")
            button.setAccessibleDescription(description)
            button.clicked.connect(lambda checked, t=text: self.demo_navigation(t))
            content_layout.addWidget(button, row, col)
            
            # Set tab order
            if previous_button:
                self.setTabOrder(previous_button, button)
            previous_button = button
        
    def create_accessibility_controls_demo(self, parent_layout):
        """Create accessibility controls demo section"""
        section = self.create_demo_section("🔧 Accessibility Features Control")
        parent_layout.addWidget(section)
        
        content_layout = QHBoxLayout()
        section.layout().addLayout(content_layout)
        
        # Accessibility toggle buttons
        high_contrast_btn = ProfessionalButton("Toggle High Contrast", button_type="primary")
        high_contrast_btn.setAccessibleDescription("Toggle high-contrast theme for better visibility")
        high_contrast_btn.clicked.connect(self.toggle_high_contrast)
        content_layout.addWidget(high_contrast_btn)
        
        focus_indicators_btn = ProfessionalButton("Toggle Focus Indicators", button_type="secondary")
        focus_indicators_btn.setAccessibleDescription("Toggle enhanced focus indicators")
        focus_indicators_btn.clicked.connect(self.toggle_focus_indicators)
        content_layout.addWidget(focus_indicators_btn)
        
        large_text_btn = ProfessionalButton("Toggle Large Text", button_type="secondary")
        large_text_btn.setAccessibleDescription("Toggle large text mode for better readability")
        large_text_btn.clicked.connect(self.toggle_large_text)
        content_layout.addWidget(large_text_btn)
        
        report_btn = ProfessionalButton("Accessibility Report", button_type="secondary")
        report_btn.setAccessibleDescription("Generate accessibility compliance report")
        report_btn.clicked.connect(self.show_accessibility_report)
        content_layout.addWidget(report_btn)
        
    def create_form_accessibility_demo(self, parent_layout):
        """Create form accessibility demo section"""
        section = self.create_demo_section("📝 Form Controls & Screen Reader Support")
        parent_layout.addWidget(section)
        
        # Create form with proper labels and accessibility
        form_layout = QGridLayout()
        section.layout().addLayout(form_layout)
        
        # Text input with label
        name_label = QLabel("Full Name:")
        name_input = QLineEdit()
        name_input.setAccessibleName("Full Name")
        name_input.setAccessibleDescription("Enter your full name")
        name_input.setPlaceholderText("Enter your full name")
        name_label.setBuddy(name_input)
        form_layout.addWidget(name_label, 0, 0)
        form_layout.addWidget(name_input, 0, 1)
        
        # Dropdown with label
        level_label = QLabel("Experience Level:")
        level_combo = QComboBox()
        level_combo.addItems(["Beginner", "Intermediate", "Advanced", "Expert"])
        level_combo.setAccessibleName("Experience Level")
        level_combo.setAccessibleDescription("Select your experience level")
        level_label.setBuddy(level_combo)
        form_layout.addWidget(level_label, 1, 0)
        form_layout.addWidget(level_combo, 1, 1)
        
        # Checkbox
        notifications_cb = QCheckBox("Enable notifications")
        notifications_cb.setAccessibleDescription("Enable or disable notifications")
        form_layout.addWidget(notifications_cb, 2, 0, 1, 2)
        
        # Number input
        age_label = QLabel("Age:")
        age_spin = QSpinBox()
        age_spin.setRange(13, 120)
        age_spin.setValue(25)
        age_spin.setAccessibleName("Age")
        age_spin.setAccessibleDescription("Enter your age")
        age_label.setBuddy(age_spin)
        form_layout.addWidget(age_label, 3, 0)
        form_layout.addWidget(age_spin, 3, 1)
        
        # Set proper tab order
        self.setTabOrder(name_input, level_combo)
        self.setTabOrder(level_combo, notifications_cb)
        self.setTabOrder(notifications_cb, age_spin)
        
    def create_compliance_demo(self, parent_layout):
        """Create WCAG compliance demo section"""
        section = self.create_demo_section("📋 WCAG 2.1 Compliance & Testing")
        parent_layout.addWidget(section)
        
        content_layout = QVBoxLayout()
        section.layout().addLayout(content_layout)
        
        # Compliance info
        compliance_info = QLabel("""
        ✅ WCAG 2.1 AA Compliance Features:
        • Keyboard navigation for all interactive elements
        • Proper focus management and visual indicators
        • Screen reader support with accessible names and descriptions
        • High-contrast theme option
        • Logical tab order and focus flow
        
        🎯 AAA Features:
        • Enhanced focus indicators
        • Large text mode
        • Reduced motion options
        """)
        compliance_info.setStyleSheet("""
            QLabel {
                background: #F0FDF4;
                border: 2px solid #10B981;
                border-radius: 8px;
                padding: 16px;
                color: #065F46;
                font-size: 14px;
            }
        """)
        compliance_info.setWordWrap(True)
        content_layout.addWidget(compliance_info)
        
        # Test buttons
        test_layout = QHBoxLayout()
        
        test_keyboard_btn = ProfessionalButton("Test Keyboard Navigation", button_type="secondary")
        test_keyboard_btn.setAccessibleDescription("Test keyboard navigation functionality")
        test_keyboard_btn.clicked.connect(self.test_keyboard_navigation)
        test_layout.addWidget(test_keyboard_btn)
        
        test_screen_reader_btn = ProfessionalButton("Test Screen Reader", button_type="secondary")
        test_screen_reader_btn.setAccessibleDescription("Test screen reader compatibility")
        test_screen_reader_btn.clicked.connect(self.test_screen_reader)
        test_layout.addWidget(test_screen_reader_btn)
        
        content_layout.addLayout(test_layout)
        
    def create_demo_section(self, title):
        """Create a demo section with title"""
        section = QFrame()
        section.setFrameStyle(QFrame.Box)
        section.setStyleSheet("""
            QFrame {
                border: 2px solid #E5E7EB;
                border-radius: 12px;
                background: #F9FAFB;
                padding: 16px;
            }
        """)
        
        layout = QVBoxLayout(section)
        
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: #374151; border: none; background: transparent;")
        title_label.setAccessibleName(f"Section: {title}")
        layout.addWidget(title_label)
        
        return section
        
    def setup_accessibility(self):
        """Set up the comprehensive accessibility system"""
        # Initialize accessibility manager
        self.accessibility_manager = ProfessionalAccessibilityManager(self)
        
        # Initialize toast system
        self.toast_system = ProfessionalToastNotification(self)
        
        # Connect accessibility signals
        self.accessibility_manager.accessibility_changed.connect(self.on_accessibility_changed)
        self.accessibility_manager.theme_changed.connect(self.on_theme_changed)
        
        # Set up main window accessibility
        self.accessibility_manager.setup_widget_accessibility(
            self,
            accessible_name="Professional Accessibility Demo",
            accessible_description="Demonstration of comprehensive accessibility features"
        )
        
        # Set up keyboard shortcuts
        self.setup_keyboard_shortcuts()
        
        logger.info("♿ Professional accessibility system ready!")
        
    def setup_keyboard_shortcuts(self):
        """Set up keyboard shortcuts"""
        from PyQt5.QtWidgets import QShortcut
        
        # Accessibility shortcuts
        QShortcut(QKeySequence("Ctrl+Alt+H"), self, self.toggle_high_contrast)
        QShortcut(QKeySequence("Ctrl+Alt+F"), self, self.toggle_focus_indicators)
        QShortcut(QKeySequence("Ctrl+Alt+T"), self, self.toggle_large_text)
        QShortcut(QKeySequence("F1"), self, self.show_accessibility_help)
        
    # Demo functions
    def demo_navigation(self, destination):
        """Demo navigation with accessibility feedback"""
        self.toast_system.show_info(f"Navigating to: {destination}")
        logger.info(f"♿ Accessible navigation to: {destination}")
        
    def toggle_high_contrast(self):
        """Toggle high-contrast theme"""
        if self.accessibility_manager:
            self.accessibility_manager.toggle_feature(AccessibilityFeature.HIGH_CONTRAST)
            
    def toggle_focus_indicators(self):
        """Toggle enhanced focus indicators"""
        if self.accessibility_manager:
            self.accessibility_manager.toggle_feature(AccessibilityFeature.FOCUS_INDICATORS)
            
    def toggle_large_text(self):
        """Toggle large text mode"""
        if self.accessibility_manager:
            self.accessibility_manager.toggle_feature(AccessibilityFeature.LARGE_TEXT)
            
    def show_accessibility_report(self):
        """Show accessibility compliance report"""
        if self.accessibility_manager:
            report = self.accessibility_manager.get_accessibility_report()
            
            message = f"""
            📊 Accessibility Report:
            
            Compliance Level: {report['compliance_level']}
            Enabled Features: {len(report['enabled_features'])}
            Focus Chain: {report['focus_chain_length']} elements
            Keyboard Shortcuts: {report['keyboard_shortcuts']}
            
            Current Theme: {report['current_theme']}
            """
            
            self.toast_system.show_info("Accessibility report generated (check console)")
            logger.info(message)
            
    def test_keyboard_navigation(self):
        """Test keyboard navigation"""
        self.toast_system.show_success("✅ Keyboard navigation test: Use Tab/Shift+Tab to navigate")
        logger.info("🧪 Testing keyboard navigation - use Tab key to navigate between elements")
        
    def test_screen_reader(self):
        """Test screen reader compatibility"""
        self.toast_system.show_success("✅ Screen reader test: All elements have accessible names")
        logger.info("🧪 Testing screen reader compatibility - all interactive elements have accessible names and descriptions")
        
    def show_accessibility_help(self):
        """Show accessibility help"""
        help_text = """
        ♿ Accessibility Help:
        
        Keyboard Navigation:
        • Tab: Move to next element
        • Shift+Tab: Move to previous element
        • Enter/Space: Activate buttons
        • Arrow keys: Navigate within components
        
        Accessibility Shortcuts:
        • Ctrl+Alt+H: Toggle high-contrast theme
        • Ctrl+Alt+F: Toggle focus indicators
        • Ctrl+Alt+T: Toggle large text
        • F1: Show this help
        
        Screen Reader Support:
        • All elements have accessible names
        • Descriptive labels for form controls
        • Status announcements via toast notifications
        """
        
        self.toast_system.show_info("Accessibility help displayed (check console)")
        logger.info(help_text)
        
    def on_accessibility_changed(self, feature, enabled):
        """Handle accessibility feature changes"""
        status = "enabled" if enabled else "disabled"
        self.toast_system.show_success(f"♿ {feature.replace('_', ' ').title()} {status}")
        
    def on_theme_changed(self, theme_name):
        """Handle theme changes"""
        self.toast_system.show_info(f"🎨 Theme changed to: {theme_name}")

def main():
    """Run the professional accessibility demo"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # Set application properties
    app.setApplicationName("Professional Accessibility Demo")
    app.setApplicationVersion("1.0.0")
    
    # Create and show demo window
    demo = ProfessionalAccessibilityDemo()
    demo.show()
    
    logger.info("🚀 Professional accessibility demo started!")
    logger.info("♿ Try using Tab key to navigate and keyboard shortcuts!")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
