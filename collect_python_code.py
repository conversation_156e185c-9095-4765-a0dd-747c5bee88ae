#!/usr/bin/env python3
"""
<PERSON><PERSON>t to collect all Python code from the knowledge_app project into a single text file.
This script will recursively find all .py files and combine their content.
"""

import os
import sys
from pathlib import Path
from datetime import datetime

def collect_python_files():
    """Collect all Python files in the project"""
    project_root = Path(__file__).parent
    python_files = []
    
    # Get all .py files recursively
    for py_file in project_root.rglob("*.py"):
        # Skip __pycache__ directories and compiled files
        if "__pycache__" not in str(py_file):
            python_files.append(py_file)
    
    return sorted(python_files)

def read_file_content(file_path):
    """Read content of a Python file with error handling"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='latin-1') as f:
                return f.read()
        except Exception as e:
            return f"# Error reading file: {e}\n"
    except Exception as e:
        return f"# Error reading file: {e}\n"

def create_combined_file():
    """Create a single text file with all Python code"""
    project_root = Path(__file__).parent
    output_file = project_root / "complete_python_code.txt"
    
    python_files = collect_python_files()
    
    print(f"Found {len(python_files)} Python files")
    print(f"Creating combined file: {output_file}")
    
    with open(output_file, 'w', encoding='utf-8') as out_file:
        # Write header
        out_file.write("=" * 80 + "\n")
        out_file.write("COMPLETE PYTHON CODE FROM KNOWLEDGE_APP PROJECT\n")
        out_file.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        out_file.write(f"Total files: {len(python_files)}\n")
        out_file.write("=" * 80 + "\n\n")
        
        # Process each file
        for i, py_file in enumerate(python_files, 1):
            relative_path = py_file.relative_to(project_root)
            
            print(f"Processing {i}/{len(python_files)}: {relative_path}")
            
            # Write file header
            out_file.write("\n" + "=" * 80 + "\n")
            out_file.write(f"FILE: {relative_path}\n")
            out_file.write(f"FULL PATH: {py_file}\n")
            out_file.write("=" * 80 + "\n\n")
            
            # Write file content
            content = read_file_content(py_file)
            out_file.write(content)
            
            # Add separator
            out_file.write("\n\n" + "-" * 80 + "\n")
            out_file.write(f"END OF FILE: {relative_path}\n")
            out_file.write("-" * 80 + "\n\n")
    
    print(f"\nCompleted! All Python code saved to: {output_file}")
    print(f"File size: {output_file.stat().st_size / (1024*1024):.2f} MB")

if __name__ == "__main__":
    create_combined_file()
