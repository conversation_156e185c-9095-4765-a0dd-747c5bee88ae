#!/usr/bin/env python3
"""
Test script to verify font size mapping functionality
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_font_size_mapping():
    """Test the font size mapping functionality"""
    
    # Import the professional settings screen
    from knowledge_app.ui.professional_settings_screen import ProfessionalSettingsScreen
    
    print("🔤 Testing Font Size Mapping")
    print("=" * 50)
    
    # Test the mapping dictionary
    mapping = ProfessionalSettingsScreen.FONT_SIZE_MAPPING
    print(f"Font Size Mapping: {mapping}")
    
    # Test each mapping
    for text, expected_size in mapping.items():
        print(f"  '{text}' -> {expected_size}px")
    
    print("\n✅ Font size mapping test completed!")
    
    # Test reverse mapping
    print("\n🔄 Testing Reverse Mapping")
    print("=" * 50)
    
    reverse_mapping = {v: k for k, v in mapping.items()}
    print(f"Reverse Mapping: {reverse_mapping}")
    
    for size, text in reverse_mapping.items():
        print(f"  {size}px -> '{text}'")
    
    print("\n✅ Reverse mapping test completed!")
    
    # Test edge cases
    print("\n⚠️ Testing Edge Cases")
    print("=" * 50)
    
    # Test unknown text
    unknown_text = "Unknown Size"
    default_size = mapping.get(unknown_text, 16)
    print(f"Unknown text '{unknown_text}' -> {default_size}px (default)")
    
    # Test unknown numeric
    unknown_numeric = 999
    default_text = reverse_mapping.get(unknown_numeric, 'Medium')
    print(f"Unknown size {unknown_numeric}px -> '{default_text}' (default)")
    
    print("\n✅ Edge case tests completed!")
    
    return True

if __name__ == "__main__":
    try:
        test_font_size_mapping()
        print("\n🎉 All tests passed!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
