#!/usr/bin/env python3
"""
Professional Animations Demo - Phase 1A Implementation

This script demonstrates the enhanced professional animation system including:
- Professional screen transitions
- Micro-interactions with buttons
- Loading indicators and spinners
- Toast notification system
- Professional feedback cards

Run this to see the improvements in action!
"""

import sys
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QPushButton, QFrame, QScrollArea, QGridLayout
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# Import our professional components
from knowledge_app.ui.seductive_transitions import (
    ProfessionalStackedWidget, TransitionType, ProfessionalFeedbackCard,
    ProfessionalLoadingSpinner, ProfessionalToastNotification, ProfessionalProgressBar
)
from knowledge_app.ui.professional_buttons import (
    ProfessionalButton, ProfessionalIconButton, ProfessionalToggleButton,
    ProfessionalLoadingButton
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProfessionalAnimationDemo(QMainWindow):
    """Demo window showcasing professional animations"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Professional Animations Demo - Phase 1A")
        self.setMinimumSize(1200, 800)
        
        # Initialize components
        self.toast_system = None
        self.loading_spinner = None
        self.progress_bar = None
        
        self.setup_ui()
        self.setup_demo_content()
        
    def setup_ui(self):
        """Set up the demo UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Title
        title = QLabel("🎨 Professional Animations Demo - Phase 1A")
        title.setFont(QFont("Arial", 24, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #3B82F6; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # Create demo sections
        self.create_transition_demo(layout)
        self.create_button_demo(layout)
        self.create_notification_demo(layout)
        self.create_loading_demo(layout)
        
    def create_transition_demo(self, parent_layout):
        """Create screen transition demo"""
        section = self.create_demo_section("🎬 Professional Screen Transitions")
        parent_layout.addWidget(section)
        
        content_layout = QVBoxLayout()
        section.layout().addLayout(content_layout)
        
        # Create professional stacked widget
        self.transition_stack = ProfessionalStackedWidget()
        self.transition_stack.setMinimumHeight(200)
        
        # Create demo screens
        screen1 = self.create_demo_screen("🌟 Main Menu", "#3B82F6")
        screen2 = self.create_demo_screen("📚 Quiz Setup", "#10B981")
        screen3 = self.create_demo_screen("⚙️ Settings", "#8B5CF6")
        
        self.transition_stack.addWidget(screen1)
        self.transition_stack.addWidget(screen2)
        self.transition_stack.addWidget(screen3)
        
        content_layout.addWidget(self.transition_stack)
        
        # Transition controls
        controls_layout = QHBoxLayout()
        
        # Transition type buttons
        slide_btn = ProfessionalButton("Slide Left", button_type="primary")
        slide_btn.clicked.connect(lambda: self.demo_transition(TransitionType.SLIDE_LEFT))
        controls_layout.addWidget(slide_btn)
        
        fade_btn = ProfessionalButton("Fade", button_type="secondary")
        fade_btn.clicked.connect(lambda: self.demo_transition(TransitionType.FADE))
        controls_layout.addWidget(fade_btn)
        
        scale_btn = ProfessionalButton("Scale Fade", button_type="secondary")
        scale_btn.clicked.connect(lambda: self.demo_transition(TransitionType.SCALE_FADE))
        controls_layout.addWidget(scale_btn)
        
        content_layout.addLayout(controls_layout)
        
        # Auto-demo timer
        self.transition_timer = QTimer()
        self.transition_timer.timeout.connect(self.auto_transition_demo)
        self.transition_timer.start(3000)  # Change every 3 seconds
        self.current_screen = 0
        
    def create_demo_screen(self, title, color):
        """Create a demo screen widget"""
        screen = QLabel(title)
        screen.setAlignment(Qt.AlignCenter)
        screen.setFont(QFont("Arial", 18, QFont.Bold))
        screen.setStyleSheet(f"""
            background: {color};
            color: white;
            border-radius: 12px;
            padding: 40px;
        """)
        return screen
        
    def create_button_demo(self, parent_layout):
        """Create button micro-interactions demo"""
        section = self.create_demo_section("🎯 Professional Button Micro-interactions")
        parent_layout.addWidget(section)
        
        content_layout = QGridLayout()
        section.layout().addLayout(content_layout)
        
        # Different button types
        primary_btn = ProfessionalButton("Primary Action", button_type="primary")
        content_layout.addWidget(primary_btn, 0, 0)
        
        secondary_btn = ProfessionalButton("Secondary Action", button_type="secondary")
        content_layout.addWidget(secondary_btn, 0, 1)
        
        icon_btn = ProfessionalIconButton("With Icon", "🚀", button_type="primary")
        content_layout.addWidget(icon_btn, 0, 2)
        
        toggle_btn = ProfessionalToggleButton("Toggle Me")
        content_layout.addWidget(toggle_btn, 1, 0)
        
        loading_btn = ProfessionalLoadingButton("Loading Button", button_type="primary")
        loading_btn.clicked.connect(lambda: self.demo_loading_button(loading_btn))
        content_layout.addWidget(loading_btn, 1, 1)
        
    def create_notification_demo(self, parent_layout):
        """Create toast notification demo"""
        section = self.create_demo_section("🔔 Professional Toast Notifications")
        parent_layout.addWidget(section)
        
        content_layout = QHBoxLayout()
        section.layout().addLayout(content_layout)
        
        # Initialize toast system
        self.toast_system = ProfessionalToastNotification(self)
        
        # Notification buttons
        success_btn = ProfessionalButton("Success Toast", button_type="primary")
        success_btn.clicked.connect(lambda: self.toast_system.show_success("Operation completed successfully! 🎉"))
        content_layout.addWidget(success_btn)
        
        error_btn = ProfessionalButton("Error Toast", button_type="secondary")
        error_btn.clicked.connect(lambda: self.toast_system.show_error("Something went wrong! Please try again. ❌"))
        content_layout.addWidget(error_btn)
        
        info_btn = ProfessionalButton("Info Toast", button_type="secondary")
        info_btn.clicked.connect(lambda: self.toast_system.show_info("Here's some helpful information! ℹ️"))
        content_layout.addWidget(info_btn)
        
        warning_btn = ProfessionalButton("Warning Toast", button_type="secondary")
        warning_btn.clicked.connect(lambda: self.toast_system.show_warning("Please be careful with this action! ⚠️"))
        content_layout.addWidget(warning_btn)
        
    def create_loading_demo(self, parent_layout):
        """Create loading indicators demo"""
        section = self.create_demo_section("⏳ Professional Loading Indicators")
        parent_layout.addWidget(section)
        
        content_layout = QVBoxLayout()
        section.layout().addLayout(content_layout)
        
        # Loading spinner
        spinner_layout = QHBoxLayout()
        spinner_layout.addWidget(QLabel("Loading Spinner:"))
        
        self.loading_spinner = ProfessionalLoadingSpinner(size=32)
        spinner_layout.addWidget(self.loading_spinner)
        spinner_layout.addStretch()
        
        spinner_btn = ProfessionalButton("Toggle Spinner", button_type="secondary")
        spinner_btn.clicked.connect(self.toggle_spinner)
        spinner_layout.addWidget(spinner_btn)
        
        content_layout.addLayout(spinner_layout)
        
        # Progress bar
        progress_layout = QVBoxLayout()
        progress_layout.addWidget(QLabel("Progress Bar:"))
        
        self.progress_bar = ProfessionalProgressBar()
        progress_layout.addWidget(self.progress_bar)
        
        progress_btn = ProfessionalButton("Demo Progress", button_type="secondary")
        progress_btn.clicked.connect(self.demo_progress)
        progress_layout.addWidget(progress_btn)
        
        content_layout.addLayout(progress_layout)
        
    def create_demo_section(self, title):
        """Create a demo section with title"""
        section = QFrame()
        section.setFrameStyle(QFrame.Box)
        section.setStyleSheet("""
            QFrame {
                border: 2px solid #E5E7EB;
                border-radius: 12px;
                background: #F9FAFB;
                padding: 16px;
            }
        """)
        
        layout = QVBoxLayout(section)
        
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: #374151; border: none; background: transparent;")
        layout.addWidget(title_label)
        
        return section
        
    def setup_demo_content(self):
        """Set up demo content and start animations"""
        logger.info("🎨 Professional animations demo ready!")
        
        # Start spinner
        if self.loading_spinner:
            self.loading_spinner.start_spinning()
            
    def demo_transition(self, transition_type):
        """Demo a specific transition type"""
        self.current_screen = (self.current_screen + 1) % self.transition_stack.count()
        widget = self.transition_stack.widget(self.current_screen)
        self.transition_stack.setCurrentWidget(widget, transition_type)
        
    def auto_transition_demo(self):
        """Auto-demo transitions"""
        transitions = [TransitionType.SLIDE_LEFT, TransitionType.FADE, TransitionType.SCALE_FADE]
        transition = transitions[self.current_screen % len(transitions)]
        self.demo_transition(transition)
        
    def demo_loading_button(self, button):
        """Demo loading button state"""
        button.set_loading(True, "Processing")
        QTimer.singleShot(3000, lambda: button.set_loading(False))
        
    def toggle_spinner(self):
        """Toggle loading spinner"""
        if self.loading_spinner:
            if hasattr(self.loading_spinner, '_spinning') and self.loading_spinner._spinning:
                self.loading_spinner.stop_spinning()
                self.loading_spinner._spinning = False
            else:
                self.loading_spinner.start_spinning()
                self.loading_spinner._spinning = True
                
    def demo_progress(self):
        """Demo progress bar animation"""
        if self.progress_bar:
            # Animate from 0 to 100
            self.progress_bar.setProgress(0)
            QTimer.singleShot(100, lambda: self.progress_bar.setProgress(25))
            QTimer.singleShot(500, lambda: self.progress_bar.setProgress(50))
            QTimer.singleShot(1000, lambda: self.progress_bar.setProgress(75))
            QTimer.singleShot(1500, lambda: self.progress_bar.setProgress(100))
            QTimer.singleShot(2500, lambda: self.progress_bar.setProgress(0))

def main():
    """Run the professional animations demo"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # Set application properties
    app.setApplicationName("Professional Animations Demo")
    app.setApplicationVersion("1.0.0")
    
    # Create and show demo window
    demo = ProfessionalAnimationDemo()
    demo.show()
    
    logger.info("🚀 Professional animations demo started!")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
