#!/usr/bin/env python3
"""
Quick Training Test - Memory Alchemy in Action

This script runs a minimal training test to verify all optimizations work together:
- Paged optimizer
- Gradient accumulation  
- Quantization
- FIRE v2.1 estimation
- Attention optimization
"""

import sys
import os
import time
import logging
import torch
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def quick_training_test():
    """Run a quick training test with all optimizations"""
    print("🔥 QUICK TRAINING TEST - MEMORY ALCHEMY IN ACTION")
    print("=" * 60)
    
    if not torch.cuda.is_available():
        print("❌ CUDA not available - cannot run training test")
        return
    
    try:
        from transformers import (
            AutoTokenizer, AutoModelForCausalLM, 
            TrainingArguments, Trainer, DataCollatorForLanguageModeling,
            BitsAndBytesConfig
        )
        from datasets import Dataset
        from peft import LoraConfig, get_peft_model, TaskType
        from knowledge_app.core.fire_v21_estimator import FIREv21Estimator
        
        print("✅ All required libraries imported successfully")
        
        # 🔥 STEP 1: Setup quantization (Memory Alchemy)
        print("\n🔥 STEP 1: MEMORY ALCHEMY - Quantization Setup")
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=True,
        )
        print("✅ 4-bit quantization configured")
        
        # 🔥 STEP 2: Load small model for testing
        print("\n🔥 STEP 2: Loading Test Model")
        model_name = "microsoft/DialoGPT-small"  # Small model for quick test
        
        print(f"Loading {model_name} with optimizations...")
        
        # Try different attention implementations
        attention_backends = ["flash_attention_2", "sdpa", "eager"]
        model = None
        
        for backend in attention_backends:
            try:
                print(f"   Trying attention backend: {backend}")
                model = AutoModelForCausalLM.from_pretrained(
                    model_name,
                    quantization_config=bnb_config,
                    attn_implementation=backend,
                    device_map="auto",
                    torch_dtype=torch.float16
                )
                print(f"✅ Model loaded with {backend} attention!")
                break
            except Exception as e:
                print(f"   ❌ {backend} failed: {str(e)[:100]}...")
                continue
        
        if model is None:
            print("❌ Failed to load model with any attention backend")
            return
        
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        # 🔥 STEP 3: Apply LoRA (Memory Efficiency)
        print("\n🔥 STEP 3: MEMORY ALCHEMY - LoRA Configuration")
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=16,  # Low rank for memory efficiency
            lora_alpha=32,
            lora_dropout=0.1,
            target_modules=["c_attn", "c_proj"],  # DialoGPT specific modules
        )
        
        model = get_peft_model(model, lora_config)
        print("✅ LoRA applied - only training small subset of parameters")
        print(f"   Trainable parameters: {model.num_parameters(only_trainable=True):,}")
        print(f"   Total parameters: {model.num_parameters():,}")
        
        # 🔥 STEP 4: Create tiny dataset
        print("\n🔥 STEP 4: Creating Test Dataset")
        
        # Create minimal training data
        texts = [
            "Hello, how are you today?",
            "I am doing well, thank you!",
            "What is your favorite color?",
            "I like blue and green colors.",
            "Tell me about artificial intelligence.",
            "AI is a fascinating field of study."
        ]
        
        # Tokenize properly for language modeling
        def tokenize_function(examples):
            # Tokenize without return_tensors (let the data collator handle that)
            tokenized = tokenizer(
                examples["text"],
                truncation=True,
                padding=False,  # Let data collator handle padding
                max_length=128,  # Short sequences for quick test
            )
            # Add labels for language modeling (same as input_ids)
            tokenized["labels"] = tokenized["input_ids"].copy()
            return tokenized

        dataset = Dataset.from_dict({"text": texts})
        tokenized_dataset = dataset.map(tokenize_function, batched=True, remove_columns=["text"])
        
        print(f"✅ Dataset created: {len(tokenized_dataset)} samples")
        
        # 🔥 STEP 5: Training Arguments (The Sacred Configuration)
        print("\n🔥 STEP 5: THE SACRED TRAINING CONFIGURATION")
        
        training_args = TrainingArguments(
            output_dir="./test_training_output",
            num_train_epochs=1,  # Just 1 epoch for quick test
            per_device_train_batch_size=1,  # Memory optimized
            gradient_accumulation_steps=4,   # Simulate batch size 4
            optim="paged_adamw_8bit",       # THE SACRED OPTIMIZER
            learning_rate=2e-4,
            weight_decay=0.001,
            fp16=True,                      # Mixed precision
            gradient_checkpointing=True,    # Memory for compute trade
            group_by_length=True,           # Efficiency spell
            logging_steps=1,                # Log every step for testing
            save_steps=10,                  # Don't save during test
            max_steps=5,                    # Only 5 steps for quick test
            remove_unused_columns=False,
            dataloader_num_workers=0,       # Avoid multiprocessing issues
        )
        
        print("✅ Sacred training configuration applied:")
        print(f"   🔥 Optimizer: {training_args.optim}")
        print(f"   🧮 Gradient accumulation: {training_args.gradient_accumulation_steps}")
        print(f"   💾 Gradient checkpointing: {training_args.gradient_checkpointing}")
        print(f"   🎯 Mixed precision: FP16")
        print(f"   📊 Max steps: {training_args.max_steps}")
        
        # 🔥 STEP 6: Initialize FIRE v2.1 Oracle
        print("\n🔥 STEP 6: FIRE v2.1 ORACLE INITIALIZATION")
        
        oracle = FIREv21Estimator()
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=tokenizer,
            mlm=False,
        )
        
        # Create trainer
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=tokenized_dataset,
            data_collator=data_collator,
        )
        
        # Initialize Oracle with real data
        try:
            prediction = oracle.initialize_with_trainer(trainer, tokenized_dataset)
            print(f"✅ Oracle prophecy: {prediction.estimated_hours:.3f} hours")
            print(f"   Confidence: {prediction.confidence_level:.1%}")
            print(f"   Method: {prediction.calculation_method}")
            
            # Add Oracle as callback
            trainer.add_callback(oracle)
            
        except Exception as e:
            print(f"⚠️ Oracle initialization failed: {e}")
        
        # 🔥 STEP 7: RUN THE TEST TRAINING
        print("\n🔥 STEP 7: RUNNING TRAINING TEST")
        print("🚀 Starting training with all optimizations...")
        
        start_time = time.time()
        
        # Clear GPU memory before training
        torch.cuda.empty_cache()
        initial_memory = torch.cuda.memory_allocated() / (1024**2)
        print(f"   Initial GPU memory: {initial_memory:.1f} MB")
        
        # Train!
        train_result = trainer.train()
        
        end_time = time.time()
        final_memory = torch.cuda.memory_allocated() / (1024**2)
        
        # 🔥 STEP 8: RESULTS
        print("\n🔥 STEP 8: TRAINING TEST RESULTS")
        print("=" * 60)
        
        training_time = end_time - start_time
        print(f"✅ Training completed successfully!")
        print(f"   ⏱️ Time: {training_time:.2f} seconds")
        print(f"   📊 Steps: {train_result.global_step}")
        print(f"   📉 Final loss: {train_result.training_loss:.4f}")
        print(f"   💾 Peak GPU memory: {final_memory:.1f} MB")
        print(f"   🔥 Memory efficiency: {(final_memory - initial_memory):.1f} MB used")
        
        # Get Oracle stats
        oracle_stats = oracle.get_real_time_stats()
        if oracle_stats:
            print(f"   🔮 Oracle accuracy: Predicted vs Actual timing")
            print(f"   📈 Average step time: {oracle_stats.get('avg_step_time', 0):.3f}s")
        
        print("\n🎉 ALL OPTIMIZATIONS WORKING PERFECTLY!")
        print("🔥 Memory Alchemy: SUCCESS")
        print("⚡ Computational Sorcery: SUCCESS") 
        print("🔮 FIRE v2.1 Oracle: SUCCESS")
        
        # Cleanup
        del model, trainer
        torch.cuda.empty_cache()
        
    except Exception as e:
        print(f"❌ Training test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_training_test()
