{"mcq_settings": {"offline_mode": true, "default_difficulty": "medium", "max_questions_per_batch": 5, "enable_fallback": true}, "local_model": {"primary_model": "mistralai/Mistral-7B-Instruct-v0.2", "fallback_models": ["Qwen/Qwen2.5-7B-Instruct", "mistralai/Mistral-7B-Instruct-v0.2", "google/gemma-2-9b-it", "microsoft/DialoGPT-medium", "microsoft/DialoGPT-small", "distilgpt2", "gpt2"], "quantization": {"load_in_4bit": true, "bnb_4bit_compute_dtype": "float16", "bnb_4bit_quant_type": "nf4"}, "generation_params": {"max_new_tokens": 600, "temperature": 0.7, "top_p": 0.9, "top_k": 50, "repetition_penalty": 1.1, "do_sample": true}}, "paths": {"lora_adapters": ["data/lora_adapters_mistral", "data/lora_adapters", "lora_adapters_mistral", "lora_adapters"], "fine_tuned_models": ["data/fine_tuned_models", "fine_tuned_models", "data/models"]}, "logging": {"level": "INFO", "enable_model_loading_logs": true, "enable_generation_logs": true}}