{"paths": {"base": ".", "config": "config", "cache": "data/cache", "image_cache": "data/cache/images", "models": "data/models", "user_data": "data/user_data", "uploaded_books": "data/uploaded_books", "logs": "logs", "resources": "resources"}, "storage": {"image_cache_limit": 524288000, "model_cache_limit": 4294967296, "book_storage_limit": 104857600, "use_mmap": true, "mmap_threshold": 104857600, "io_workers": 4, "read_buffer_size": 1048576, "write_buffer_size": 1048576, "prefetch_size": 16777216, "disk_optimization": {"read_ahead": 16384, "use_direct_io": true, "use_async_io": true, "use_sequential_hint": true}}, "logging": {"level": "INFO", "file_path": "logs/app.log"}, "development": {"debug": false}, "display_settings": {"enable_gpu": true, "theme": "dark", "font_size": 12}, "environment": {"PYTHONPATH": ".", "KNOWLEDGE_APP_DEBUG": "0"}}