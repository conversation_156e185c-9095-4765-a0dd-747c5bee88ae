# CUDA Setup Guide for Knowledge App

This guide explains how to set up CUDA to enable hardware acceleration for the Knowledge App.

## Requirements

- NVIDIA GPU with CUDA capability 3.5 or higher
- 64-bit operating system (Windows 10/11, Linux, macOS)
- Sufficient disk space (approximately 10GB for CUDA toolkit)

## Step 1: Check GPU Compatibility

First, verify that your GPU supports CUDA:

1. For **Windows**: Right-click on desktop → NVIDIA Control Panel → Help → System Information
2. For **Linux**: Run `nvidia-smi` in terminal
3. For **macOS**: Recent versions don't support CUDA; consider using a virtual environment or Docker

## Step 2: Install CUDA Toolkit

### Windows Installation

1. Download the CUDA Toolkit from [NVIDIA's website](https://developer.nvidia.com/cuda-toolkit-archive)
   - Choose version 11.8 for best compatibility with PyTorch 2.0+
   - Select your operating system and architecture

2. Run the installer and follow these steps:
   - Choose "Custom Installation"
   - Install CUDA toolkit and driver (unless you already have the latest driver)
   - Keep the default installation location

3. Verify installation by opening Command Prompt and running:
   ```
   nvcc --version
   ```

### Linux Installation

1. Download the CUDA toolkit runfile from [NVIDIA's website](https://developer.nvidia.com/cuda-toolkit-archive)

2. Install dependencies:
   ```bash
   sudo apt update
   sudo apt install build-essential gcc-<version>
   ```

3. Stop the display server:
   ```bash
   sudo service lightdm stop  # or gdm/kdm depending on your system
   ```

4. Run the installer:
   ```bash
   chmod +x cuda_<version>_linux.run
   sudo ./cuda_<version>_linux.run
   ```

5. Add CUDA to your PATH by adding these lines to ~/.bashrc:
   ```bash
   export PATH=/usr/local/cuda-<version>/bin${PATH:+:${PATH}}
   export LD_LIBRARY_PATH=/usr/local/cuda-<version>/lib64${LD_LIBRARY_PATH:+:${LD_LIBRARY_PATH}}
   ```

6. Verify installation:
   ```bash
   nvcc --version
   ```

### macOS Alternative Setup

For macOS users, we recommend using PyTorch with MPS (Metal Performance Shaders) as CUDA is no longer supported on macOS:

1. Install PyTorch with MPS support:
   ```bash
   pip install torch torchvision
   ```

2. In your code, use:
   ```python
   device = torch.device('mps' if torch.backends.mps.is_available() else 'cpu')
   ```

## Step 3: Install cuDNN

cuDNN is required for optimal deep learning performance:

1. Register and download cuDNN from [NVIDIA's website](https://developer.nvidia.com/cudnn)
   - Choose the version compatible with your CUDA installation

2. For **Windows**:
   - Extract the archive
   - Copy files to corresponding folders in your CUDA installation

3. For **Linux**:
   ```bash
   tar -xzvf cudnn-<version>.tgz
   sudo cp cuda/include/cudnn*.h /usr/local/cuda/include
   sudo cp cuda/lib64/libcudnn* /usr/local/cuda/lib64
   sudo chmod a+r /usr/local/cuda/include/cudnn*.h /usr/local/cuda/lib64/libcudnn*
   ```

## Step 4: Install PyTorch with CUDA Support

Install PyTorch with the correct CUDA version:

```bash
# For CUDA 11.8
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# For CUDA 12.1
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
```

## Step 5: Verify Installation

Verify your setup with this Python code:

```python
import torch
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"CUDA device count: {torch.cuda.device_count()}")
if torch.cuda.is_available():
    print(f"CUDA device: {torch.cuda.get_device_name(0)}")
```

## Troubleshooting

### Common Issues

1. **CUDA not found**:
   - Check environment variables (PATH and LD_LIBRARY_PATH)
   - Verify that NVIDIA drivers are installed correctly

2. **Version mismatch**:
   - Ensure PyTorch CUDA version matches your installed CUDA toolkit

3. **Out of memory errors**:
   - Reduce batch sizes
   - Use gradient accumulation

4. **Performance issues**:
   - Update to the latest NVIDIA drivers
   - Check for thermal throttling

### Getting Help

If you encounter issues:
1. Run our diagnostics: `python -m utils.test_component_compatibility`
2. Check the console output for specific error messages
3. Consult the PyTorch forums or NVIDIA developer forums

## Performance Tips

- Keep your NVIDIA drivers up to date
- Monitor GPU usage with `nvidia-smi` (Windows/Linux) or Activity Monitor (macOS)
- Consider using mixed precision training (`torch.cuda.amp`) for better performance
- For multi-GPU setups, use `torch.nn.DataParallel` or `torch.nn.DistributedDataParallel`

## Appendix: Which CUDA Version to Use

| PyTorch Version | Recommended CUDA Version |
|-----------------|--------------------------|
| 2.0.0+          | CUDA 11.7/11.8/12.1     |
| 1.13.x          | CUDA 11.7               |
| 1.12.x          | CUDA 11.3/11.6          |
| 1.10.x/1.11.x   | CUDA 11.3               |
| 1.8.x/1.9.x     | CUDA 10.2/11.1          |
| 1.7.x           | CUDA 10.2/11.0          |
