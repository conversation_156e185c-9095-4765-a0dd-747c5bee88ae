# Refactoring Guide: Separating UI from Application Logic

This guide provides practical steps for gradually refactoring the Knowledge App to better separate UI logic (PyQt) from application logic and data services, as recommended in the architecture guidelines.

## Why Separate UI from Logic?

Separating UI from application logic offers several benefits:

1. **Maintainability**: Changes to UI don't affect core business logic and vice versa
2. **Testability**: Components can be tested in isolation without complex UI dependencies
3. **Flexibility**: The same logic can be reused across different UI implementations
4. **Team Collaboration**: UI designers and business logic developers can work independently

## Identifying Current Issues

Before refactoring, identify code that exhibits tight coupling:

- UI components making direct database queries
- Business logic embedded in UI event handlers
- UI components directly implementing domain rules
- UI-specific classes handling data persistence
- UI components maintaining complex state

## Refactoring Strategy: Incremental Approach

### Phase 1: Identify and Plan (2 weeks)

1. **Map Dependencies**: Create a dependency graph of components
2. **Prioritize Components**: Identify critical or problematic areas first
3. **Define Interfaces**: Design the interfaces that will separate UI from logic
4. **Create Test Coverage**: Ensure tests are in place before refactoring

### Phase 2: Extract Application Logic (4-8 weeks)

1. **Extract Service Layer**:
   - Move business logic into dedicated service classes
   - Start with the most independent components
   - Ensure UI only calls these services, never implementing logic itself

2. **Extract Domain Models**:
   - Replace UI-specific data structures with domain models
   - Ensure models don't have UI dependencies

3. **Implement Repository Pattern**:
   - Abstract data access behind repository interfaces
   - Remove direct database access from UI components

### Phase 3: Refactor UI Layer (4-8 weeks)

1. **Implement MVVM Pattern**:
   - Create ViewModels to handle UI state and presentation logic
   - Keep Views (UI components) focused solely on display and user interaction
   - Use data binding where possible

2. **Apply Dependency Injection**:
   - Initialize services outside UI components
   - Pass services to UI components rather than creating them inline

## Practical Examples

### Before: UI with embedded logic

```python
class QuizScreen(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_connection = sqlite3.connect("quiz_data.db")
        # UI setup code...
        
    def start_quiz(self):
        # Direct database access
        cursor = self.db_connection.cursor()
        cursor.execute("SELECT * FROM questions WHERE category=?", (self.category,))
        self.questions = cursor.fetchall()
        
        # Complex business logic inside UI component
        if self.difficulty == "hard":
            self.questions = [q for q in self.questions if q[3] > 7]
            self.time_limit = 30
        else:
            self.time_limit = 60
            
        # More UI code...
```

### After: Separated concerns

```python
# Domain model
class Question:
    def __init__(self, id, text, options, answer, difficulty):
        self.id = id
        self.text = text
        self.options = options
        self.answer = answer
        self.difficulty = difficulty

# Repository interface
class QuestionRepository:
    def get_questions_by_category(self, category):
        pass
        
# Concrete implementation
class SQLiteQuestionRepository(QuestionRepository):
    def __init__(self, db_path):
        self.db_path = db_path
        
    def get_questions_by_category(self, category):
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM questions WHERE category=?", (category,))
            rows = cursor.fetchall()
            return [Question(row[0], row[1], row[2], row[3], row[4]) for row in rows]

# Service class
class QuizService:
    def __init__(self, question_repository):
        self.question_repository = question_repository
        
    def get_quiz_questions(self, category, difficulty):
        questions = self.question_repository.get_questions_by_category(category)
        
        if difficulty == "hard":
            questions = [q for q in questions if q.difficulty > 7]
            time_limit = 30
        else:
            time_limit = 60
            
        return questions, time_limit

# UI component
class QuizScreen(QWidget):
    def __init__(self, quiz_service, parent=None):
        super().__init__(parent)
        self.quiz_service = quiz_service
        # UI setup code...
        
    def start_quiz(self):
        # Use service instead of direct DB access and logic
        self.questions, self.time_limit = self.quiz_service.get_quiz_questions(
            self.category, self.difficulty
        )
        # UI update code...
```

## Refactoring Priority Areas

Focus on these components first:

1. **Data Access Logic**: Extract all database queries into repositories
2. **File Processing Logic**: Move file handling into dedicated services
3. **Model Training/Inference**: Extract ML logic into dedicated classes
4. **User Authentication**: Separate auth logic from UI components
5. **Configuration Management**: Create dedicated configuration service

## Handling Legacy Code

For older parts that are too risky to refactor immediately:

1. **Facade Pattern**: Create clean interfaces around legacy code
2. **Adapter Pattern**: Wrap legacy components with adapters that follow the new architecture
3. **Strangler Pattern**: Gradually replace parts of legacy components

## Testing Strategy

As you refactor:

1. **Write Tests First**: Add tests before refactoring when possible
2. **Test Boundaries**: Focus on testing the interfaces between UI and logic
3. **Mock Dependencies**: Use mock objects to test components in isolation
4. **UI Tests**: Add UI tests for critical paths to ensure refactoring doesn't break functionality

## Timeline and Progress Tracking

Track progress with these metrics:

1. **Coupling Score**: Measure dependencies between UI and logic components
2. **Test Coverage**: Track coverage of refactored components
3. **Issue Rate**: Monitor if refactored areas have fewer bugs
4. **Development Speed**: Measure if adding features becomes faster over time

## Conclusion

Refactoring is a gradual process. Focus on high-value, high-risk areas first, and ensure each change improves the architecture without breaking functionality. Always maintain a working application throughout the refactoring process.
