# xFormers Installation Guide for Knowledge App

This guide helps you install and configure xFormers for optimal performance in the Knowledge App, especially on Windows systems.

## What is xFormers?

xFormers is a library developed by Meta that provides memory-efficient attention mechanisms for transformer models. It's particularly useful as an alternative to Flash Attention 2 on Windows systems where Flash Attention can be difficult to install.

## Benefits of xFormers

- ✅ **Windows Compatible**: Easier to install on Windows than Flash Attention 2
- ✅ **Memory Efficient**: Reduces GPU memory usage during training
- ✅ **Performance Boost**: Faster attention computation
- ✅ **Stable**: Well-tested and maintained by Meta
- ✅ **Automatic Fallback**: Works seamlessly with the Knowledge App's attention optimizer

## Installation Methods

### Method 1: Automatic Installation (Recommended)

The Knowledge App will automatically install xFormers when you install the requirements:

```bash
pip install -r requirements.txt
```

### Method 2: Manual Installation

If you prefer to install xFormers manually:

```bash
pip install xformers
```

### Method 3: Specific Version Installation

For compatibility with specific PyTorch versions:

```bash
# For PyTorch 2.0+
pip install xformers>=0.0.20

# For older PyTorch versions, check compatibility
pip install xformers==0.0.18
```

## Verification

After installation, you can verify that xFormers is working correctly:

### Option 1: Run the Test Script

```bash
python test_xformers_integration.py
```

This will show you:
- Whether xFormers is installed and detected
- Which attention backend is selected
- Performance optimization settings
- Compatibility status

### Option 2: Check in Python

```python
try:
    import xformers
    import xformers.ops
    print(f"✅ xFormers {xformers.__version__} is available")
    print("✅ Memory efficient attention is ready")
except ImportError:
    print("❌ xFormers is not installed")
```

## Configuration in Knowledge App

The Knowledge App automatically detects and configures xFormers. No manual configuration is needed!

### Automatic Selection Logic

1. **Windows**: Prefers xFormers over Flash Attention 2
2. **Linux/macOS**: Prefers Flash Attention 2 over xFormers
3. **Fallback**: Uses standard attention if neither is available

### Manual Override (Advanced)

If you want to force a specific attention backend, you can modify the configuration:

```python
from knowledge_app.core.attention_optimizer import attention_optimizer

# Force xFormers (if available)
attention_optimizer.selected_backend = 'xformers'

# Force Flash Attention 2 (if available)
attention_optimizer.selected_backend = 'flash_attention_2'

# Force standard attention
attention_optimizer.selected_backend = 'eager'
```

## Performance Comparison

| Backend | Windows | Linux | Memory Usage | Speed | Stability |
|---------|---------|-------|--------------|-------|-----------|
| xFormers | ✅ Excellent | ✅ Good | 🔥 Low | 🚀 Fast | ✅ Stable |
| Flash Attention 2 | ⚠️ Difficult | ✅ Excellent | 🔥 Very Low | 🚀 Very Fast | ✅ Stable |
| Standard (Eager) | ✅ Always Works | ✅ Always Works | ⚠️ High | 🐌 Slower | ✅ Stable |

## Troubleshooting

### Common Issues

#### 1. Import Error
```
ImportError: No module named 'xformers'
```
**Solution**: Install xFormers using pip:
```bash
pip install xformers
```

#### 2. Version Compatibility
```
RuntimeError: xFormers version incompatible with PyTorch
```
**Solution**: Install compatible versions:
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install xformers
```

#### 3. CUDA Version Mismatch
```
RuntimeError: CUDA version mismatch
```
**Solution**: Ensure PyTorch and xFormers are built for the same CUDA version:
```bash
# Check your CUDA version
nvidia-smi

# Install matching PyTorch and xFormers
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install xformers
```

#### 4. Memory Issues
If you still experience memory issues with xFormers:

1. **Reduce batch size** in training configuration
2. **Enable gradient checkpointing** (automatically enabled)
3. **Use mixed precision** (automatically configured)

### Getting Help

If you encounter issues:

1. **Run the test script**: `python test_xformers_integration.py`
2. **Check the logs**: Look for attention optimization messages
3. **Verify installation**: Ensure all dependencies are correctly installed
4. **Check compatibility**: Ensure PyTorch and xFormers versions are compatible

## Advanced Configuration

### Memory Optimization Settings

The Knowledge App automatically applies these xFormers-specific optimizations:

```python
{
    'attention_dropout': 0.0,  # xFormers works better with no dropout
    'use_memory_efficient_attention': True,
    'memory_efficient_attention_xformers': True,
    'fp16': True,  # xFormers works well with FP16
    'tf32': True,  # Enable TF32 for better performance on Ampere GPUs
}
```

### Training Optimizations

When xFormers is selected, the following training optimizations are applied:

- **Mixed Precision**: FP16 training for memory efficiency
- **Gradient Checkpointing**: Enabled for memory savings
- **Optimized Data Loading**: Configured for attention backends
- **Memory Management**: Enhanced cleanup for xFormers

## Conclusion

xFormers provides an excellent alternative to Flash Attention 2, especially for Windows users. The Knowledge App automatically detects and configures xFormers for optimal performance, making it easy to get the benefits of memory-efficient attention without manual configuration.

For most users, simply installing the requirements (`pip install -r requirements.txt`) will set up xFormers automatically and provide significant performance improvements for 7B model training.
