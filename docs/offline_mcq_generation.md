# Offline MCQ Generation

This document explains how the offline MCQ generation system works, especially when no custom training has been performed.

## Overview

The offline MCQ generation system is designed to work seamlessly whether you have trained custom models or not. It automatically detects available models and falls back to high-quality instruct models when needed.

## Model Priority System

The system follows this priority order when selecting models for MCQ generation:

1. **Trained LoRA Adapters** (if available)
   - Located in `data/lora_adapters_mistral/`
   - Custom fine-tuned adapters for your specific content

2. **Fine-tuned Models** (if available)
   - Located in `data/fine_tuned_models/`
   - Complete fine-tuned models

3. **Base Instruct Models** (always available)
   - High-quality instruction-following models
   - Automatically downloaded when needed

## Default Instruct Models

When no custom training has been done, the system uses these instruct models in order:

1. **Mistral-7B-Instruct-v0.2** (Primary)
   - Excellent instruction following
   - Good for educational content
   - Memory efficient with 4-bit quantization

2. **Llama-3-8B-Instruct** (Fallback 1)
   - Strong reasoning capabilities
   - Good for complex questions

3. **Qwen2.5-7B-Instruct** (Fallback 2)
   - Multilingual support
   - Good general knowledge

4. **Gemma-2-9B-IT** (Fallback 3)
   - Google's instruction-tuned model
   - Good safety features

5. **DialoGPT-medium** (Last resort)
   - Lightweight fallback option

## How It Works

### 1. Initialization
```python
from knowledge_app.core.mcq_manager import get_mcq_manager

# Create MCQ manager
mcq_manager = get_mcq_manager()

# Set to offline mode
mcq_manager.set_offline_mode(True)
```

### 2. Model Detection
The system automatically:
- Scans for trained models in standard directories
- Falls back to instruct models if no trained models found
- Uses 4-bit quantization for memory efficiency

### 3. MCQ Generation
```python
# Generate single MCQ
result = await mcq_manager.generate_quiz_async(content, "medium")

# Generate multiple MCQs
questions = mcq_manager.generate_multiple_questions(content, num_questions=5)
```

## Configuration

### Basic Configuration
```json
{
  "mcq_settings": {
    "offline_mode": true,
    "default_difficulty": "medium"
  },
  "local_model": {
    "primary_model": "mistralai/Mistral-7B-Instruct-v0.2",
    "quantization": {
      "load_in_4bit": true
    }
  }
}
```

### Advanced Configuration
See `config/offline_mcq_config.json` for complete configuration options.

## Testing

Run the test script to verify offline MCQ generation:

```bash
python test_offline_mcq_default.py
```

This will test:
- Model fallback configuration
- Single MCQ generation
- Multiple MCQ generation

## Memory Requirements

With 4-bit quantization:
- **Mistral-7B**: ~4GB VRAM
- **Llama-3-8B**: ~5GB VRAM
- **Qwen2.5-7B**: ~4GB VRAM
- **Gemma-2-9B**: ~5GB VRAM

## Troubleshooting

### Common Issues

1. **"Offline generator not available"**
   - Check internet connection for model download
   - Ensure sufficient disk space
   - Verify CUDA/GPU setup if using GPU

2. **"Model loading failed"**
   - Try different fallback models
   - Check available memory
   - Enable CPU-only mode if needed

3. **Poor quality questions**
   - Try different difficulty levels
   - Provide more context in input text
   - Consider training custom models

### Debug Mode

Enable detailed logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Performance Tips

1. **First Run**: Model download may take time
2. **Memory**: Use 4-bit quantization for limited VRAM
3. **Speed**: Keep models loaded between generations
4. **Quality**: Longer context usually produces better questions

## Integration with Training

When you train custom models:
1. Models are automatically detected
2. Custom models take priority over base models
3. Fallback to base models if custom models fail
4. Seamless switching between trained and untrained modes

## API Reference

### MCQManager Methods

- `set_offline_mode(offline: bool)` - Enable/disable offline mode
- `is_offline_available() -> bool` - Check if offline mode is ready
- `generate_quiz_async(context, difficulty)` - Generate single MCQ
- `generate_multiple_questions(context, num, difficulty)` - Generate multiple MCQs

### LocalModelInference Methods

- `load_model(model_path, base_model)` - Load specific model
- `_load_base_model_with_fallback(model)` - Load with fallback support
- `generate_text(prompt, **kwargs)` - Generate text with model

## Best Practices

1. **Always test offline mode** before relying on it
2. **Monitor memory usage** during generation
3. **Use appropriate difficulty levels** for your content
4. **Provide sufficient context** for better questions
5. **Consider training custom models** for specialized content

## Future Enhancements

- Automatic model selection based on content type
- Dynamic difficulty adjustment
- Caching of generated questions
- Support for more model formats
- Integration with online/offline hybrid mode
