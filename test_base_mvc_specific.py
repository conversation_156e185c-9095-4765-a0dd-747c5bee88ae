#!/usr/bin/env python3
"""
Base MVC Specific Import Test

This script investigates exactly what in base_mvc is causing torch to be imported.
"""

import sys
import os

# Add the src directory to path for local imports
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

def test_base_mvc_step_by_step():
    """Test base_mvc imports step by step"""
    
    heavy_modules = ['torch', 'transformers', 'peft', 'datasets']
    
    print("🔍 Testing base_mvc imports step by step...")
    
    # Step 1: Check initial state
    print("\n📦 Step 1: Initial state")
    before = [m for m in heavy_modules if m in sys.modules]
    print(f"Heavy modules before: {before}")
    
    # Step 2: Import ABC
    print("\n📦 Step 2: Import ABC")
    before = [m for m in heavy_modules if m in sys.modules]
    from abc import ABC, abstractmethod
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after ABC: {after}")
    if newly_imported:
        print(f"⚠️ ABC imported: {newly_imported}")
    
    # Step 3: Import typing
    print("\n📦 Step 3: Import typing")
    before = after
    from typing import Any, Dict, Optional, Callable, List
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after typing: {after}")
    if newly_imported:
        print(f"⚠️ typing imported: {newly_imported}")
    
    # Step 4: Import PyQt5.QtCore
    print("\n📦 Step 4: Import PyQt5.QtCore")
    before = after
    from PyQt5.QtCore import QObject, pyqtSignal
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after PyQt5.QtCore: {after}")
    if newly_imported:
        print(f"⚠️ PyQt5.QtCore imported: {newly_imported}")
    
    # Step 5: Import PyQt5.QtWidgets
    print("\n📦 Step 5: Import PyQt5.QtWidgets")
    before = after
    from PyQt5.QtWidgets import QWidget
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after PyQt5.QtWidgets: {after}")
    if newly_imported:
        print(f"⚠️ PyQt5.QtWidgets imported: {newly_imported}")
    
    # Step 6: Import logging
    print("\n📦 Step 6: Import logging")
    before = after
    import logging
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    print(f"Heavy modules after logging: {after}")
    if newly_imported:
        print(f"⚠️ logging imported: {newly_imported}")
    
    return newly_imported

def test_base_mvc_module_import():
    """Test importing the base_mvc module directly"""
    
    heavy_modules = ['torch', 'transformers', 'peft', 'datasets']
    
    print("🔍 Testing base_mvc module import...")
    
    # Check what modules are loaded when we import the module
    print("Modules before importing base_mvc:")
    before_modules = set(sys.modules.keys())
    before_heavy = [m for m in heavy_modules if m in sys.modules]
    print(f"Heavy modules before: {before_heavy}")
    
    # Import the module
    import knowledge_app.ui.mvc.base_mvc
    
    after_modules = set(sys.modules.keys())
    after_heavy = [m for m in heavy_modules if m in sys.modules]
    newly_imported_heavy = [m for m in after_heavy if m not in before_heavy]
    
    print(f"Heavy modules after: {after_heavy}")
    if newly_imported_heavy:
        print(f"⚠️ base_mvc imported: {newly_imported_heavy}")
    
    # Check what new modules were loaded
    new_modules = after_modules - before_modules
    knowledge_app_modules = [m for m in new_modules if 'knowledge_app' in m]
    print(f"New knowledge_app modules: {knowledge_app_modules}")
    
    # Look for any torch-related modules
    torch_modules = [m for m in new_modules if 'torch' in m.lower()]
    if torch_modules:
        print(f"Torch-related modules found: {torch_modules[:10]}...")  # Show first 10
    
    return newly_imported_heavy

def test_knowledge_app_ui_import():
    """Test importing knowledge_app.ui package"""
    
    heavy_modules = ['torch', 'transformers', 'peft', 'datasets']
    
    print("🔍 Testing knowledge_app.ui package import...")
    
    # Check what modules are loaded when we import the package
    print("Modules before importing knowledge_app.ui:")
    before_modules = set(sys.modules.keys())
    before_heavy = [m for m in heavy_modules if m in sys.modules]
    print(f"Heavy modules before: {before_heavy}")
    
    # Import the package
    import knowledge_app.ui
    
    after_modules = set(sys.modules.keys())
    after_heavy = [m for m in heavy_modules if m in sys.modules]
    newly_imported_heavy = [m for m in after_heavy if m not in before_heavy]
    
    print(f"Heavy modules after: {after_heavy}")
    if newly_imported_heavy:
        print(f"⚠️ knowledge_app.ui imported: {newly_imported_heavy}")
    
    # Check what new modules were loaded
    new_modules = after_modules - before_modules
    knowledge_app_modules = [m for m in new_modules if 'knowledge_app' in m]
    print(f"New knowledge_app modules: {knowledge_app_modules}")
    
    return newly_imported_heavy

def main():
    """Run the specific test"""
    print("🚀 Starting base_mvc specific import test...")
    print("=" * 80)
    
    # Test knowledge_app.ui package first
    ui_culprits = test_knowledge_app_ui_import()
    
    print("\n" + "=" * 40)
    
    # Test step by step
    step_by_step_culprits = test_base_mvc_step_by_step()
    
    print("\n" + "=" * 40)
    
    # Test module imports
    module_import_culprits = test_base_mvc_module_import()
    
    print("\n" + "=" * 80)
    print("📊 BASE MVC TEST RESULTS")
    print("=" * 80)
    
    if ui_culprits or step_by_step_culprits or module_import_culprits:
        print(f"❌ Heavy modules imported:")
        if ui_culprits:
            print(f"   UI package: {ui_culprits}")
        if step_by_step_culprits:
            print(f"   Step by step: {step_by_step_culprits}")
        if module_import_culprits:
            print(f"   Module import: {module_import_culprits}")
        return 1
    else:
        print("✅ No heavy modules imported!")
        return 0

if __name__ == "__main__":
    sys.exit(main())
