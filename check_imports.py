import sys
import os

print("Python version:", sys.version)
print("Python executable:", sys.executable)
print("Current working directory:", os.getcwd())

# Print the system path
print("\nSystem paths:")
for path in sys.path:
    print("-", path)

# Try importing ML dependencies
print("\nTrying to import ML dependencies:")
try:
    import torch
    print("✓ PyTorch:", torch.__version__)
    print("  Located at:", torch.__file__)
except ImportError as e:
    print("✗ PyTorch:", e)

try:
    import transformers
    print("✓ Transformers:", transformers.__version__)
except ImportError as e:
    print("✗ Transformers:", e)

try:
    from sentence_transformers import SentenceTransformer
    print("✓ SentenceTransformer available")
except ImportError as e:
    print("✗ SentenceTransformer:", e)

try:
    import peft
    print("✓ PEFT:", peft.__version__)
except ImportError as e:
    print("✗ PEFT:", e)

# Try importing app-specific modules
print("\nTrying to import app-specific modules:")
try:
    import knowledge_app
    print("✓ knowledge_app package found")
except ImportError as e:
    print("✗ knowledge_app:", e)

try:
    from knowledge_app.core.gpu_manager import GPUManager
    print("✓ GPUManager found")
except ImportError as e:
    print("✗ GPUManager:", e)
