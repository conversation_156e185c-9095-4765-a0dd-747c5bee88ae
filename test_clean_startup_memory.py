#!/usr/bin/env python3
"""
Clean Startup Memory Test

This script tests the memory usage of a clean application startup without
triggering any heavy imports.
"""

import sys
import os
import psutil

# Add the src directory to path for local imports
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

def get_memory_usage():
    """Get current memory usage in MB"""
    process = psutil.Process()
    return process.memory_info().rss / (1024 * 1024)

def test_clean_startup():
    """Test clean startup memory usage"""
    
    heavy_modules = ['torch', 'transformers', 'peft', 'datasets']
    
    print("🚀 Testing clean startup memory usage...")
    print("=" * 60)
    
    # Step 1: Check initial memory
    initial_memory = get_memory_usage()
    print(f"📊 Initial memory usage: {initial_memory:.1f} MB")
    
    # Step 2: Check heavy modules before any imports
    before_heavy = [m for m in heavy_modules if m in sys.modules]
    print(f"📦 Heavy modules before imports: {before_heavy}")
    
    # Step 3: Import only the essential components for startup
    print("\n🔄 Importing essential startup components...")
    
    # Import config manager (should be lightweight)
    from knowledge_app.core.config_manager import ConfigManager, get_config
    config_memory = get_memory_usage()
    print(f"📊 Memory after config_manager: {config_memory:.1f} MB (+{config_memory - initial_memory:.1f} MB)")
    
    # Import enterprise design system (should be lightweight)
    from knowledge_app.ui.enterprise_design_system import get_design_system
    design_memory = get_memory_usage()
    print(f"📊 Memory after design_system: {design_memory:.1f} MB (+{design_memory - config_memory:.1f} MB)")
    
    # Import enterprise style manager (should be lightweight)
    from knowledge_app.ui.enterprise_style_manager import get_style_manager
    style_memory = get_memory_usage()
    print(f"📊 Memory after style_manager: {style_memory:.1f} MB (+{style_memory - design_memory:.1f} MB)")
    
    # Import main window MVC (should be lightweight now)
    from knowledge_app.ui.mvc.main_window_mvc import MainWindowModel, MainWindowView, MainWindowController
    mvc_memory = get_memory_usage()
    print(f"📊 Memory after main_window_mvc: {mvc_memory:.1f} MB (+{mvc_memory - style_memory:.1f} MB)")
    
    # Import enterprise main window (should be lightweight)
    from knowledge_app.ui.enterprise_main_window import create_enterprise_main_window
    enterprise_memory = get_memory_usage()
    print(f"📊 Memory after enterprise_main_window: {enterprise_memory:.1f} MB (+{enterprise_memory - mvc_memory:.1f} MB)")
    
    # Step 4: Check heavy modules after essential imports
    after_heavy = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after_heavy if m not in before_heavy]
    
    print(f"\n📦 Heavy modules after essential imports: {after_heavy}")
    if newly_imported:
        print(f"⚠️ Heavy modules imported during startup: {newly_imported}")
    else:
        print("✅ No heavy modules imported during startup!")
    
    # Step 5: Calculate total overhead
    total_overhead = enterprise_memory - initial_memory
    print(f"\n📊 Total startup overhead: {total_overhead:.1f} MB")
    
    # Step 6: Evaluate results
    print("\n" + "=" * 60)
    print("📊 CLEAN STARTUP MEMORY TEST RESULTS")
    print("=" * 60)
    
    success = True
    
    # Check if heavy modules were imported
    if newly_imported:
        print(f"❌ Heavy modules imported: {newly_imported}")
        success = False
    else:
        print("✅ No heavy modules imported during startup")
    
    # Check final memory usage
    if enterprise_memory < 300:
        print(f"✅ Final memory usage acceptable: {enterprise_memory:.1f} MB < 300 MB")
    else:
        print(f"⚠️ Final memory usage high: {enterprise_memory:.1f} MB >= 300 MB")
        success = False
    
    # Check startup overhead
    if total_overhead < 100:
        print(f"✅ Startup overhead reasonable: {total_overhead:.1f} MB < 100 MB")
    else:
        print(f"⚠️ Startup overhead high: {total_overhead:.1f} MB >= 100 MB")
        success = False
    
    if success:
        print("\n🎉 MEMORY OPTIMIZATION: SUCCESS!")
        print("The application now has fast startup with low memory usage.")
        return 0
    else:
        print("\n⚠️ MEMORY OPTIMIZATION: NEEDS IMPROVEMENT")
        print("Some heavy modules are still being imported during startup.")
        return 1

def main():
    """Run the clean startup test"""
    return test_clean_startup()

if __name__ == "__main__":
    sys.exit(main())
