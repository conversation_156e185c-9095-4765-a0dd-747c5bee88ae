name: Knowledge App CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.10', '3.11', '3.12']

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        if [ -f requirements/requirements-dev.txt ]; then pip install -r requirements/requirements-dev.txt; else pip install -r requirements.txt; fi
        # Install package in development mode
        pip install -e .

    - name: Lint with flake8
      run: |
        # Install flake8 if not already included in requirements
        pip install flake8
        # stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=120 --statistics

    - name: Test with pytest
      run: |
        pytest -v --junitxml=test-results.xml

    - name: Upload test results
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: test-results-${{ matrix.python-version }}
        path: test-results.xml

  integration-tests:
    needs: test
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        if [ -f requirements/requirements-dev.txt ]; then pip install -r requirements/requirements-dev.txt; else pip install -r requirements.txt; fi
        # Install package in development mode
        pip install -e .

    - name: Run integration tests
      run: |
        pytest -v src/knowledge_app/tests/test_integration.py --junitxml=integration-results.xml

    - name: Upload integration test results
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: integration-test-results
        path: integration-results.xml
