# Knowledge Quiz Application

A professional, enterprise-grade knowledge quiz application built with Python and PyQt5, featuring AI-powered question generation and a modern, responsive UI.

## Features

- **Enterprise UI**: Professional interface with seductive animations and responsive design
- **AI-Powered**: Local and cloud-based question generation using fine-tuned language models
- **Process-Based Architecture**: Isolated model server for maximum stability and performance
- **MVC Pattern**: Clean separation of concerns with Model-View-Controller architecture
- **Advanced Training**: Support for LoRA/QLoRA fine-tuning with real-time progress tracking
- **Multi-Modal**: Document processing with OCR and layout analysis
- **Cross-Platform**: Windows, macOS, and Linux support with hardware-optimized installations

## Architecture

The application follows enterprise architecture principles with a focus on stability and maintainability:

### **Process-Based Model Server**
- AI models run in completely isolated processes
- Eliminates UI crashes from affecting model inference
- Superior memory management and resource isolation
- Easier debugging and monitoring

### **MVC Pattern Throughout**
- **Model**: Business logic and data management
- **View**: Pure UI components that only handle display
- **Controller**: Mediates between Model and View, handles user interactions

### **Enterprise Design System**
- Unified styling through `EnterpriseStyleManager`
- Consistent color palette, typography, and spacing
- Theme support (dark/light) with runtime switching
- Component-based styling for maintainability

### **Key Architectural Decisions**

1. **Process Isolation**: Models run in separate processes via `model_server.py`
2. **Truth-Based Estimation**: FIRE v2.1 estimator uses actual trainer data, not assumptions
3. **Centralized Configuration**: Single source of truth via `ConfigManager`
4. **Worker Thread Pattern**: Long-running tasks never block the UI
5. **Enterprise Styling**: No hardcoded styles, everything through the design system

## System Requirements

- Python 3.8 or higher
- 4GB RAM minimum (8GB recommended)
- 10GB free disk space
- CUDA-compatible GPU (optional, for acceleration)

## Quick Start

For detailed installation instructions, see [INSTALL.md](INSTALL.md).

### Hardware Detection
```bash
python check_attention.py
```

### Install & Run
```bash
# For CUDA systems
pip install -e .[cuda]

# For CPU-only systems
pip install -e .[cpu]

# Run the application
python main.py
```

The application includes automatic hardware detection and will guide you through the optimal installation for your system.

## Configuration

The application uses a hierarchical configuration system:

```json
{
    "app_name": "Knowledge Quiz",
    "org_name": "KnowledgeApp",
    "debug_mode": false,
    "max_memory_usage_gb": 8,
    "enable_gpu": true,
    "model": {
        "name": "mistral-7b-instruct",
        "device_map": "auto",
        "quantization": "4bit"
    },
    "quiz": {
        "default_category": "general",
        "default_difficulty": 1,
        "questions_per_session": 10
    }
}
```

Configuration can be accessed using dot notation:
```python
config.get_setting("model.name")  # Returns "mistral-7b-instruct"
```

## Development

### Project Structure

```
knowledge_app/
├── src/
│   └── knowledge_app/
│       ├── core/           # Core business logic
│       │   ├── interfaces/   # Abstract interfaces
│       │   ├── models/       # Domain models
│       │   └── services/     # Business services
│       ├── infrastructure/ # Implementation details
│       │   ├── persistence/  # Data storage
│       │   └── external/     # External services
│       ├── presentation/   # UI components
│       │   ├── views/        # PyQt5 views
│       │   └── viewmodels/   # View logic
│       └── utils/          # Shared utilities
├── tests/                # Test suite
└── resources/           # Application resources
```

### Setting up the Development Environment

1. Install development dependencies:
```bash
pip install -r requirements-dev.txt
```

2. Install pre-commit hooks:
```bash
pre-commit install
```

### Code Quality Tools

- **Black**: Code formatting
- **isort**: Import sorting
- **flake8**: Style guide enforcement
- **mypy**: Static type checking
- **pytest**: Unit testing
- **bandit**: Security checks

### Running Tests

```bash
# Run all tests
pytest

# Run tests with coverage report
pytest --cov=knowledge_app

# Run specific test file
pytest tests/test_app_init.py
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and ensure code quality
5. Submit a pull request

### Development Guidelines

1. Follow SOLID principles
2. Write unit tests for new features
3. Update documentation
4. Use type hints
5. Follow the established architecture

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, please:
1. Check the documentation
2. Search existing issues
3. Create a new issue if needed

## Acknowledgments

- PyQt5 for the UI framework
- Hugging Face for transformer models
- NLTK for natural language processing
- The open source community