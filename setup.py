"""
Setup script for the Knowledge App package
"""

from setuptools import setup, find_packages

setup(
    name="knowledge_app",
    version="0.1.0",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    install_requires=[
        "PyQt5>=5.15.0",
        "pydot>=1.4.2",
        "numpy>=1.19.0",
        "nltk>=3.6.0",
        "pillow>=8.0.0",
    ],
    extras_require={
        "dev": [
            "pytest>=6.0.0",
            "pytest-qt>=4.0.0",
            "black>=21.0.0",
            "flake8>=3.9.0",
        ],
        "ml": [
            "torch>=2.1.0",
            "transformers>=4.35.0",
            "peft>=0.7.0",
            "accelerate>=0.20.0",
            "bitsandbytes>=0.39.0",
            "sentence-transformers>=2.2.0",
            "faiss-cpu>=1.7.0",
        ]
    },
    python_requires=">=3.8",
    author="Your Name",
    author_email="<EMAIL>",
    description="An AI-powered knowledge quiz application",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    keywords="quiz, education, ai, machine-learning",
    url="https://github.com/yourusername/knowledge_app",
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Education",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
)
