"""
Model Manager for Knowledge App

This module provides model management functionality including training,
evaluation, and inference.
"""

# CRITICAL MEMORY FIX: Import only lightweight modules during startup
import os
import logging
import shutil
import tempfile
import threading
import time
import asyncio
import psutil
import gc
import weakref
from pathlib import Path
from typing import Dict, Any, Optional, Union, List, Callable, Tuple
from .cache_manager import BaseCacheManager
from .interfaces import IModelManager
from ..utils.resource_manager import ResourceManager
from .app_config import AppConfig

# CRITICAL MEMORY FIX: Heavy ML imports will be done lazily when model operations are first used
# This prevents loading PyTorch, transformers, peft during application startup

logger = logging.getLogger(__name__)

DEFAULT_MODEL = "facebook/opt-125m"  # Smaller, publicly available model
MODEL_INIT_TIMEOUT = 60  # Timeout in seconds for model initialization

class ModelInitializationError(Exception):
    """Exception raised when model initialization fails or times out"""
    pass

class ModelInitializationTimeout(Exception):
    """Exception raised when model initialization times out"""
    pass

class ModelManager(BaseCacheManager, IModelManager):
    """Manages AI model operations including training and inference"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, config: Optional[Union[str, Dict[str, Any], AppConfig]] = None):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ModelManager, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance
            
    def __init__(self, config: Optional[Union[str, Dict[str, Any], AppConfig]] = None):
        if self._initialized:
            return
            
        self._initialized = True
        
        # Convert string path to config dict if needed
        if isinstance(config, str):
            config = {
                'base_path': config,
                'max_size': 8 * 1024 * 1024 * 1024,  # 8GB
                'cleanup_threshold': 0.85,
                'cache_expiry': 3600  # 1 hour
            }
        elif isinstance(config, AppConfig):
            config = {
                'base_path': config.get_setting('paths.models'),
                'max_size': config.get_setting('storage_config.model_cache_limit'),
                'cleanup_threshold': config.get_setting('storage_config.cleanup_threshold'),
                'cache_expiry': config.get_setting('storage_settings.cache_ttl')
            }
        elif config is None:
            config = {
                'base_path': 'data/models',
                'max_size': 8 * 1024 * 1024 * 1024,  # 8GB
                'cleanup_threshold': 0.85,
                'cache_expiry': 3600  # 1 hour
            }
            
        super().__init__(config)

        # Initialize GPU manager with lazy import
        try:
            from .gpu_manager import GPUManager
            self._gpu_manager = GPUManager()
            self.device = self._gpu_manager.device
        except ImportError as e:
            logger.warning(f"GPUManager not available: {e}")
            # Fallback to CPU device
            import torch
            self._gpu_manager = None
            self.device = torch.device('cpu')
        
        # Model state
        self._model = None
        self._tokenizer = None  # Initialize tokenizer attribute
        self._stop_requested = False
        self._model_lock = threading.Lock()
        self._init_thread = None
        self._optimizer = None
        # CRITICAL MEMORY FIX: Defer GradScaler initialization to avoid importing torch during startup
        self._scaler = None  # Will be initialized lazily when needed
        
        # Memory tracking
        self._models = weakref.WeakValueDictionary()
        self._tokenizers = weakref.WeakValueDictionary()
        self._active_models = set()
        self._resource_manager = ResourceManager()
        self._memory_monitor_thread = None
        self._stop_monitoring = threading.Event()
        self._memory_check_interval = 60  # Check every 60 seconds
        
        # Memory monitoring is now handled centrally by ShutdownManager
        # self._start_memory_monitoring()  # Disabled to prevent redundant monitoring
        
        # CRITICAL MEMORY FIX: Defer GPU logging to avoid importing torch during startup
        # GPU availability will be logged when actually needed
        logger.info("ModelManager initialized - GPU availability will be checked when needed")
            
    def _start_memory_monitoring(self):
        """Start memory monitoring thread"""
        self._stop_monitoring.clear()
        self._memory_monitor_thread = threading.Thread(
            target=self._monitor_memory_loop,
            name="ModelMemoryMonitor",
            daemon=True
        )
        self._memory_monitor_thread.start()
        
    def _stop_memory_monitoring(self):
        """Stop memory monitoring thread"""
        if self._memory_monitor_thread is not None:
            self._stop_monitoring.set()
            if self._memory_monitor_thread.is_alive():
                self._memory_monitor_thread.join(timeout=2.0)
            self._memory_monitor_thread = None
            
    def _monitor_memory_loop(self):
        """Memory monitoring loop"""
        while not self._stop_monitoring.is_set():
            try:
                self._check_memory_usage()
                self._stop_monitoring.wait(self._memory_check_interval)
            except Exception as e:
                logger.error(f"Error in memory monitoring loop: {e}")
                self._stop_monitoring.wait(300)  # Wait longer after error
                
    def _check_memory_usage(self):
        """Check memory usage and cleanup if needed"""
        try:
            # CRITICAL MEMORY FIX: Import torch only when memory checking is needed
            try:
                import torch
                if self.device.type == 'cuda':
                    # Get GPU memory stats
                    allocated = torch.cuda.memory_allocated() / 1024**3  # GB
                    reserved = torch.cuda.memory_reserved() / 1024**3  # GB
                    total = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB

                    # Check if cleanup needed
                    if allocated / total > 0.9:  # Over 90% usage
                        logger.warning(f"High GPU memory usage: {allocated:.2f}GB/{total:.2f}GB")
                        self._cleanup_gpu_memory()
            except ImportError:
                # torch not available, skip GPU memory check
                pass

            # Check system memory
            memory = psutil.virtual_memory()
            if memory.percent > 90:  # Over 90% usage
                logger.warning(f"High system memory usage: {memory.percent}%")
                self._cleanup_system_memory()

        except Exception as e:
            logger.error(f"Error checking memory usage: {e}")
            
    def _cleanup_gpu_memory(self):
        """Clean up GPU memory"""
        try:
            # CRITICAL MEMORY FIX: Import torch only when GPU cleanup is needed
            import torch
            if self.device.type == 'cuda':
                # Clear unused memory
                torch.cuda.empty_cache()

                # Move least recently used models to CPU
                with self._model_lock:
                    active_models = list(self._active_models)
                    if len(active_models) > 1:  # Keep at least one model
                        for model_key in active_models[1:]:
                            if model_key in self._models:
                                model = self._models[model_key]
                                if model is not None and next(model.parameters()).device.type == 'cuda':
                                    model.cpu()
                                    logger.info(f"Moved model {model_key} to CPU")

        except Exception as e:
            logger.error(f"Error cleaning up GPU memory: {e}")
            
    def _cleanup_system_memory(self):
        """Clean up system memory"""
        try:
            # Clear Python cache
            gc.collect()

            # Clear model cache
            with self._model_lock:
                self._models.clear()
                self._tokenizers.clear()

            # Clear CUDA cache if using GPU
            try:
                # CRITICAL MEMORY FIX: Import torch only when CUDA cleanup is needed
                import torch
                if self.device.type == 'cuda':
                    torch.cuda.empty_cache()
            except ImportError:
                # torch not available, skip CUDA cleanup
                pass

        except Exception as e:
            logger.error(f"Error cleaning up system memory: {e}")
            
    def create_model_architecture(self) -> "torch.nn.Module":
        """Create the model architecture"""
        try:
            # CRITICAL MEMORY FIX: Import torch only when model creation is needed
            import torch

            # Create a simple language model architecture for token sequences
            vocab_size = 50257  # GPT-2 vocab size (default for most tokenizers)
            embed_dim = 256
            hidden_dim = 512

            class SimpleLanguageModel(torch.nn.Module):
                def __init__(self, vocab_size, embed_dim, hidden_dim):
                    super().__init__()
                    self.embedding = torch.nn.Embedding(vocab_size, embed_dim)
                    self.transformer = torch.nn.Sequential(
                        torch.nn.Linear(embed_dim, hidden_dim),
                        torch.nn.ReLU(),
                        torch.nn.Dropout(0.1),
                        torch.nn.Linear(hidden_dim, hidden_dim),
                        torch.nn.ReLU(),
                        torch.nn.Dropout(0.1),
                        torch.nn.Linear(hidden_dim, vocab_size)  # Output vocab_size for next token prediction
                    )

                def forward(self, input_ids):
                    # Convert token IDs to embeddings
                    embeddings = self.embedding(input_ids)  # [batch_size, seq_len, embed_dim]

                    # For simplicity, we'll use the mean of embeddings as input to the transformer
                    # In a real transformer, this would be more sophisticated
                    pooled = embeddings.mean(dim=1)  # [batch_size, embed_dim]

                    # Pass through transformer layers
                    logits = self.transformer(pooled)  # [batch_size, vocab_size]

                    # Expand to match sequence length for language modeling
                    seq_len = input_ids.size(1)
                    logits = logits.unsqueeze(1).expand(-1, seq_len, -1)  # [batch_size, seq_len, vocab_size]

                    return logits

            model = SimpleLanguageModel(vocab_size, embed_dim, hidden_dim).to(self.device)
            logger.info(f"Model architecture created on {self.device}")
            return model

        except Exception as e:
            logger.error(f"Error creating model architecture: {e}")
            raise
            
    def prepare_training_data(self) -> List[Tuple["torch.Tensor", "torch.Tensor"]]:
        """Prepare data for training from uploaded books with multimodal processing"""
        try:
            # CRITICAL MEMORY FIX: Import torch and transformers only when training data preparation is needed
            import torch
            from transformers import AutoTokenizer

            logger.info("Loading training data from uploaded books with multimodal processing...")

            # Get uploaded books directory
            from ..core.app_config import AppConfig
            config = AppConfig()
            books_dir = config.get_setting('paths.uploaded_books', 'data/uploaded_books')

            # Find all document files (not just text files)
            import os
            document_files = []
            if os.path.exists(books_dir):
                for file in os.listdir(books_dir):
                    file_path = os.path.join(books_dir, file)
                    # Include PDFs, images, and text files
                    if file.lower().endswith(('.pdf', '.txt', '.png', '.jpg', '.jpeg', '.docx')):
                        document_files.append(file_path)

            logger.info(f"Found {len(document_files)} documents for processing")

            # Process documents with multimodal pipeline
            processed_text_chunks = self._process_documents_for_training(document_files)

            if not processed_text_chunks:
                logger.warning("No processed text chunks available, using minimal training data")
                # Create minimal training data for testing
                data = torch.randn(100, 512).to(self.device)
                labels = torch.randint(0, 2, (100,)).to(self.device)
                return [(data[i:i+16], labels[i:i+16]) for i in range(0, 100, 16)]

            # Use processed text chunks directly
            combined_text = "\n\n".join(processed_text_chunks)
            logger.info(f"Using {len(processed_text_chunks)} processed text chunks, total {len(combined_text)} characters")

            # Ensure tokenizer is initialized
            if not self._tokenizer:
                logger.info("Initializing tokenizer for training data preparation...")
                try:
                    self._tokenizer = AutoTokenizer.from_pretrained(
                        DEFAULT_MODEL,
                        use_fast=True,
                        trust_remote_code=True
                    )
                    # Add padding token if it doesn't exist
                    if self._tokenizer.pad_token is None:
                        self._tokenizer.pad_token = self._tokenizer.eos_token
                    logger.info(f"Tokenizer initialized: {self._tokenizer.__class__.__name__}")
                except Exception as e:
                    logger.error(f"Failed to initialize tokenizer: {e}")
                    raise RuntimeError(f"Could not initialize tokenizer: {e}")

            # Split text into chunks for training
            max_length = 512  # Reasonable chunk size
            chunks = []
            words = combined_text.split()

            for i in range(0, len(words), max_length // 2):  # Overlap chunks
                chunk = " ".join(words[i:i + max_length])
                if len(chunk.strip()) > 50:  # Only use substantial chunks
                    chunks.append(chunk)

            logger.info(f"Created {len(chunks)} training chunks")

            # Tokenize chunks
            training_pairs = []
            for i, chunk in enumerate(chunks[:1000]):  # Limit to 1000 chunks for memory
                try:
                    # Tokenize input
                    tokens = self._tokenizer(
                        chunk,
                        max_length=max_length,
                        truncation=True,
                        padding='max_length',
                        return_tensors='pt'
                    )

                    input_ids = tokens['input_ids'].squeeze(0).to(self.device)

                    # Create labels (for language modeling, labels = input_ids shifted)
                    labels = input_ids.clone()

                    training_pairs.append((input_ids, labels))

                except Exception as e:
                    logger.warning(f"Error tokenizing chunk {i}: {e}")
                    continue

            if not training_pairs:
                logger.error("No valid training data created")
                raise RuntimeError("Failed to create training data from books")

            logger.info(f"Created {len(training_pairs)} training examples")
            return training_pairs

        except Exception as e:
            logger.error(f"Error preparing training data: {e}")
            raise

    def _process_documents_for_training(self, document_files: List[str]) -> List[str]:
        """Process documents using unified training data processor"""
        try:
            from .training_data_processor import TrainingDataProcessor

            logger.info(f"🚀 Processing {len(document_files)} documents for training with multimodal pipeline...")

            # Initialize the unified processor
            processor = TrainingDataProcessor()

            # Process all documents
            all_chunks = processor.process_documents_for_training(
                document_files,
                progress_callback=lambda msg: logger.info(f"📄 {msg}")
            )

            logger.info(f"✅ Total text chunks extracted: {len(all_chunks)}")
            return all_chunks

        except Exception as e:
            logger.error(f"❌ Error in document processing pipeline: {e}")
            return []

    def _check_initialization_timeout(self):
        """Check if model initialization has timed out"""
        if self._initialization_start_time is None:
            return
            
        elapsed_time = time.time() - self._initialization_start_time
        if elapsed_time > MODEL_INIT_TIMEOUT:
            error_report = self._generate_error_report()
            logger.error(f"Model initialization timed out after {elapsed_time:.1f} seconds")
            logger.error("Error Report:\n" + error_report)
            raise ModelInitializationTimeout(
                f"Model initialization timed out after {elapsed_time:.1f} seconds.\n"
                f"Please check the error report in the logs for details."
            )
            
    def _generate_error_report(self) -> str:
        """Generate a detailed error report"""
        try:
            report = []
            report.append("=== Model Initialization Error Report ===")
            report.append(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # System info
            report.append("\nSystem Information:")
            report.append(f"- Device: {self.device}")
            try:
                import torch
                if torch.cuda.is_available():
                    report.append(f"- GPU: {torch.cuda.get_device_name(0)}")
                    report.append(f"- CUDA Version: {torch.version.cuda}")
                    if hasattr(self, '_gpu_manager') and self._gpu_manager:
                        mem_info = self._gpu_manager.get_memory_info()
                        report.append(f"- GPU Memory: {mem_info['allocated_gb']:.1f}GB / {mem_info['total_gb']:.1f}GB")
            except ImportError:
                report.append("- PyTorch not available")
            
            # Process info
            process = psutil.Process()
            report.append("\nProcess Information:")
            report.append(f"- CPU Usage: {process.cpu_percent()}%")
            report.append(f"- Memory Usage: {process.memory_info().rss / (1024*1024*1024):.1f}GB")
            report.append(f"- Thread Count: {process.num_threads()}")
            
            # Model info
            report.append("\nModel Information:")
            report.append(f"- Current Model Path: {self._current_model_path}")
            report.append(f"- Model Loaded: {'Yes' if self._model is not None else 'No'}")
            report.append(f"- Tokenizer Loaded: {'Yes' if self._tokenizer is not None else 'No'}")
            
            return "\n".join(report)
        except Exception as e:
            return f"Error generating report: {str(e)}"
            
    def initialize_model(self, model_path: Optional[str] = None) -> None:
        """Initialize the model with timeout"""
        self._initialization_start_time = time.time()
        
        try:
            # Start initialization in a separate thread
            init_thread = threading.Thread(target=self._initialize_model_thread, args=(model_path,))
            init_thread.start()
            
            # Wait for initialization with timeout
            init_thread.join(timeout=MODEL_INIT_TIMEOUT)
            
            if init_thread.is_alive():
                # If thread is still alive after timeout, initialization failed
                self._stop_requested = True
                error_report = self._generate_error_report()
                logger.error("Model initialization timed out. Error Report:\n" + error_report)
                raise ModelInitializationTimeout(
                    "Model initialization timed out. Check logs for detailed error report."
                )
                
        except Exception as e:
            error_report = self._generate_error_report()
            logger.error(f"Model initialization failed: {str(e)}\nError Report:\n{error_report}")
            raise ModelInitializationError(f"Model initialization failed: {str(e)}")
        finally:
            self._initialization_start_time = None
            
    def _initialize_model_thread(self, model_path: Optional[str] = None) -> None:
        """Background thread for model initialization"""
        try:
            # CRITICAL MEMORY FIX: Import torch and transformers only when model initialization is needed
            import torch
            from transformers import AutoTokenizer

            # Set process priority
            if os.name == 'nt':  # Windows
                process = psutil.Process()
                process.nice(psutil.HIGH_PRIORITY_CLASS)
                
            # Initialize model
            with self._model_lock:
                self._model = self.create_model_architecture()
                
            # Optimize for training if using GPU
            if self.device.type == 'cuda':
                self._gpu_manager.optimize_for_training()
            
            # Set model path
            self._current_model_path = model_path or DEFAULT_MODEL
            
            # Load tokenizer with fallback options
            try:
                # Try optimized settings first
                self._tokenizer = AutoTokenizer.from_pretrained(
                    self._current_model_path,
                    use_fast=True,
                    local_files_only=True,
                    use_safetensors=True,
                    low_cpu_memory_usage=True,
                    use_direct_io=True,
                    io_buffer_size=16*1024*1024
                )
            except Exception as e:
                logger.warning(f"Failed to load tokenizer with optimized settings: {e}")
                # Fallback to basic settings
                try:
                    self._tokenizer = AutoTokenizer.from_pretrained(
                        self._current_model_path,
                        use_fast=True,
                        trust_remote_code=True
                    )
                except Exception as e2:
                    logger.error(f"Failed to load tokenizer with basic settings: {e2}")
                    raise ModelInitializationError(f"Could not load tokenizer: {e2}")

            # Add padding token if it doesn't exist
            if self._tokenizer.pad_token is None:
                self._tokenizer.pad_token = self._tokenizer.eos_token
            
            # Move model to GPU efficiently with meta tensor handling
            if self.device.type == 'cuda':
                try:
                    self._model.to(
                        self.device,
                        non_blocking=True,  # Use async transfer
                        memory_format=torch.channels_last  # More efficient memory format
                    )
                except Exception as e:
                    error_msg = str(e).lower()
                    if "meta tensor" in error_msg or "cannot copy out of meta tensor" in error_msg:
                        logger.warning(f"🔧 Meta tensor error during device transfer, using to_empty(): {e}")
                        if hasattr(self._model, 'to_empty'):
                            self._model = self._model.to_empty(device=self.device)
                        else:
                            # Fallback to CPU then GPU transfer
                            self._model = self._model.cpu()
                            self._model = self._model.to(self.device)
                    else:
                        raise e
            else:
                self._model.to(self.device)
            
            # Set evaluation mode
            self._model.eval()
            
            # Enable optimizations
            if hasattr(self._model, "gradient_checkpointing_enable"):
                self._model.gradient_checkpointing_enable()
            
            if hasattr(self._model, "parallelize") and self.device.type == 'cuda':
                self._model.parallelize()
            
            # Clear GPU cache
            if self.device.type == 'cuda':
                torch.cuda.empty_cache()
            
            logger.info(f"Model initialized successfully on {self.device}")
            
        except Exception as e:
            logger.error(f"Error in model initialization thread: {str(e)}")
            self._model = None
            raise ModelInitializationError(f"Model initialization failed: {str(e)}")

    def load_model(self, model_name: str, model_type: str = "qa",
                  device: Optional["torch.device"] = None) -> Any:
        """
        Load a model into memory
        
        Args:
            model_name: Name/path of the model to load
            model_type: Type of model (qa, generation, etc.)
            device: Device to load model on
            
        Returns:
            Loaded model
        """
        try:
            # Use provided device or default
            device = device or self.device
            
            # Check if model is already loaded
            model_key = f"{model_name}:{model_type}"
            if model_key in self._models:
                model = self._models[model_key]
                if model is not None:
                    return model
                    
            # Load model
            model = self._load_model_from_path(model_name, model_type, device)
            
            # Store model reference
            self._models[model_key] = model
            self._active_models.add(model_key)
            
            # Register with resource manager
            self._resource_manager.register_resource(
                f"model:{model_key}",
                model,
                cleanup_handler=lambda: self.unload_model(model_key)
            )
            
            return model
            
        except Exception as e:
            logger.error(f"Error loading model {model_name}: {e}", exc_info=True)
            raise
            
    def _load_model_from_path(self, model_path: str, model_type: str,
                            device: "torch.device") -> Any:
        """Load model from file system with enterprise-grade optimizations"""
        try:
            # CRITICAL MEMORY FIX: Import torch and transformers only when model loading is needed
            import torch

            # Import appropriate model class based on type
            if model_type == "qa":
                from transformers import AutoModelForQuestionAnswering
                model_class = AutoModelForQuestionAnswering
            elif model_type == "generation":
                from transformers import AutoModelForCausalLM
                model_class = AutoModelForCausalLM
            else:
                raise ValueError(f"Unsupported model type: {model_type}")

            # Load model
            model = model_class.from_pretrained(model_path)
            model.to(device)

            # Enterprise Optimization: Compile model for inference speedup (20-30% improvement)
            if torch.__version__ >= "2.0.0":
                try:
                    logger.info("🚀 Compiling model with torch.compile for enterprise-grade performance")
                    model = torch.compile(model, mode="reduce-overhead")
                    logger.info("✅ Model compilation successful - expect 20-30% inference speedup")
                except Exception as compile_error:
                    logger.warning(f"Model compilation failed, using uncompiled model: {compile_error}")
            else:
                logger.info("PyTorch < 2.0.0 detected, skipping model compilation")

            return model

        except Exception as e:
            logger.error(f"Error loading model from {model_path}: {e}", exc_info=True)
            raise
            
    def unload_model(self, model_key: str) -> None:
        """
        Unload a model from memory
        
        Args:
            model_key: Key of model to unload
        """
        try:
            if model_key in self._models:
                model = self._models[model_key]
                if model is not None:
                    # Move to CPU first
                    if hasattr(model, 'cpu'):
                        model.cpu()
                    
                    # Delete model
                    del self._models[model_key]
                    self._active_models.remove(model_key)
                    
                    # Force garbage collection
                    gc.collect()
                    try:
                        import torch
                        if torch.cuda.is_available():
                            torch.cuda.empty_cache()
                    except ImportError:
                        pass
                        
        except Exception as e:
            logger.error(f"Error unloading model {model_key}: {e}")
            
    def cleanup_models(self) -> None:
        """Clean up all loaded models"""
        try:
            # Get copy of active models since we'll modify the set
            active_models = self._active_models.copy()
            
            # Unload each model
            for model_key in active_models:
                self.unload_model(model_key)
                
            # Clear dictionaries
            self._models.clear()
            self._tokenizers.clear()
            self._active_models.clear()
            
            # Force cleanup
            gc.collect()
            try:
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            except ImportError:
                pass
                
        except Exception as e:
            logger.error(f"Error cleaning up models: {e}")
            
    def get_model(self, model_key: str) -> Optional[Any]:
        """
        Get a loaded model
        
        Args:
            model_key: Key of model to get
            
        Returns:
            Model if loaded, None otherwise
        """
        return self._models.get(model_key)
        
    def is_model_loaded(self, model_key: str) -> bool:
        """
        Check if a model is loaded
        
        Args:
            model_key: Key of model to check
            
        Returns:
            True if model is loaded
        """
        return model_key in self._active_models
        
    def get_active_models(self) -> List[str]:
        """
        Get list of active model keys

        Returns:
            List of active model keys
        """
        return list(self._active_models)

    def delete_model(self, model_name: str) -> bool:
        """
        Delete a model file from disk

        Args:
            model_name: Name of the model to delete

        Returns:
            True if model was deleted successfully, False otherwise
        """
        try:
            # First unload the model if it's loaded
            model_key = f"{model_name}:default"
            if model_key in self._active_models:
                self.unload_model(model_key)

            # Find and delete the model file
            model_path = Path(self.base_path) / f"{model_name}.pt"
            if model_path.exists():
                model_path.unlink()
                logger.info(f"Deleted model file: {model_path}")
                return True
            else:
                logger.warning(f"Model file not found: {model_path}")
                return False

        except Exception as e:
            logger.error(f"Error deleting model {model_name}: {e}")
            return False

    def model_exists(self, model_name: str) -> bool:
        """
        Check if a model exists on disk

        Args:
            model_name: Name of the model to check

        Returns:
            True if model exists, False otherwise
        """
        try:
            model_path = Path(self.base_path) / f"{model_name}.pt"
            return model_path.exists()
        except Exception as e:
            logger.error(f"Error checking if model {model_name} exists: {e}")
            return False

    def save_model(self, model: Any, model_name: str) -> bool:
        """
        Save a model to disk

        Args:
            model: The model to save
            model_name: Name to save the model as

        Returns:
            True if model was saved successfully, False otherwise
        """
        try:
            model_path = Path(self.base_path) / f"{model_name}.pt"
            model_path.parent.mkdir(parents=True, exist_ok=True)

            # Get model state
            if hasattr(model, 'get_state'):
                model_state = model.get_state()
            elif hasattr(model, 'state_dict'):
                model_state = model.state_dict()
            else:
                model_state = model

            # Save model
            torch.save({"model_state": model_state}, model_path)
            logger.info(f"Saved model to: {model_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving model {model_name}: {e}")
            return False

    def __del__(self):
        """Cleanup on deletion"""
        try:
            self.cleanup_models()
        except Exception as e:
            logger.error(f"Error during ModelManager cleanup: {e}")

    def train_model(
        self,
        config: Dict[str, Any],
        progress_callback: Optional[Callable[[str], None]] = None,
        phase_callback: Optional[Callable[[int], None]] = None
    ) -> None:
        """
        Train the model with the given configuration
        
        Args:
            config: Training configuration
            progress_callback: Callback for progress messages
            phase_callback: Callback for phase progress updates
        """
        try:
            # Initialize training
            self._stop_requested = False
            self._model = self.create_model_architecture()
            train_data = self.prepare_training_data()
            
            # Configure optimizer
            self._optimizer = torch.optim.Adam(
                self._model.parameters(),
                lr=config.get('learning_rate', 0.001)
            )
            
            if progress_callback:
                progress_callback("Training initialized")
            
            # Training loop
            epochs = config.get('epochs', 20)
            device = torch.device(config.get('device', self.device))
            
            for epoch in range(epochs):
                if self._stop_requested:
                    if progress_callback:
                        progress_callback("Training stopped by user")
                    break
                    
                if progress_callback:
                    progress_callback(f"Starting epoch {epoch + 1}/{epochs}")
                
                # Train one epoch
                self._model.train()
                total_loss = 0
                correct = 0
                total = 0
                
                for batch_idx, (inputs, targets) in enumerate(train_data):
                    if self._stop_requested:
                        break
                        
                    # Move data to device
                    inputs = inputs.to(device)
                    targets = targets.to(device)
                    
                    # Mixed precision training on GPU
                    if device.type == 'cuda':
                        with autocast(device_type='cuda', dtype=torch.float16):
                            # Forward pass
                            self._optimizer.zero_grad(set_to_none=True)
                            outputs = self._model(inputs)
                            loss = nn.functional.cross_entropy(outputs, targets)
                            
                            # Backward pass with gradient scaling
                            self._scaler.scale(loss).backward()
                            self._scaler.step(self._optimizer)
                            self._scaler.update()
                    else:
                        # CPU training
                        self._optimizer.zero_grad(set_to_none=True)
                        outputs = self._model(inputs)
                        loss = nn.functional.cross_entropy(outputs, targets)
                        loss.backward()
                        self._optimizer.step()
                    
                    # Calculate accuracy
                    _, predicted = outputs.max(1)
                    total += targets.size(0)
                    correct += predicted.eq(targets).sum().item()
                    
                    # Update running loss
                    total_loss += loss.item()
                    
                    # Log progress every 10 batches
                    if batch_idx % 10 == 0 and progress_callback:
                        accuracy = 100 * correct / total
                        progress_callback(
                            f"Epoch {epoch + 1}, Batch {batch_idx}: "
                            f"Loss = {total_loss/(batch_idx+1):.4f}, "
                            f"Accuracy = {accuracy:.2f}%"
                        )
                
                # Calculate epoch accuracy and update progress
                accuracy = 100 * correct / total
                if phase_callback:
                    progress = ((epoch + 1) / epochs) * 100
                    phase_callback(int(progress))
                
                if progress_callback:
                    progress_callback(
                        f"Epoch {epoch + 1} complete: "
                        f"Loss = {total_loss/len(train_data):.4f}, "
                        f"Accuracy = {accuracy:.2f}%"
                    )
                
                # Check if target accuracy reached
                if accuracy >= config.get('target_accuracy', 85) * 100:
                    if progress_callback:
                        progress_callback(f"Target accuracy {accuracy:.2f}% reached!")
                    break
            
            if progress_callback:
                progress_callback("Training completed")
            
        except Exception as e:
            logger.error(f"Training error: {e}")
            if progress_callback:
                progress_callback(f"Error during training: {str(e)}")
            raise
            
    def stop_training(self) -> None:
        """Request training to stop"""
        self._stop_requested = True
        logger.info("Training stop requested")

    def configure_optimizer(self, parameters, learning_rate: float = 0.001, weight_decay: float = 0.01, optimizer_type: str = 'adam'):
        """Configure optimizer for training

        Args:
            parameters: Model parameters
            learning_rate: Learning rate for optimizer
            weight_decay: Weight decay for regularization
            optimizer_type: Type of optimizer ('adam', 'adamw', 'sgd')

        Returns:
            Configured optimizer
        """
        try:
            if optimizer_type.lower() == 'adamw':
                optimizer = torch.optim.AdamW(
                    parameters,
                    lr=learning_rate,
                    weight_decay=weight_decay,
                    betas=(0.9, 0.999),
                    eps=1e-8
                )
            elif optimizer_type.lower() == 'sgd':
                optimizer = torch.optim.SGD(
                    parameters,
                    lr=learning_rate,
                    weight_decay=weight_decay,
                    momentum=0.9
                )
            else:  # Default to Adam
                optimizer = torch.optim.Adam(
                    parameters,
                    lr=learning_rate,
                    weight_decay=weight_decay,
                    betas=(0.9, 0.999),
                    eps=1e-8
                )

            logger.info(f"Configured {optimizer_type} optimizer with lr={learning_rate}, weight_decay={weight_decay}")
            return optimizer

        except Exception as e:
            logger.error(f"Error configuring optimizer: {e}")
            raise

    def get_training_data(self) -> Optional[List[Tuple["torch.Tensor", "torch.Tensor"]]]:
        """Get the training data loader"""
        return self._training_data
        

            
    def cleanup(self) -> None:
        """Clean up resources"""
        try:
            # Stop monitoring
            self._stop_memory_monitoring()
            
            # Stop training if in progress
            self.stop_training()
            
            # Clean up models
            with self._model_lock:
                # Move models to CPU first
                for model_key in list(self._active_models):
                    try:
                        if model_key in self._models:
                            model = self._models[model_key]
                            if model is not None:
                                model.cpu()
                    except Exception as e:
                        logger.error(f"Error moving model {model_key} to CPU: {e}")
                
                # Clear model references
                self._models.clear()
                self._tokenizers.clear()
                
                # Clear current model
                if self._model is not None:
                    try:
                        self._model.cpu()
                    except Exception:
                        pass
                    self._model = None
                
                # Clear optimizer and scaler
                self._optimizer = None
                self._scaler = None
            
            # Clear GPU cache if using CUDA
            if hasattr(self, 'device') and hasattr(self.device, 'type') and self.device.type == 'cuda':
                try:
                    import torch
                    torch.cuda.empty_cache()
                    torch.cuda.reset_peak_memory_stats()
                except Exception as e:
                    logger.error(f"Error clearing GPU cache: {e}")
            
            # Force garbage collection
            gc.collect()
            
            logger.info("Model manager cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during model manager cleanup: {e}")
            raise

    def list_available_models(self) -> List[str]:
        """List all available models

        Returns:
            List[str]: List of model names
        """
        try:
            # Get models from active models
            active_models = [key.split(':')[0] for key in self._active_models]

            # Get models from base directory and models subdirectory
            model_files = []
            base_path = Path(self.base_path)

            # Check base directory
            if base_path.exists():
                for ext in ['.pt', '.pth', '.bin', '.model']:
                    model_files.extend(base_path.glob(f'*{ext}'))

            # Check models subdirectory
            models_dir = base_path / 'models'
            if models_dir.exists():
                for ext in ['.pt', '.pth', '.bin', '.model']:
                    model_files.extend(models_dir.glob(f'**/*{ext}'))

            # Extract model names without extensions
            model_names = [model.stem for model in model_files]

            # Combine with active models and remove duplicates
            all_models = set(active_models + model_names)
            return sorted(list(all_models))

        except Exception as e:
            logger.error(f"Error listing available models: {e}")
            return []

    def get_storage_usage(self) -> int:
        """Get total storage usage in bytes"""
        total_size = 0
        try:
            for root, _, files in os.walk(self.base_path):
                for file in files:
                    file_path = Path(root) / file
                    total_size += file_path.stat().st_size
        except Exception as e:
            logger.error(f"Failed to calculate model storage usage: {e}")
        return total_size
