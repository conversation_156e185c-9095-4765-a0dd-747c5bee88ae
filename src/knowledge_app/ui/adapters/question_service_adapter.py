"""
Question Service Adapter

This adapter bridges the QuizController's expectation of a question service
with the Enterprise Main Window's MCQ manager functionality.
"""

import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class QuestionServiceAdapter:
    """
    Adapter that provides a question service interface for the QuizController
    while using the Enterprise Main Window's MCQ manager functionality.
    """
    
    def __init__(self, enterprise_main_window):
        """
        Initialize the adapter with the enterprise main window.
        
        Args:
            enterprise_main_window: The EnterpriseMainWindow instance
        """
        self.main_window = enterprise_main_window
        self.logger = logger
        
        # Ensure MCQ manager is initialized
        self._ensure_mcq_manager()
        
        logger.info("✅ Question service adapter initialized")
    
    def _ensure_mcq_manager(self):
        """Ensure the MCQ manager is properly initialized"""
        try:
            if hasattr(self.main_window, '_get_mcq_manager'):
                mcq_manager = self.main_window._get_mcq_manager()
                if mcq_manager:
                    logger.info("✅ MCQ manager verified in adapter")
                else:
                    logger.warning("⚠️ MCQ manager not available in adapter")
            else:
                logger.warning("⚠️ Main window doesn't have _get_mcq_manager method")
        except Exception as e:
            logger.error(f"❌ Error ensuring MCQ manager: {e}")
    
    def get_next_question(self) -> Optional[Dict[str, Any]]:
        """
        Get the next question using the main window's synchronous method.
        
        Returns:
            Dict containing question data or None if no question available
        """
        try:
            logger.info("🔄 Adapter getting next question...")
            
            # Use the main window's synchronous question generation
            if hasattr(self.main_window, 'get_next_question_sync'):
                question_data = self.main_window.get_next_question_sync()
                
                if question_data:
                    logger.info("✅ Adapter successfully got question from main window")
                    return question_data
                else:
                    logger.warning("⚠️ Main window returned no question data")
                    return None
            else:
                logger.error("❌ Main window doesn't have get_next_question_sync method")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error getting next question in adapter: {e}")
            return None
    
    def is_available(self) -> bool:
        """
        Check if the question service is available.
        
        Returns:
            True if the service can provide questions, False otherwise
        """
        try:
            # Check if main window has the required method
            if not hasattr(self.main_window, 'get_next_question_sync'):
                return False
            
            # Check if MCQ manager is available
            if hasattr(self.main_window, '_get_mcq_manager'):
                mcq_manager = self.main_window._get_mcq_manager()
                return mcq_manager is not None
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error checking adapter availability: {e}")
            return False
    
    def get_service_info(self) -> Dict[str, Any]:
        """
        Get information about the question service.
        
        Returns:
            Dict containing service information
        """
        try:
            info = {
                'adapter_type': 'QuestionServiceAdapter',
                'main_window_type': type(self.main_window).__name__,
                'available': self.is_available(),
                'methods': []
            }
            
            # Check available methods
            if hasattr(self.main_window, 'get_next_question_sync'):
                info['methods'].append('get_next_question_sync')
            if hasattr(self.main_window, '_get_mcq_manager'):
                info['methods'].append('_get_mcq_manager')
            
            # Check MCQ manager info
            if hasattr(self.main_window, '_get_mcq_manager'):
                mcq_manager = self.main_window._get_mcq_manager()
                if mcq_manager:
                    info['mcq_manager_available'] = True
                    if hasattr(mcq_manager, 'is_instant_available'):
                        info['instant_mode_available'] = mcq_manager.is_instant_available()
                else:
                    info['mcq_manager_available'] = False
            
            return info
            
        except Exception as e:
            logger.error(f"❌ Error getting service info: {e}")
            return {'error': str(e)}
