"""
Quiz setup screen for the Knowledge App
"""

from PyQt5.QtWidgets import (
    QWidget, QLabel, QPushButton, QVBoxLayout, QHBoxLayout,
    QComboBox, QLineEdit, QFrame, QMessageBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
import logging

logger = logging.getLogger(__name__)

class QuizSetupScreen(QWidget):
    """Quiz setup screen widget"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface"""
        # Create main layout
        layout = QVBoxLayout()
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(20)
        self.setLayout(layout)
        
        # Title
        title = QLabel("Quiz Setup")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: #8075FF;
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # Create setup form
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background-color: #2b2c31;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(15)
        
        # Topic input
        topic_label = QLabel("Choose Topic:")
        topic_label.setStyleSheet("color: white; font-size: 14px;")
        form_layout.addWidget(topic_label)
        
        self.topic_input = QLineEdit()
        self.topic_input.setPlaceholderText("Enter a subject (e.g., physics, history, programming)")
        self.topic_input.setStyleSheet("""
            QLineEdit {
                background-color: #1a1b1e;
                color: white;
                border: 1px solid #3f3f3f;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border: 1px solid #8075FF;
            }
        """)
        form_layout.addWidget(self.topic_input)
        
        # Mode selection
        mode_label = QLabel("Select Mode:")
        mode_label.setStyleSheet("color: white; font-size: 14px;")
        form_layout.addWidget(mode_label)
        
        self.mode_selector = QComboBox()
        self.mode_selector.addItems(["Serious", "Casual"])
        self.mode_selector.setStyleSheet("""
            QComboBox {
                background-color: #1a1b1e;
                color: white;
                border: 1px solid #3f3f3f;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
            }
            QComboBox:hover {
                border: 1px solid #8075FF;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid white;
                margin-right: 8px;
            }
        """)
        form_layout.addWidget(self.mode_selector)
        
        # Question type selection
        type_label = QLabel("Select Question Type:")
        type_label.setStyleSheet("color: white; font-size: 14px;")
        form_layout.addWidget(type_label)

        self.type_selector = QComboBox()
        self.type_selector.addItems(["Multiple Choice", "Numerical", "Conceptual"])
        self.type_selector.setStyleSheet(self.mode_selector.styleSheet())
        form_layout.addWidget(self.type_selector)

        # Cognitive level selection (Bloom's Taxonomy)
        cognitive_label = QLabel("Select Cognitive Level:")
        cognitive_label.setStyleSheet("color: white; font-size: 14px;")
        form_layout.addWidget(cognitive_label)

        self.cognitive_selector = QComboBox()
        cognitive_items = [
            "Understanding - Explain concepts and ideas",
            "Remembering - Recall facts and basic concepts",
            "Applying - Use information in new situations",
            "Analyzing - Draw connections among ideas",
            "Evaluating - Justify decisions and critique",
            "Creating - Produce new or original work"
        ]
        self.cognitive_selector.addItems(cognitive_items)
        self.cognitive_selector.setStyleSheet(self.mode_selector.styleSheet())
        form_layout.addWidget(self.cognitive_selector)

        # Advanced options toggle
        advanced_label = QLabel("Advanced Generation:")
        advanced_label.setStyleSheet("color: white; font-size: 14px;")
        form_layout.addWidget(advanced_label)

        self.advanced_selector = QComboBox()
        self.advanced_selector.addItems([
            "Enhanced - Use advanced AI techniques",
            "Standard - Use basic generation methods"
        ])
        self.advanced_selector.setStyleSheet(self.mode_selector.styleSheet())
        form_layout.addWidget(self.advanced_selector)
        
        layout.addWidget(form_frame)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        back_btn = QPushButton("Back")
        back_btn.clicked.connect(self._on_back)
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #2b2c31;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px 30px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3a3b3f;
            }
        """)
        buttons_layout.addWidget(back_btn)
        
        self.start_btn = QPushButton("Start Quiz")
        self.start_btn.clicked.connect(self._on_start)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #8075FF;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px 30px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #9085FF;
            }
        """)
        buttons_layout.addWidget(self.start_btn)
        
        layout.addLayout(buttons_layout)
        
        # Status label
        self.status_label = QLabel()
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                color: #8075FF;
                font-size: 14px;
                margin-top: 10px;
            }
        """)
        self.status_label.hide()
        layout.addWidget(self.status_label)
        
        # Set widget style
        self.setStyleSheet("""
            QWidget {
                background-color: #1a1b1e;
            }
        """)
        
    def _on_back(self):
        """Handle back button click"""
        if self.parent:
            self.parent.show_main_menu()
            
    def _on_start(self):
        """Handle start quiz button click with enhanced error handling and model checking"""
        if not self.parent:
            return

        topic = self.topic_input.text().strip() or "General Knowledge"
        mode = self.mode_selector.currentText()
        question_type = self.type_selector.currentText()

        # Extract cognitive level from selection
        cognitive_text = self.cognitive_selector.currentText()
        cognitive_level = cognitive_text.split(" - ")[0].lower()

        # Extract advanced option
        advanced_text = self.advanced_selector.currentText()
        use_advanced = advanced_text.startswith("Enhanced")

        # Disable start button to prevent multiple clicks
        self.start_btn.setEnabled(False)
        self.start_btn.setText("Loading...")

        self.status_label.setText("Checking AI models...")
        self.status_label.show()

        try:
            # Check model availability before starting quiz
            model_status = self._check_model_availability()

            if model_status["available"]:
                self.status_label.setText("Starting quiz...")
                # Pass the new parameters to the parent
                if hasattr(self.parent, 'start_new_quiz_from_setup_advanced'):
                    self.parent.start_new_quiz_from_setup_advanced(
                        topic, mode, question_type, cognitive_level, use_advanced
                    )
                else:
                    # Fallback to original method
                    self.parent.start_new_quiz_from_setup(topic, mode, question_type)
            else:
                # Show detailed error message with suggestions
                error_msg = model_status["error"]
                suggestion = model_status.get("suggestion", "")

                self.status_label.setText(f"Error: {error_msg}")
                self.status_label.show()

                # Show detailed error dialog
                self._show_model_error_dialog(error_msg, suggestion)

        except Exception as e:
            error_msg = f"Failed to start quiz: {str(e)}"
            self.status_label.setText(error_msg)
            self.status_label.show()
            logger.error(f"Quiz start error: {e}")
        finally:
            # Re-enable start button
            self.start_btn.setEnabled(True)
            self.start_btn.setText("Start Quiz")

    def _check_model_availability(self):
        """Check if AI models are available for quiz generation"""
        try:
            # Get MCQ manager from parent
            if not hasattr(self.parent, '_get_mcq_manager'):
                return {
                    "available": False,
                    "error": "MCQ manager not available",
                    "suggestion": "Please restart the application to initialize the AI system."
                }

            mcq_manager = self.parent._get_mcq_manager()

            # Check if instant mode is available (always works)
            if mcq_manager.is_instant_available():
                logger.info("✅ Instant mode available for quiz generation")
                return {"available": True}

            # Check if offline mode is available
            if mcq_manager.is_offline_available():
                logger.info("✅ Offline mode available for quiz generation")
                return {"available": True}

            # Check if online mode is available
            if hasattr(mcq_manager, 'online_generator') and mcq_manager.online_generator:
                logger.info("✅ Online mode available for quiz generation")
                return {"available": True}

            # No modes available
            return {
                "available": False,
                "error": "No AI models are currently available",
                "suggestion": "The application will use instant mode with pre-generated questions. For AI-generated questions, please ensure models are properly installed."
            }

        except Exception as e:
            logger.error(f"Error checking model availability: {e}")
            return {
                "available": False,
                "error": f"Failed to check model availability: {str(e)}",
                "suggestion": "Please restart the application or check the logs for more details."
            }

    def _show_model_error_dialog(self, error_msg, suggestion):
        """Show detailed error dialog with suggestions"""
        dialog = QMessageBox(self)
        dialog.setWindowTitle("AI Model Error")
        dialog.setIcon(QMessageBox.Warning)

        # Create detailed message
        message = f"<b>Error:</b> {error_msg}<br><br>"
        if suggestion:
            message += f"<b>Suggestion:</b> {suggestion}<br><br>"

        message += "<b>Options:</b><br>"
        message += "• Click 'Continue' to use instant mode with pre-generated questions<br>"
        message += "• Click 'Cancel' to go back and try again later"

        dialog.setText(message)
        dialog.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
        dialog.button(QMessageBox.Ok).setText("Continue with Instant Mode")
        dialog.button(QMessageBox.Cancel).setText("Cancel")

        result = dialog.exec_()

        if result == QMessageBox.Ok:
            # Force instant mode and try again
            self._force_instant_mode_and_start()

    def _force_instant_mode_and_start(self):
        """Force instant mode and start quiz"""
        try:
            # Get parameters
            topic = self.topic_input.text().strip() or "General Knowledge"
            mode = self.mode_selector.currentText()
            question_type = self.type_selector.currentText()

            # Force instant mode
            if hasattr(self.parent, '_get_mcq_manager'):
                mcq_manager = self.parent._get_mcq_manager()
                mcq_manager.set_instant_mode(True)
                mcq_manager.set_offline_mode(False)
                logger.info("🚀 Forced instant mode for quiz generation")

            self.status_label.setText("Starting quiz in instant mode...")
            self.parent.start_new_quiz_from_setup(topic, mode, question_type)

        except Exception as e:
            error_msg = f"Failed to start quiz even in instant mode: {str(e)}"
            self.status_label.setText(error_msg)
            logger.error(error_msg)
