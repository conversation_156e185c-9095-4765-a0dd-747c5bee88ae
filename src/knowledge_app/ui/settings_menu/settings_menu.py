"""
Settings menu screen for the Knowledge App
"""

import os
import json
import logging
from PyQt5.QtWidgets import (
    QWidget, QLabel, QPushButton, QVBoxLayout, QHBoxLayout,
    QFrame, QScrollArea, QSlider, QComboBox, QLineEdit,
    QCheckBox, QSpinBox, QFileDialog, QMessageBox, QProgressDialog, QGroupBox, QProgressBar, QDoubleSpinBox, QLayout
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QObject, QTimer
from PyQt5.QtGui import QFont, QIcon
import torch
import psutil
from pathlib import Path

# Set up logger
logger = logging.getLogger(__name__)

# Enterprise styling imports
from ..enterprise_style_manager import get_style_manager
from ..enterprise_design_system import get_design_system
from ..style_migration_helper import get_migration_helper

# Use enterprise design system instead of legacy AppStyles
from ..enterprise_design_system import EnterpriseDesignSystem, get_design_system
from ..enterprise_style_manager import EnterpriseStyleManager, get_style_manager

try:
    from ...core.document_processor import DocumentProcessor
except ImportError:
    DocumentProcessor = None

try:
    from knowledge_app.core.ml_availability import ml_features_available
except ImportError:
    def ml_features_available():
        return True


class SettingsController(QObject):
    """
    Controller for settings logic following MVC pattern.
    Handles loading/saving settings via ConfigManager and business logic.
    """

    # Signals for communicating with the view
    setting_changed = pyqtSignal(str, object)  # setting_key, new_value
    settings_loaded = pyqtSignal(dict)  # all_settings
    settings_saved = pyqtSignal(bool)  # success

    def __init__(self, config_manager, parent=None):
        super().__init__(parent)
        self.config = config_manager

    def load_settings(self):
        """Load all settings from config"""
        try:
            settings = {
                'theme': self.config.get_value('ui.theme', 'dark'),
                'font_size': self.config.get_value('ui.font_size', 12),
                'language': self.config.get_value('ui.language', 'English'),
                'storage_limit': self.config.get_value('storage_config.image_cache_limit', 500) // (1024 * 1024),
                'auto_save': self.config.get_value('app.auto_save', True),
                'debug_mode': self.config.get_value('app.debug_mode', False),
                'gpu_enabled': self.config.get_value('model.enable_gpu', True),
                'model_name': self.config.get_value('model.name', 'mistral-7b-instruct'),
                'max_memory': self.config.get_value('app.max_memory_usage_gb', 8),
            }
            self.settings_loaded.emit(settings)
            return settings
        except Exception as e:
            logger.error(f"Error loading settings: {e}")
            return {}

    def save_setting(self, key: str, value):
        """Save a single setting"""
        try:
            # Map UI keys to config keys
            key_mapping = {
                'theme': 'ui.theme',
                'font_size': 'ui.font_size',
                'language': 'ui.language',
                'storage_limit': 'storage_config.image_cache_limit',
                'auto_save': 'app.auto_save',
                'debug_mode': 'app.debug_mode',
                'gpu_enabled': 'model.enable_gpu',
                'model_name': 'model.name',
                'max_memory': 'app.max_memory_usage_gb',
            }

            config_key = key_mapping.get(key, key)

            # Convert storage limit back to bytes
            if key == 'storage_limit':
                value = value * 1024 * 1024

            self.config.set_value(config_key, value)
            if hasattr(self.config, 'save'):
                self.config.save()
            elif hasattr(self.config, '_save_user_settings'):
                self.config._save_user_settings()

            self.setting_changed.emit(key, value)
            return True

        except Exception as e:
            logger.error(f"Error saving setting {key}: {e}")
            return False


class SettingsSection(QFrame):
    """A section in the settings menu with enterprise styling"""

    def __init__(self, title, parent=None):
        super().__init__(parent)

        # Initialize enterprise styling system
        self.style_manager = get_style_manager()
        self.design_system = get_design_system()

        self.title_label = None
        self.content = None
        self.content_layout = None
        self.init_ui(title)
        
    def init_ui(self, title):
        """Initialize the section UI"""
        try:
            # Main section layout
            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)

            # Section container with enterprise styling
            section_container = QFrame()
            section_container.setStyleSheet(self.style_manager.get_style('card'))

            # Section layout
            section_layout = QVBoxLayout(section_container)
            section_layout.setContentsMargins(
                self.design_system.spacing('lg'),
                self.design_system.spacing('md'),
                self.design_system.spacing('lg'),
                self.design_system.spacing('lg')
            )
            section_layout.setSpacing(self.design_system.spacing('md'))

            # Section title with enterprise styling
            self.title_label = QLabel(title)
            title_style = f"""
                QLabel {{
                    color: {self.design_system.color('primary')};
                    font-size: {self.design_system.font_size('xl')}px;
                    font-weight: {self.design_system._typography['font_weight_semibold']};
                    margin-bottom: {self.design_system.spacing('sm')}px;
                    background: transparent;
                    border: none;
                }}
            """
            self.title_label.setStyleSheet(title_style)
            section_layout.addWidget(self.title_label)

            # Content area
            self.content = QWidget()
            self.content.setStyleSheet("background: transparent; border: none;")
            self.content_layout = QVBoxLayout(self.content)
            self.content_layout.setContentsMargins(0, 0, 0, 0)
            self.content_layout.setSpacing(12)

            section_layout.addWidget(self.content)
            main_layout.addWidget(section_container)

        except Exception as e:
            logger.error(f"Error initializing SettingsSection: {e}")
            # Create minimal fallback
            layout = QVBoxLayout(self)
            error_label = QLabel(f"Section Error: {title}")
            error_label.setStyleSheet("color: #FF6B6B; padding: 10px;")
            layout.addWidget(error_label)

            # Initialize fallback content frame
            self.content = QWidget()
            self.content_layout = QVBoxLayout(self.content)
            layout.addWidget(self.content)
        
    def add_setting(self, label_text, widget):
        """Add a setting to the section"""
        try:
            # Ensure content layout exists
            if not hasattr(self, 'content_layout') or self.content_layout is None:
                if hasattr(self, 'content') and self.content:
                    # Check if content already has a layout
                    if self.content.layout():
                        self.content_layout = self.content.layout()
                    else:
                        self.content_layout = QVBoxLayout(self.content)
                        self.content_layout.setContentsMargins(0, 0, 0, 0)
                        self.content_layout.setSpacing(12)
                else:
                    logger.error(f"Cannot add setting '{label_text}': no content widget")
                    return

            # Create setting row container
            setting_row = QWidget()
            setting_row.setStyleSheet("background: transparent; border: none;")
            row_layout = QHBoxLayout(setting_row)
            row_layout.setContentsMargins(0, 8, 0, 8)
            row_layout.setSpacing(20)

            # Add label if provided with enterprise styling
            if label_text:
                label = QLabel(label_text)
                label_style = f"""
                    QLabel {{
                        color: {self.design_system.color('text_primary')};
                        font-size: {self.design_system.font_size('base')}px;
                        font-weight: {self.design_system._typography['font_weight_medium']};
                        background: transparent;
                        border: none;
                    }}
                """
                label.setStyleSheet(label_style)
                label.setMinimumWidth(180)
                label.setMaximumWidth(200)
                label.setWordWrap(True)
                row_layout.addWidget(label)

            # Handle layout widgets
            if isinstance(widget, QLayout):
                container = QWidget()
                container.setLayout(widget)
                container.setStyleSheet("background: transparent; border: none;")
                widget = container

            # Style widgets based on type with enterprise styling
            if isinstance(widget, (QSpinBox, QComboBox)):
                widget.setFixedHeight(self.design_system.dimension('input_height'))
                widget.setMinimumWidth(120)
                widget.setStyleSheet(self.style_manager.get_style('input_field'))
                widget.setFocusPolicy(Qt.ClickFocus)

            elif isinstance(widget, QPushButton):
                widget.setFixedHeight(self.design_system.dimension('button_height_md'))
                widget.setMinimumWidth(120)
                widget.setMaximumWidth(200)
                widget.setStyleSheet(self.style_manager.get_style('button_primary'))

            elif isinstance(widget, QCheckBox):
                checkbox_style = f"""
                    QCheckBox {{
                        color: {self.design_system.color('text_primary')};
                        font-size: {self.design_system.font_size('base')}px;
                        spacing: {self.design_system.spacing('sm')}px;
                    }}
                    QCheckBox::indicator {{
                        width: 18px;
                        height: 18px;
                    }}
                    QCheckBox::indicator:unchecked {{
                        background-color: {self.design_system.color('surface')};
                        border: 2px solid {self.design_system.color('border')};
                        border-radius: {self.design_system.radius('sm')}px;
                    }}
                    QCheckBox::indicator:checked {{
                        background-color: {self.design_system.color('primary')};
                        border: 2px solid {self.design_system.color('primary')};
                        border-radius: {self.design_system.radius('sm')}px;
                    }}
                """
                widget.setStyleSheet(checkbox_style)

            elif isinstance(widget, QLineEdit):
                widget.setFixedHeight(self.design_system.dimension('input_height'))
                widget.setMinimumWidth(200)
                widget.setStyleSheet(self.style_manager.get_style('input_field'))

            # Add widget to row
            row_layout.addWidget(widget)

            # Only add stretch if it's not a button (buttons should stay compact)
            if not isinstance(widget, QPushButton):
                row_layout.addStretch()

            # Add row to content
            self.content_layout.addWidget(setting_row)

        except Exception as e:
            logger.error(f"Error adding setting '{label_text}': {e}")
            # Add error indicator
            try:
                if hasattr(self, 'content_layout') and self.content_layout:
                    error_label = QLabel(f"Error: {label_text}")
                    error_label.setStyleSheet("color: #FF6B6B; font-size: 12px; padding: 5px;")
                    self.content_layout.addWidget(error_label)
            except Exception as e2:
                logger.error(f"Failed to add error indicator: {e2}")

class WorkerThread(QThread):
    """Base worker thread class"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.worker = None
    
    def run(self):
        """Run the worker's task"""
        if self.worker:
            self.worker.run()

class CloudConnectionTester(QObject):
    """Worker class for testing cloud connections"""
    finished = pyqtSignal()
    result = pyqtSignal(bool, str)
    
    def __init__(self, provider, api_key, endpoint_url=None, region=None, model=None):
        super().__init__()
        self.provider = provider
        self.api_key = api_key
        self.endpoint_url = endpoint_url
        self.region = region
        self.model = model
    
    def run(self):
        """Test the cloud connection"""
        try:
            if self.provider == "OpenAI":
                import openai
                openai.api_key = self.api_key
                openai.Model.list()
                self.result.emit(True, "Successfully connected to OpenAI API.")
                
            elif self.provider == "Azure OpenAI":
                import openai
                openai.api_type = "azure"
                openai.api_key = self.api_key
                openai.api_base = "https://your-azure-endpoint.openai.azure.com"
                openai.api_version = "2023-05-15"
                openai.Model.list()
                self.result.emit(True, "Successfully connected to Azure OpenAI API.")
                
            elif self.provider == "AWS SageMaker":
                import boto3
                session = boto3.Session(
                    aws_access_key_id=self.api_key.split(':')[0],
                    aws_secret_access_key=self.api_key.split(':')[1],
                    region_name=self.region
                )
                sagemaker = session.client('sagemaker')
                sagemaker.list_endpoints()
                self.result.emit(True, "Successfully connected to AWS SageMaker.")
                
            elif self.provider == "Google Vertex AI":
                from google.cloud import aiplatform
                aiplatform.init(
                    project=self.api_key.split(':')[0],
                    location=self.region,
                    credentials=self.api_key.split(':')[1]
                )
                self.result.emit(True, "Successfully connected to Google Vertex AI.")
                
            elif self.provider == "IBM Watson ML":
                import requests
                response = requests.get(
                    f"{self.endpoint_url}/ml/v4/deployments",
                    headers={"Authorization": f"Bearer {self.api_key}"},
                    timeout=10
                )
                response.raise_for_status()
                self.result.emit(True, "Successfully connected to IBM Watson ML.")
                
            elif self.provider == "Azure ML":
                from azureml.core import Workspace
                ws = Workspace.from_config()
                ws.get_details()
                self.result.emit(True, "Successfully connected to Azure ML.")
                
            elif self.provider == "Hugging Face":
                from huggingface_hub import HfApi
                api = HfApi(token=self.api_key)
                api.list_models(limit=1)
                self.result.emit(True, "Successfully connected to Hugging Face API.")
                
            elif self.provider == "Custom Endpoint":
                import requests
                response = requests.get(
                    self.endpoint_url,
                    headers={"Authorization": f"Bearer {self.api_key}"},
                    timeout=10
                )
                response.raise_for_status()
                self.result.emit(True, "Successfully connected to custom endpoint.")
                
        except Exception as e:
            self.result.emit(False, f"Connection test failed: {str(e)}")
        finally:
            self.finished.emit()

class TestRunner(QObject):
    """Worker class for running tests in a separate thread"""
    progress = pyqtSignal(int)
    status = pyqtSignal(str)
    finished = pyqtSignal()
    
    def __init__(self, category):
        super().__init__()
        self.category = category
    
    def run(self):
        """Run the tests"""
        try:
            self.status.emit("Initializing tests...")
            self.progress.emit(5)

            # Import test runner
            from run_tests import run_test_category, run_all_tests

            def progress_callback(value, message):
                self.progress.emit(value)
                self.status.emit(message)

            # Run tests and get result
            if self.category == "all":
                success = run_all_tests(progress_callback)
            else:
                success = run_test_category(self.category, progress_callback)

            # The final status message is already set by the progress_callback
            # from the test runner, so we don't need to override it here

        except Exception as e:
            self.progress.emit(100)
            self.status.emit(f"💥 ERROR: Failed to run tests - {str(e)}")
        finally:
            self.finished.emit()

class SettingsMenu(QWidget):
    """Settings menu screen widget with enterprise styling - MVC View Component"""

    # Signal for hardware settings changes
    settings_changed = pyqtSignal(dict)

    def __init__(self, parent=None):
        """Initialize settings menu"""
        super().__init__(parent)
        self.parent = parent

        # Initialize enterprise styling system
        self.style_manager = get_style_manager()
        self.design_system = get_design_system()
        self.migration_helper = get_migration_helper()

        # Apply enterprise styling to main widget
        self.apply_enterprise_styling()

        # Get config from parent or create a new one
        try:
            if hasattr(parent, 'config') and parent.config is not None:
                self.config = parent.config
            else:
                try:
                    from ...core.config_manager import get_config
                    self.config = get_config()
                    if self.config is None:
                        from ...core.config_manager import ConfigManager
                        self.config = ConfigManager()
                except ImportError:
                    # Create a minimal config fallback
                    self.config = self._create_fallback_config()
        except Exception as e:
            logger.error(f"Error setting up config: {e}")
            self.config = self._create_fallback_config()

        # Initialize MVC controller
        self.controller = SettingsController(self.config, self)
        self._connect_controller_signals()

        # Initialize UI with error handling
        try:
            self.init_ui()
            # Load settings after UI is created
            self.load_settings()
        except Exception as e:
            logger.error(f"Error initializing settings UI: {e}")
            self._create_error_ui(str(e))

    def _connect_controller_signals(self):
        """Connect controller signals to view methods"""
        self.controller.setting_changed.connect(self._on_setting_changed)
        self.controller.settings_loaded.connect(self._on_settings_loaded)
        self.controller.settings_saved.connect(self._on_settings_saved)

    def _on_setting_changed(self, key: str, value):
        """Handle setting change from controller"""
        logger.info(f"Setting changed: {key} = {value}")
        # Emit signal for parent components
        self.settings_changed.emit({key: value})

    def _on_settings_loaded(self, settings: dict):
        """Handle settings loaded from controller"""
        logger.info("Settings loaded from controller")
        # Update UI components with loaded settings
        self._update_ui_from_settings(settings)

    def _on_settings_saved(self, success: bool):
        """Handle settings save result from controller"""
        if success:
            logger.info("Settings saved successfully")
        else:
            logger.error("Failed to save settings")

    def apply_enterprise_styling(self):
        """Apply enterprise styling to the settings menu"""
        try:
            # Apply main widget styling
            main_widget_style = f"""
                QWidget {{
                    background: {self.design_system.color('bg_primary')};
                    color: {self.design_system.color('text_primary')};
                    font-family: {self.design_system._typography['font_family_primary']};
                }}
            """
            self.setStyleSheet(main_widget_style)

            logger.info("✅ Enterprise styling applied to settings menu")

        except Exception as e:
            logger.warning(f"Enterprise styling failed for settings menu, using fallback: {e}")
            # Fallback to basic styling using design system if possible
            try:
                fallback_style = f"""
                    QWidget {{
                        background-color: {self.design_system.color('bg_primary')};
                        color: {self.design_system.color('text_primary')};
                    }}
                """
                self.setStyleSheet(fallback_style)
            except:
                # Ultimate fallback with hardcoded values
                self.setStyleSheet("""
                    QWidget {
                        background-color: #1E1E2E;
                        color: #FFFFFF;
                    }
                """)

    def _create_fallback_config(self):
        """Create a minimal fallback config object"""
        class FallbackConfig:
            def get_value(self, key, default=None):
                return default
            def set_value(self, key, value):
                pass
            def _save_user_settings(self):
                pass
        return FallbackConfig()

    def _create_error_ui(self, error_message):
        """Create a simple error UI when main UI fails"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)

        # Error title
        title = QLabel("Settings Menu Error")
        title.setStyleSheet("""
            QLabel {
                color: #FF6B6B;
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)

        # Error message
        error_label = QLabel(f"Failed to load settings menu:\n{error_message}")
        error_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 14px;
                padding: 20px;
                background-color: #2D2D3F;
                border-radius: 8px;
                border: 1px solid #FF6B6B;
            }
        """)
        error_label.setWordWrap(True)
        layout.addWidget(error_label)

        # Back button
        back_btn = QPushButton("Back to Main Menu")
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #6C63FF;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 24px;
                margin-top: 20px;
            }
            QPushButton:hover {
                background-color: #8075FF;
            }
        """)
        back_btn.clicked.connect(self._on_back)
        layout.addWidget(back_btn)

        layout.addStretch()
        
    def init_ui(self):
        """Initialize the UI"""
        try:
            # Clear any existing layout
            if self.layout():
                QWidget().setLayout(self.layout())

            # Create main layout
            main_layout = QVBoxLayout(self)
            main_layout.setSpacing(0)
            main_layout.setContentsMargins(0, 0, 0, 0)

            # Create header with title and back button
            header_widget = QWidget()
            header_widget.setFixedHeight(80)
            header_widget.setStyleSheet("""
                QWidget {
                    background-color: #2D2D3F;
                    border-bottom: 2px solid #6C63FF;
                }
            """)
            header_layout = QHBoxLayout(header_widget)
            header_layout.setContentsMargins(20, 20, 20, 20)

            # Title
            title = QLabel("Settings")
            title.setStyleSheet("""
                QLabel {
                    color: #6C63FF;
                    font-size: 24px;
                    font-weight: bold;
                    background: transparent;
                    border: none;
                }
            """)
            header_layout.addWidget(title)
            header_layout.addStretch()

            # Back button in header
            back_btn = QPushButton("← Back")
            back_btn.setFixedSize(100, 40)
            back_btn.setStyleSheet("""
                QPushButton {
                    background-color: #6C63FF;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #8075FF;
                }
            """)
            back_btn.clicked.connect(self._on_back)
            header_layout.addWidget(back_btn)

            main_layout.addWidget(header_widget)

            # Create scroll area for settings
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setStyleSheet("""
                QScrollArea {
                    border: none;
                    background-color: #1E1E2E;
                }
                QScrollBar:vertical {
                    background: #2D2D3F;
                    width: 12px;
                    border-radius: 6px;
                }
                QScrollBar::handle:vertical {
                    background: #6C63FF;
                    border-radius: 6px;
                    min-height: 20px;
                }
            """)

            # Create content widget for scroll area
            content_widget = QWidget()
            content_widget.setStyleSheet("background-color: #1E1E2E;")
            content_layout = QVBoxLayout(content_widget)
            content_layout.setSpacing(20)
            content_layout.setContentsMargins(20, 20, 20, 20)

            # Create organized sections with categories
            self.create_settings_categories(content_layout)



            # Add spacer to content
            content_layout.addStretch()

            # Set content widget to scroll area
            scroll_area.setWidget(content_widget)
            main_layout.addWidget(scroll_area)

        except Exception as e:
            logger.error(f"Critical error initializing settings UI: {e}")
            # Fallback to error UI
            self._create_error_ui(str(e))

    def create_settings_categories(self, parent_layout):
        """Create organized settings categories"""
        try:
            # Application Settings Category
            app_category = self.create_category_section("Application Settings", [
                ("Theme", self.create_theme_setting),
                ("Font Size", self.create_font_size_setting),
                ("Language", self.create_language_setting),
            ])
            parent_layout.addWidget(app_category)

            # Content Management Category
            content_category = self.create_category_section("Content Management", [
                ("Upload Background Images", self.create_upload_images_setting),
                ("Manage Books", self.create_books_management_setting),
                ("Storage Limit", self.create_storage_limit_setting),
            ])
            parent_layout.addWidget(content_category)

            # AI & Performance Category
            ai_category = self.create_category_section("AI & Performance", [
                ("MCQ Generation Mode", self.create_mcq_mode_setting),
                ("Cloud Provider", self.create_cloud_provider_setting),
                ("API Configuration", self.create_api_config_setting),
                ("GPU Settings", self.create_gpu_settings),
                ("Memory Limit", self.create_memory_limit_setting),
            ])
            parent_layout.addWidget(ai_category)

            # System & Maintenance Category
            system_category = self.create_category_section("System & Maintenance", [
                ("Run Diagnostics", self.create_diagnostics_setting),
                ("Run Tests", self.create_test_runner_setting),
                ("Export Settings", self.create_export_setting),
                ("Import Settings", self.create_import_setting),
                ("Reset Application", self.create_reset_setting),
            ])
            parent_layout.addWidget(system_category)

        except Exception as e:
            logger.error(f"Error creating settings categories: {e}")

    def create_category_section(self, title, settings_list):
        """Create a professional category section"""
        # Create category container
        category_frame = QFrame()
        category_frame.setStyleSheet("""
            QFrame {
                background-color: #2D2D3F;
                border-radius: 12px;
                border: 1px solid #3D3D4F;
                margin-bottom: 20px;
            }
        """)

        category_layout = QVBoxLayout(category_frame)
        category_layout.setContentsMargins(24, 20, 24, 24)
        category_layout.setSpacing(16)

        # Category title
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: #6C63FF;
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 8px;
                background: transparent;
                border: none;
            }
        """)
        category_layout.addWidget(title_label)

        # Add settings grid
        settings_grid = QWidget()
        grid_layout = QVBoxLayout(settings_grid)
        grid_layout.setSpacing(12)
        grid_layout.setContentsMargins(0, 0, 0, 0)

        for setting_name, setting_creator in settings_list:
            try:
                setting_widget = setting_creator()
                if setting_widget:
                    # Create setting row
                    row_widget = QWidget()
                    row_layout = QHBoxLayout(row_widget)
                    row_layout.setContentsMargins(0, 8, 0, 8)
                    row_layout.setSpacing(20)

                    # Setting label
                    label = QLabel(setting_name + ":")
                    label.setStyleSheet("""
                        QLabel {
                            color: #D1D1E1;
                            font-size: 14px;
                            font-weight: 500;
                            min-width: 160px;
                            max-width: 160px;
                        }
                    """)
                    row_layout.addWidget(label)

                    # Setting widget
                    row_layout.addWidget(setting_widget)
                    row_layout.addStretch()

                    grid_layout.addWidget(row_widget)

            except Exception as e:
                logger.error(f"Error creating setting '{setting_name}': {e}")

        category_layout.addWidget(settings_grid)
        return category_frame

    # Individual Setting Creators
    def create_theme_setting(self):
        """Create theme selection widget"""
        self.theme_combo = QComboBox(self)
        self.theme_combo.addItems(["Dark", "Light"])
        self.theme_combo.setCurrentText("Dark")
        self.theme_combo.setStyleSheet(self.get_input_style())
        self.theme_combo.currentTextChanged.connect(self._on_theme_changed)
        return self.theme_combo

    def create_font_size_setting(self):
        """Create font size widget"""
        self.font_size_spin = QSpinBox(self)
        self.font_size_spin.setRange(10, 24)
        self.font_size_spin.setValue(14)
        self.font_size_spin.setSuffix(" pt")
        self.font_size_spin.setStyleSheet(self.get_input_style())
        self.font_size_spin.valueChanged.connect(self._on_font_size_changed)
        return self.font_size_spin

    def create_language_setting(self):
        """Create language selection widget"""
        combo = QComboBox(self)
        combo.addItems(["English", "Spanish", "French", "German"])
        combo.setCurrentText("English")
        combo.setStyleSheet(self.get_input_style())
        return combo

    def create_upload_images_setting(self):
        """Create upload images button"""
        btn = QPushButton("Upload Images")
        btn.setStyleSheet(self.get_button_style())
        btn.clicked.connect(self._upload_background_images)
        return btn

    def create_books_management_setting(self):
        """Create books management widget"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        view_btn = QPushButton("View Books")
        view_btn.setStyleSheet(self.get_button_style())
        view_btn.clicked.connect(self._show_books_dialog)
        layout.addWidget(view_btn)

        upload_btn = QPushButton("Upload Books")
        upload_btn.setStyleSheet(self.get_button_style())
        upload_btn.clicked.connect(self._handle_upload_books)
        layout.addWidget(upload_btn)

        return container

    def create_storage_limit_setting(self):
        """Create storage limit widget"""
        spin = QSpinBox(self)
        spin.setRange(100, 10000)
        spin.setValue(500)
        spin.setSuffix(" MB")
        spin.setStyleSheet(self.get_input_style())
        spin.valueChanged.connect(self._on_storage_limit_changed)
        return spin

    def create_mcq_mode_setting(self):
        """Create MCQ generation mode widget"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)

        # Mode selection combo
        self.mcq_mode_combo = QComboBox()
        self.mcq_mode_combo.addItems(["Online Mode (Groq API)", "Offline Mode (Local Models)"])

        # Load current setting from config
        try:
            if hasattr(self, 'config') and self.config:
                offline_mode = self.config.get_value('mcq_settings.offline_mode', False)
                if offline_mode:
                    self.mcq_mode_combo.setCurrentText("Offline Mode (Local Models)")
                else:
                    self.mcq_mode_combo.setCurrentText("Online Mode (Groq API)")
            else:
                self.mcq_mode_combo.setCurrentText("Online Mode (Groq API)")  # Default to online
        except Exception:
            self.mcq_mode_combo.setCurrentText("Online Mode (Groq API)")  # Default to online

        self.mcq_mode_combo.setStyleSheet(self.get_input_style())
        self.mcq_mode_combo.currentTextChanged.connect(self._on_mcq_mode_changed)
        layout.addWidget(self.mcq_mode_combo)

        # Status indicator
        self.mcq_status_label = QLabel("🌐 Using external API")
        self.mcq_status_label.setStyleSheet("""
            QLabel {
                color: #4CAF50;
                font-size: 12px;
                font-weight: 500;
                padding: 4px 8px;
                background-color: #2D2D3F;
                border-radius: 4px;
                border: 1px solid #4CAF50;
            }
        """)
        layout.addWidget(self.mcq_status_label)

        return container

    def create_cloud_provider_setting(self):
        """Create cloud provider widget"""
        combo = QComboBox(self)
        combo.addItems(["OpenAI", "Azure OpenAI", "AWS SageMaker", "Google Cloud"])
        combo.setCurrentText("OpenAI")
        combo.setStyleSheet(self.get_input_style())
        return combo

    def create_api_config_setting(self):
        """Create API configuration widget"""
        btn = QPushButton("Configure API")
        btn.setStyleSheet(self.get_button_style())
        btn.clicked.connect(self._configure_api)
        return btn

    def create_gpu_settings(self):
        """Create GPU settings widget"""
        checkbox = QCheckBox("Enable GPU Acceleration")
        checkbox.setStyleSheet(self.get_checkbox_style())
        checkbox.stateChanged.connect(self._on_gpu_toggle)
        return checkbox

    def create_memory_limit_setting(self):
        """Create memory limit widget"""
        spin = QSpinBox(self)
        spin.setRange(1, 32)
        spin.setValue(8)
        spin.setSuffix(" GB")
        spin.setStyleSheet(self.get_input_style())
        return spin

    def create_diagnostics_setting(self):
        """Create diagnostics widget"""
        btn = QPushButton("Run System Check")
        btn.setStyleSheet(self.get_button_style())
        btn.clicked.connect(self._run_diagnostics)
        return btn

    def create_test_runner_setting(self):
        """Create test runner widget"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        # Test category selection
        self.test_category_combo = QComboBox()
        self.test_category_combo.addItems([
            "All Tests",
            "Core Tests",
            "Hardware Tests",
            "Model Tests",
            "Training Tests",
            "Integration Tests"
        ])
        self.test_category_combo.setStyleSheet(self.get_input_style())
        layout.addWidget(self.test_category_combo)

        # Run tests button
        run_tests_btn = QPushButton("Run Tests")
        run_tests_btn.setStyleSheet(self.get_button_style())
        run_tests_btn.clicked.connect(self._run_selected_tests)
        layout.addWidget(run_tests_btn)

        return container

    def create_export_setting(self):
        """Create export settings widget"""
        btn = QPushButton("Export Settings")
        btn.setStyleSheet(self.get_button_style())
        btn.clicked.connect(self._export_settings)
        return btn

    def create_import_setting(self):
        """Create import settings widget"""
        btn = QPushButton("Import Settings")
        btn.setStyleSheet(self.get_button_style())
        btn.clicked.connect(self._import_settings)
        return btn

    def create_reset_setting(self):
        """Create reset application widget"""
        btn = QPushButton("Reset Application")
        btn.setStyleSheet("""
            QPushButton {
                background-color: #FF6B6B;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 13px;
                font-weight: 600;
                min-width: 120px;
                max-width: 200px;
            }
            QPushButton:hover {
                background-color: #FF5252;
            }
        """)
        btn.clicked.connect(self._on_factory_reset)
        return btn

    # Enterprise Styling Helper Methods
    def get_input_style(self):
        """Get consistent input styling using enterprise system"""
        try:
            return self.style_manager.get_style('input_field')
        except:
            # Fallback to legacy styling
            return """
                QSpinBox, QComboBox {
                    background-color: #3D3D4F;
                    color: #FFFFFF;
                    border: 1px solid #4D4D5F;
                    border-radius: 8px;
                    padding: 8px 12px;
                    font-size: 13px;
                    min-width: 120px;
                    max-width: 200px;
                    min-height: 16px;
                }
                QSpinBox:focus, QComboBox:focus {
                    border-color: #6C63FF;
                    background-color: #4D4D5F;
                }
            """

    def get_button_style(self):
        """Get consistent button styling using enterprise system"""
        try:
            return self.style_manager.get_style('button_primary')
        except:
            # Fallback to design system colors if possible
            try:
                return f"""
                    QPushButton {{
                        background-color: {self.design_system.color('primary')};
                        color: {self.design_system.color('text_on_primary')};
                        border: none;
                        border-radius: {self.design_system.radius('md')}px;
                        padding: {self.design_system.spacing('sm')}px {self.design_system.spacing('md')}px;
                        font-size: {self.design_system.font_size('sm')}px;
                        font-weight: {self.design_system._typography['font_weight_semibold']};
                        min-width: 120px;
                        max-width: 200px;
                        min-height: 16px;
                    }}
                    QPushButton:hover {{
                        background-color: {self.design_system.color('primary_hover')};
                    }}
                """
            except:
                # Ultimate fallback with hardcoded values
                return """
                    QPushButton {
                        background-color: #6C63FF;
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 10px 20px;
                        font-size: 13px;
                        font-weight: 600;
                        min-width: 120px;
                        max-width: 200px;
                        min-height: 16px;
                    }
                    QPushButton:hover {
                        background-color: #8075FF;
                    }
                """

    def get_checkbox_style(self):
        """Get consistent checkbox styling using enterprise system"""
        try:
            return f"""
                QCheckBox {{
                    color: {self.design_system.color('text_primary')};
                    font-size: {self.design_system.font_size('base')}px;
                    spacing: {self.design_system.spacing('sm')}px;
                }}
                QCheckBox::indicator {{
                    width: 18px;
                    height: 18px;
                }}
                QCheckBox::indicator:unchecked {{
                    background-color: {self.design_system.color('surface')};
                    border: 2px solid {self.design_system.color('border')};
                    border-radius: {self.design_system.radius('sm')}px;
                }}
                    background-color: {self.design_system.color('primary')};
                    border: 2px solid {self.design_system.color('primary')};
                    border-radius: {self.design_system.radius('sm')}px;
                }}
            """
        except:
            # Fallback to legacy styling
            return """
                QCheckBox {
                    color: #D1D1E1;
                    font-size: 13px;
                    spacing: 8px;
                }
                QCheckBox::indicator:checked {
                    background-color: #6C63FF;
                    border: 2px solid #6C63FF;
                    border-radius: 4px;
                }
            """

    # Event Handlers
    def _configure_api(self):
        """Configure API settings"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLineEdit, QDialogButtonBox

            dialog = QDialog(self)
            dialog.setWindowTitle("API Configuration")
            dialog.setFixedSize(400, 200)
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #2D2D3F;
                    color: #FFFFFF;
                }
            """)

            layout = QVBoxLayout(dialog)

            # API Key input
            api_layout = QHBoxLayout()
            api_label = QLabel("API Key:")
            api_label.setStyleSheet("color: #D1D1E1; font-size: 14px;")
            api_input = QLineEdit()
            api_input.setEchoMode(QLineEdit.Password)
            api_input.setStyleSheet(self.get_input_style())
            api_layout.addWidget(api_label)
            api_layout.addWidget(api_input)
            layout.addLayout(api_layout)

            # Buttons
            buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            buttons.setStyleSheet("""
                QDialogButtonBox QPushButton {
                    background-color: #6C63FF;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: 600;
                }
                QDialogButtonBox QPushButton:hover {
                    background-color: #8075FF;
                }
            """)
            buttons.accepted.connect(dialog.accept)
            buttons.rejected.connect(dialog.reject)
            layout.addWidget(buttons)

            if dialog.exec_() == QDialog.Accepted:
                # Save API key
                api_key = api_input.text()
                if api_key:
                    self.config.set_value('cloud_config.api_key', api_key)
                    QMessageBox.information(self, "Success", "API key saved successfully!")

        except Exception as e:
            logger.error(f"Error configuring API: {e}")
            QMessageBox.critical(self, "Error", f"Failed to configure API: {str(e)}")

    def _export_settings(self):
        """Export application settings"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Export Settings",
                "knowledge_app_settings.json",
                "JSON Files (*.json);;All Files (*)"
            )

            if file_path:
                # Export settings logic here
                QMessageBox.information(self, "Export Complete", f"Settings exported to:\n{file_path}")

        except Exception as e:
            logger.error(f"Error exporting settings: {e}")
            QMessageBox.critical(self, "Error", f"Failed to export settings: {str(e)}")

    def _import_settings(self):
        """Import application settings"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Import Settings",
                "",
                "JSON Files (*.json);;All Files (*)"
            )

            if file_path:
                # Import settings logic here
                QMessageBox.information(self, "Import Complete", f"Settings imported from:\n{file_path}")

        except Exception as e:
            logger.error(f"Error importing settings: {e}")
            QMessageBox.critical(self, "Error", f"Failed to import settings: {str(e)}")

    def _handle_upload_books(self):
        """Handle book upload with proper file dialog and processing"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox, QProgressDialog
            from PyQt5.QtCore import QTimer
            import shutil
            from pathlib import Path

            # Open file dialog to select books
            files, _ = QFileDialog.getOpenFileNames(
                self,
                "Select Books to Upload",
                "",
                "Book Files (*.pdf *.txt *.doc *.docx *.epub *.mobi);;PDF Files (*.pdf);;Text Files (*.txt);;Word Documents (*.doc *.docx);;All Files (*)"
            )

            if not files:
                return

            # Get books directory
            books_dir = self._get_books_directory()
            if not books_dir:
                QMessageBox.critical(self, "Error", "Could not access books directory")
                return

            # Create progress dialog
            progress = QProgressDialog("Uploading books...", "Cancel", 0, len(files), self)
            progress.setWindowTitle("Upload Progress")
            progress.setModal(True)
            progress.show()

            # Process files
            uploaded_count = 0
            failed_files = []

            for i, file_path in enumerate(files):
                if progress.wasCanceled():
                    break

                try:
                    file_path = Path(file_path)
                    progress.setLabelText(f"Uploading: {file_path.name}")
                    progress.setValue(i)

                    # Check file size (use configurable limit)
                    max_file_size_mb = self.config.get_value('storage_settings.max_book_size_mb', 50)
                    max_file_size_bytes = max_file_size_mb * 1024 * 1024
                    if file_path.stat().st_size > max_file_size_bytes:
                        failed_files.append(f"{file_path.name} (too large - max {max_file_size_mb}MB)")
                        continue

                    # Copy file to books directory
                    destination = books_dir / file_path.name

                    # Handle duplicate names
                    counter = 1
                    original_stem = destination.stem
                    while destination.exists():
                        destination = books_dir / f"{original_stem}_{counter}{destination.suffix}"
                        counter += 1

                    shutil.copy2(file_path, destination)
                    uploaded_count += 1

                    # Small delay to show progress
                    QTimer.singleShot(50, lambda: None)

                except Exception as e:
                    logger.error(f"Failed to upload {file_path}: {e}")
                    failed_files.append(f"{file_path.name} ({str(e)})")

            progress.setValue(len(files))
            progress.close()

            # Show results
            if uploaded_count > 0:
                message = f"Successfully uploaded {uploaded_count} book(s)."
                if failed_files:
                    max_file_size_mb = self.config.get_value('storage_settings.max_book_size_mb', 50)
                    message += f"\n\nFailed uploads ({len(failed_files)}):\n" + "\n".join(failed_files[:5])
                    if len(failed_files) > 5:
                        message += f"\n... and {len(failed_files) - 5} more"
                    message += f"\n\nTip: You can increase the file size limit (currently {max_file_size_mb}MB) in Document Settings."

                QMessageBox.information(self, "Upload Complete", message)
            else:
                if failed_files:
                    max_file_size_mb = self.config.get_value('storage_settings.max_book_size_mb', 50)
                    message = "No books were uploaded successfully.\n\nErrors:\n" + "\n".join(failed_files[:5])
                    if len(failed_files) > 5:
                        message += f"\n... and {len(failed_files) - 5} more"
                    message += f"\n\nTip: You can increase the file size limit (currently {max_file_size_mb}MB) in Document Settings."
                else:
                    message = "No books were uploaded."
                QMessageBox.warning(self, "Upload Failed", message)

        except Exception as e:
            logger.error(f"Error in book upload: {e}")
            QMessageBox.critical(self, "Error", f"Failed to upload books: {str(e)}")

    def _get_books_directory(self):
        """Get or create the books directory"""
        try:
            # Try to get from config first
            if (self.parent and hasattr(self.parent, 'config') and
                hasattr(self.parent.config, '_config')):
                books_path = self.parent.config._config.get('paths', {}).get('uploaded_books')
                if books_path:
                    books_dir = Path(books_path)
                    books_dir.mkdir(parents=True, exist_ok=True)
                    return books_dir

            # Fallback to default location
            if self.parent and hasattr(self.parent, 'base_path'):
                base_path = Path(self.parent.base_path)
            else:
                base_path = Path.cwd()

            books_dir = base_path / 'data' / 'uploaded_books'
            books_dir.mkdir(parents=True, exist_ok=True)
            return books_dir

        except Exception as e:
            logger.error(f"Error getting books directory: {e}")
            # Emergency fallback
            fallback_dir = Path.cwd() / 'data' / 'uploaded_books'
            fallback_dir.mkdir(parents=True, exist_ok=True)
            return fallback_dir
        
    def add_document_section(self, parent_layout):
        """Add document settings section"""
        section = SettingsSection("Document Settings", self)
        
        # Upload button
        upload_btn = QPushButton("Upload Documents")
        upload_btn.setIcon(QIcon("assets/icons/upload.png"))
        upload_btn.clicked.connect(self._handle_upload_documents)
        section.add_setting("Upload:", upload_btn)
        
        # Storage limit
        storage_layout = QHBoxLayout()
        storage_label = QLabel("Storage Limit (MB):")
        self.storage_spin = QSpinBox(self)
        self.storage_spin.setRange(100, 10000)
        self.storage_spin.setValue(self.config.get_value('storage_config.image_cache_limit', 500) // (1024 * 1024))
        self.storage_spin.valueChanged.connect(self._on_storage_limit_changed)

        storage_layout.addWidget(storage_label)
        storage_layout.addWidget(self.storage_spin)
        section.add_setting("Storage:", storage_layout)

        # Max book file size
        book_size_layout = QHBoxLayout()
        book_size_label = QLabel("Max Book File Size (MB):")
        self.book_size_spin = QSpinBox(self)
        self.book_size_spin.setRange(1, 500)  # 1MB to 500MB
        self.book_size_spin.setValue(self.config.get_value('storage_settings.max_book_size_mb', 50))
        self.book_size_spin.valueChanged.connect(self._on_book_size_limit_changed)

        book_size_layout.addWidget(book_size_label)
        book_size_layout.addWidget(self.book_size_spin)
        section.add_setting("Book Size:", book_size_layout)
        
        parent_layout.addWidget(section)
        
    def _handle_upload_documents(self):
        """Handle document upload"""
        try:
            files, _ = QFileDialog.getOpenFileNames(
                self,
                "Select Documents",
                "",
                "Documents (*.pdf *.txt *.doc *.docx)"
            )
            
            if files:
                # Process uploaded files
                for file in files:
                    print(f"Processing uploaded file: {file}")
                    # TODO: Implement document processing
                    
                QMessageBox.information(
                    self,
                    "Upload Complete",
                    f"Successfully uploaded {len(files)} document(s)"
                )
                
        except Exception as e:
            QMessageBox.critical(
                self,
                "Upload Error",
                f"Error uploading documents: {str(e)}"
            )
            
    def _on_storage_limit_changed(self, limit_mb: int):
        """Handle storage limit change"""
        limit_bytes = limit_mb * 1024 * 1024
        self.config.set_value('storage_config.image_cache_limit', limit_bytes)
        self.settings_changed.emit({'storage_limit': limit_bytes})

    def _on_book_size_limit_changed(self, value):
        """Handle book file size limit change"""
        try:
            self.config.set_value('storage_settings.max_book_size_mb', value)
            self.config.save()
        except Exception as e:
            logger.error(f"Error updating book size limit: {e}")

    def add_display_section(self, parent_layout):
        """Add Display Settings section"""
        section = SettingsSection("Display Settings", self)
        
        # Theme selection
        self.theme_combo = QComboBox(self)
        self.theme_combo.addItems(["Dark", "Light"])
        self.theme_combo.currentTextChanged.connect(self._on_theme_changed)
        section.add_setting("Theme:", self.theme_combo)

        # Font size
        self.font_size_spin = QSpinBox(self)
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(12)
        self.font_size_spin.setSuffix(" pt")
        self.font_size_spin.valueChanged.connect(self._on_font_size_changed)
        section.add_setting("Font Size:", self.font_size_spin)
        
        parent_layout.addWidget(section)
        
    def _on_theme_changed(self, theme):
        """Handle theme change"""
        try:
            logger.info(f"🎨 SETTINGS: Theme change requested to {theme}")

            # Normalize theme name
            theme_normalized = theme.lower()

            # Save theme to configuration first
            self._save_theme_to_config(theme_normalized)

            # Update parent (main window) theme
            if self.parent and hasattr(self.parent, 'update_theme'):
                self.parent.update_theme(theme_normalized)
                logger.info(f"✅ SETTINGS: Theme change propagated to parent")
            elif self.parent and hasattr(self.parent, '_on_theme_changed'):
                self.parent._on_theme_changed(theme_normalized)
                logger.info(f"✅ SETTINGS: Theme change sent via _on_theme_changed")
            else:
                # Try to find the main window
                from PyQt5.QtWidgets import QApplication
                app = QApplication.instance()
                if app:
                    main_window = None
                    for widget in app.topLevelWidgets():
                        if hasattr(widget, '_on_theme_changed'):
                            main_window = widget
                            break

                    if main_window:
                        main_window._on_theme_changed(theme_normalized)
                        logger.info(f"✅ SETTINGS: Theme change sent to main window")
                    else:
                        logger.warning(f"⚠️ SETTINGS: Could not find main window to update theme")

            # Update this settings menu
            self.update_theme()

        except Exception as e:
            logger.error(f"❌ SETTINGS: Error changing theme: {e}")
            import traceback
            traceback.print_exc()

    def _save_theme_to_config(self, theme: str):
        """Save theme to configuration"""
        try:
            # Get configuration
            config = None
            if self.parent and hasattr(self.parent, 'config'):
                config = self.parent.config
            else:
                from ...core.config_manager import get_config
                config = get_config()

            if config:
                # Save to multiple paths for compatibility
                config.set_value('app_settings.theme', theme)
                config.set_value('ui.theme', theme)

                # Save to file
                config.save_config()
                logger.info(f"✅ SETTINGS: Theme '{theme}' saved to configuration")
            else:
                logger.error("❌ SETTINGS: No configuration available to save theme")

        except Exception as e:
            logger.error(f"❌ SETTINGS: Error saving theme to config: {e}")

    def _on_font_size_changed(self, size):
        """Handle font size change"""
        try:
            logger.info(f"🔤 SETTINGS: Font size change requested to {size}")

            # Save font size to configuration first
            self._save_font_size_to_config(size)

            # Update parent (main window) font size
            if self.parent and hasattr(self.parent, '_on_font_size_changed'):
                self.parent._on_font_size_changed(size)
                logger.info(f"✅ SETTINGS: Font size change sent to main window")
            else:
                # Try to find the main window
                from PyQt5.QtWidgets import QApplication
                app = QApplication.instance()
                if app:
                    main_window = None
                    for widget in app.topLevelWidgets():
                        if hasattr(widget, '_on_font_size_changed'):
                            main_window = widget
                            break

                    if main_window:
                        main_window._on_font_size_changed(size)
                        logger.info(f"✅ SETTINGS: Font size change sent to main window")
                    else:
                        logger.warning(f"⚠️ SETTINGS: Could not find main window to update font size")

        except Exception as e:
            logger.error(f"❌ SETTINGS: Error changing font size: {e}")
            import traceback
            traceback.print_exc()

    def _save_font_size_to_config(self, font_size: int):
        """Save font size to configuration"""
        try:
            # Get configuration
            config = None
            if self.parent and hasattr(self.parent, 'config'):
                config = self.parent.config
            else:
                from ...core.config_manager import get_config
                config = get_config()

            if config:
                # Validate font size range
                font_size = int(font_size)
                if font_size < 8:
                    logger.warning(f"⚠️ Font size too small '{font_size}', setting to minimum 8")
                    font_size = 8
                elif font_size > 32:
                    logger.warning(f"⚠️ Font size too large '{font_size}', setting to maximum 32")
                    font_size = 32

                # Save to multiple paths for compatibility
                config.set_value('app_settings.font_size', font_size)
                config.set_value('ui.font_size', font_size)
                config.set_value('ui_config.font_size', font_size)

                # Save to file
                config.save_config()
                logger.info(f"✅ SETTINGS: Font size '{font_size}' saved to configuration")
            else:
                logger.error("❌ SETTINGS: No configuration available to save font size")

        except Exception as e:
            logger.error(f"❌ SETTINGS: Error saving font size to config: {e}")
            
    def update_theme(self):
        """Update the theme of all widgets"""
        # Update scroll area
        for scroll in self.findChildren(QScrollArea):
            scroll.setStyleSheet(AppStyles.get_scrollbar_style())
            
        # Update sections
        for section in self.findChildren(SettingsSection):
            section.title_label.setStyleSheet(AppStyles.get_heading_style())
            section.content.setStyleSheet(AppStyles.get_card_style())
            
        # Update input widgets
        for widget in self.findChildren((QSpinBox, QComboBox)):
            widget.setStyleSheet(AppStyles.get_input_style())
            
        # Update buttons
        for button in self.findChildren(QPushButton):
            button.setStyleSheet(AppStyles.get_button_style())
            
        # Update labels
        for label in self.findChildren(QLabel):
            if label.parent() and isinstance(label.parent(), SettingsSection):
                label.setStyleSheet(AppStyles.get_heading_style())
            else:
                label.setStyleSheet(AppStyles.get_label_style())
                
    def load_settings(self):
        """Load current settings from config"""
        try:
            # Get configuration from multiple possible sources
            config = None
            if self.parent and hasattr(self.parent, 'config'):
                config = self.parent.config
            else:
                # Try to get global config
                from ...core.config_manager import get_config
                config = get_config()

            if config:
                # Load theme from multiple possible paths
                theme_paths = ['app_settings.theme', 'ui.theme', 'ui_config.theme']
                theme = None

                for path in theme_paths:
                    theme = config.get_value(path, None)
                    if theme:
                        break

                if not theme:
                    theme = 'dark'  # Default fallback

                # Set theme in combo box
                theme_idx = self.theme_combo.findText(theme.capitalize())
                if theme_idx >= 0:
                    self.theme_combo.setCurrentIndex(theme_idx)
                    logger.info(f"✅ Settings menu loaded theme: {theme}")
                else:
                    logger.warning(f"⚠️ Theme '{theme}' not found in combo box, using default")
                    self.theme_combo.setCurrentIndex(0)  # Default to first item

                # Load font size from multiple possible paths
                font_size_paths = ['app_settings.font_size', 'ui.font_size', 'ui_config.font_size']
                font_size = None

                for path in font_size_paths:
                    font_size = config.get_value(path, None)
                    if font_size is not None:
                        break

                if font_size is None:
                    font_size = 14  # Default fallback

                # Validate font size range
                font_size = max(8, min(32, int(font_size)))

                if hasattr(self, 'font_size_spin'):
                    self.font_size_spin.setValue(font_size)
                    logger.info(f"✅ Settings menu loaded font size: {font_size}")

            else:
                logger.warning("⚠️ No configuration available, using defaults")
                self.theme_combo.setCurrentIndex(0)  # Default to Dark
                if hasattr(self, 'font_size_spin'):
                    self.font_size_spin.setValue(12)

        except Exception as e:
            logger.error(f"❌ Error loading settings: {e}")
            # Set safe defaults
            self.theme_combo.setCurrentIndex(0)  # Default to Dark
            if hasattr(self, 'font_size_spin'):
                self.font_size_spin.setValue(12)
        
    def add_storage_section(self, parent_layout):
        """Add Storage Settings section"""
        section = SettingsSection("Storage Settings", self)
        self.storage_section = section
        
        # Storage usage label
        self.storage_label = QLabel(self)
        section.add_setting("Storage Usage:", self.storage_label)
        
        parent_layout.addWidget(section)
        
    def add_background_section(self, parent_layout):
        """Add Background Images section"""
        section = SettingsSection("Background Images", self)

        # Upload images button
        upload_btn = QPushButton("Upload Images")
        upload_btn.clicked.connect(self._upload_background_images)
        section.add_setting("Upload Images:", upload_btn)

        # Manage images button
        manage_btn = QPushButton("Manage Images")
        manage_btn.clicked.connect(self._manage_background_images)
        section.add_setting("Manage Images:", manage_btn)

        auto_change = QCheckBox(self)
        section.add_setting("Auto-change Background:", auto_change)

        interval = QSpinBox(self)
        interval.setRange(1, 60)
        interval.setValue(5)
        interval.setSuffix(" min")
        section.add_setting("Change Interval:", interval)

        # Storage usage label
        self.storage_label = QLabel(self)
        section.add_setting("Storage Usage:", self.storage_label)

        parent_layout.addWidget(section)
        
    def add_books_section(self, parent_layout):
        """Add Uploaded Books Management section"""
        section = SettingsSection("Uploaded Books Management", self)
        
        # Create a view books button
        view_books_btn = QPushButton("View All Books")
        view_books_btn.clicked.connect(self._show_books_dialog)
        view_books_btn.setProperty("type", "action")
        section.add_setting("View Books:", view_books_btn)
        
        # Add upload button
        upload_btn = QPushButton("Upload Books")
        upload_btn.clicked.connect(self._upload_books)
        upload_btn.setProperty("type", "action")
        section.add_setting("Add Books:", upload_btn)
        
        parent_layout.addWidget(section)
        
    def _show_books_dialog(self):
        """Show dialog with list of all uploaded books"""
        try:
            # Get books directory
            books_dir = self._get_books_directory()
            if not books_dir:
                QMessageBox.critical(self, "Error", "Could not access books directory")
                return

            # Get list of books
            books = [f.name for f in books_dir.iterdir()
                    if f.is_file() and f.suffix.lower() in ['.txt', '.pdf', '.doc', '.docx', '.epub', '.mobi']]

            # Create and show the dialog
            from ..uploaded_books_dialog import UploadedBooksDialog
            dialog = UploadedBooksDialog(self)
            dialog.set_books(books)
            dialog.exec_()

        except Exception as e:
            logger.error(f"Error showing books dialog: {e}")
            QMessageBox.critical(self, "Error", f"Failed to show books: {str(e)}")
            
    def _upload_books(self):
        """Handle book upload"""
        if self.parent and hasattr(self.parent, 'handle_upload_books_dialog'):
            self.parent.handle_upload_books_dialog()
            
    def add_diagnostics_section(self, parent_layout):
        """Add Diagnostics section"""
        section = SettingsSection("Diagnostics", self)
        
        # Add diagnostics button
        run_diagnostics_btn = QPushButton("Run Diagnostics")
        run_diagnostics_btn.clicked.connect(self._run_diagnostics)
        run_diagnostics_btn.setProperty("type", "action")
        section.add_setting("System Check:", run_diagnostics_btn)
        
        parent_layout.addWidget(section)
        
    def add_cloud_section(self, parent_layout):
        """Add cloud settings section"""
        section = SettingsSection("Cloud Settings", self)
        
        # Create provider selection
        provider_layout = QHBoxLayout()
        provider_combo = QComboBox(self)
        provider_combo.addItems(["OpenAI", "Azure OpenAI", "AWS SageMaker", "Google Cloud"])
        provider_layout.addWidget(provider_combo)

        # Create a container widget for the provider layout
        provider_container = QWidget(self)
        provider_container.setLayout(provider_layout)

        # Add the container widget to the section
        section.add_setting("Provider:", provider_container)

        # Add API key input
        api_key_input = QLineEdit(self)
        api_key_input.setEchoMode(QLineEdit.Password)
        section.add_setting("API Key:", api_key_input)
        
        # Add test connection button
        test_button = QPushButton("Test Connection")
        test_button.setProperty("type", "action")
        test_button.clicked.connect(lambda: self._test_cloud_connection(
            provider_combo.currentText(),
            api_key_input.text()
        ))
        section.add_setting("", test_button)
        
        parent_layout.addWidget(section)
        
    def add_export_section(self, parent_layout):
        """Add Export/Import section"""
        section = SettingsSection("Export / Import Settings Data", self)
        
        export_btn = QPushButton("Export Settings")
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #8075FF;
                padding: 8px;
                border-radius: 4px;
            }
        """)
        section.add_setting("Export:", export_btn)
        
        import_btn = QPushButton("Import Settings")
        import_btn.setStyleSheet("""
            QPushButton {
                background-color: #8075FF;
                padding: 8px;
                border-radius: 4px;
            }
        """)
        section.add_setting("Import:", import_btn)
        
        parent_layout.addWidget(section)
        
    def add_performance_section(self, parent_layout):
        """Add Performance section"""
        section = SettingsSection("Performance", self)
        
        # GPU toggle
        self.use_gpu = QCheckBox(self)
        self.use_gpu.setChecked(False)  # Default to False
        self.use_gpu.stateChanged.connect(self._on_gpu_toggle)
        section.add_setting("Use GPU (if available)", self.use_gpu)

        # CPU threads container
        self.cpu_threads_container = QWidget(self)
        cpu_threads_layout = QHBoxLayout(self.cpu_threads_container)
        cpu_threads_layout.setContentsMargins(0, 0, 0, 0)

        # CPU threads spinbox
        self.cpu_threads = QSpinBox(self.cpu_threads_container)
        self.cpu_threads.setRange(1, os.cpu_count() or 4)
        self.cpu_threads.setValue((os.cpu_count() or 4) - 1)  # Default to all but one core
        cpu_threads_layout.addWidget(self.cpu_threads)

        section.add_setting("CPU Threads:", self.cpu_threads_container)

        # Half precision toggle
        self.use_half_precision = QCheckBox(self)
        self.use_half_precision.setChecked(False)  # Default to False for safety
        section.add_setting("Use Half Precision (FP16)", self.use_half_precision)

        # Memory limit
        self.memory_limit = QSpinBox(self)
        self.memory_limit.setRange(1, 32)
        self.memory_limit.setValue(8)
        self.memory_limit.setSuffix(" GB")
        section.add_setting("System RAM Limit", self.memory_limit)
        
        parent_layout.addWidget(section)
        
        # Initial visibility of CPU threads based on GPU toggle
        self._on_gpu_toggle(self.use_gpu.checkState())
        
    def _on_gpu_toggle(self, state):
        """Handle GPU toggle state change"""
        # Hide CPU threads option when GPU is enabled
        is_gpu_enabled = state == Qt.Checked
        self.cpu_threads_container.setVisible(not is_gpu_enabled)
        # Also hide the label
        for widget in self.findChildren(QLabel):
            if widget.text() == "CPU Threads:":
                widget.setVisible(not is_gpu_enabled)
                
    def add_reset_section(self, parent_layout):
        """Add Reset Options section"""
        section = SettingsSection("Application Reset", self)
        
        # Reset Settings button
        reset_settings_btn = QPushButton("Reset App Settings")
        reset_settings_btn.setProperty("type", "warning")
        reset_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #FFA500;
                color: white;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #FF8C00;
            }
        """)
        reset_settings_btn.clicked.connect(self._on_reset_settings)
        section.add_setting("Reset configuration and user content:", reset_settings_btn)
        
        # Full Factory Reset button
        factory_reset_btn = QPushButton("Factory Reset (Complete)")
        factory_reset_btn.setProperty("type", "danger")
        factory_reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF4136;
                color: white;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #E02A24;
            }
        """)
        factory_reset_btn.clicked.connect(self._on_factory_reset)
        section.add_setting("Complete factory reset:", factory_reset_btn)
        
        parent_layout.addWidget(section)
        
    def _on_back(self):
        """Handle back button click"""
        if self.parent:
            self.parent.show_main_menu()

    def _upload_background_images(self):
        """Handle background image upload"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox

            files, _ = QFileDialog.getOpenFileNames(
                self,
                "Select Background Images",
                "",
                "Image Files (*.png *.jpg *.jpeg *.gif *.bmp *.tiff);;All Files (*)"
            )

            if not files:
                return

            # Get image manager from parent
            image_manager = None
            if self.parent and hasattr(self.parent, 'image_manager'):
                image_manager = self.parent.image_manager
            else:
                from ...core.image_manager import get_image_manager
                image_manager = get_image_manager()

            if not image_manager:
                QMessageBox.warning(self, "Error", "Image manager not available")
                return

            # Process images
            added_count = 0
            failed_files = []

            for file_path in files:
                try:
                    if image_manager.add_image(file_path):
                        added_count += 1
                    else:
                        failed_files.append(file_path)
                except Exception as e:
                    logger.error(f"Failed to add image {file_path}: {e}")
                    failed_files.append(file_path)

            # Show results
            if added_count > 0:
                message = f"Successfully added {added_count} background image(s)."
                if failed_files:
                    message += f"\n{len(failed_files)} file(s) failed to add."
                QMessageBox.information(self, "Images Added", message)

                # Update storage display
                self._update_storage_display()
            else:
                QMessageBox.warning(self, "No Images Added", "No images were successfully added.")

        except Exception as e:
            logger.error(f"Error uploading background images: {e}")
            QMessageBox.critical(self, "Error", f"Failed to upload images: {str(e)}")

    def _manage_background_images(self):
        """Handle background image management"""
        try:
            QMessageBox.information(
                self,
                "Manage Images",
                "Background image management feature will be available in a future update."
            )
        except Exception as e:
            logger.error(f"Error in manage background images: {e}")

    def _update_storage_display(self):
        """Update storage usage display"""
        try:
            if hasattr(self, 'storage_label') and self.storage_label:
                # Get storage info
                if self.parent and hasattr(self.parent, 'image_manager'):
                    usage = self.parent.image_manager.get_storage_usage()
                    usage_mb = usage / (1024 * 1024)
                    limit_mb = self.config.get_value('storage_config.image_cache_limit', 500) // (1024 * 1024)
                    self.storage_label.setText(f"{usage_mb:.2f} MB used / {limit_mb} MB limit")
                else:
                    self.storage_label.setText("Storage info unavailable")
        except Exception as e:
            logger.error(f"Error updating storage display: {e}")
            
    def get_settings(self) -> dict:
        """Get current settings as dictionary"""
        return {
            'use_gpu': self.use_gpu.isChecked(),
            'use_half_precision': self.use_half_precision.isChecked(),
            'memory_limit': self.memory_limit.value(),
            # ... rest of settings
        }
        
    def _run_diagnostics(self):
        """Run system diagnostics"""
        if self.parent and hasattr(self.parent, 'run_diagnostics'):
            self.parent.run_diagnostics()
            
    def _on_reset_settings(self):
        """Handle reset settings button click"""
        try:
            from pathlib import Path
            base_path = Path(self.parent.base_path) if hasattr(self.parent, 'base_path') else Path.cwd()
            
            # Delete user data directory
            user_dir = base_path / "user_data"
            if user_dir.exists():
                import shutil
                shutil.rmtree(user_dir)
                os.makedirs(user_dir)
                
            # Delete app settings file
            settings_file = base_path / "app_settings.json"
            if settings_file.exists():
                settings_file.unlink()
                
            # Delete app initialization flag to force reinitialization
            init_flag = base_path / "data" / "user_data" / ".app_initialized"
            if init_flag.exists():
                init_flag.unlink()
                
            # Restart application
            if self.parent:
                self.parent.restart_application()
                
        except Exception as e:
            logger.error(f"Error resetting settings: {e}")
            
    def _on_factory_reset(self):
        """Handle factory reset button click"""
        try:
            import shutil
            from pathlib import Path
            base_path = Path(self.parent.base_path) if hasattr(self.parent, 'base_path') else Path.cwd()
            
            # Delete data directory completely (includes models, adapters, etc.)
            data_dir = base_path / "data"
            if data_dir.exists():
                shutil.rmtree(data_dir)
                os.makedirs(data_dir)
                
            # Delete user_data directory
            user_dir = base_path / "user_data"
            if user_dir.exists():
                shutil.rmtree(user_dir)
                os.makedirs(user_dir)
                
            # Delete uploaded_books directory
            books_dir = base_path / "uploaded_books"
            if books_dir.exists():
                shutil.rmtree(books_dir)
                os.makedirs(books_dir)
                
            # Delete any cached data
            cache_dir = base_path / "cache"
            if cache_dir.exists():
                shutil.rmtree(cache_dir)
                os.makedirs(cache_dir)
                
            # Delete image_cache directory
            img_cache_dir = base_path / "image_cache"
            if img_cache_dir.exists():
                shutil.rmtree(img_cache_dir)
                os.makedirs(img_cache_dir)
                
            # Delete any other app-generated files
            for file in ["app_settings.json", "dependency_cache.json", "error_log.txt"]:
                file_path = base_path / file
                if file_path.exists():
                    file_path.unlink()
                    
            # Restart application
            if self.parent:
                self.parent.restart_application()
                
        except Exception as e:
            logger.error(f"Error performing factory reset: {e}")

    def _test_cloud_connection(self, provider, api_key):
        """Test cloud connection in a separate thread"""
        # Create worker and thread
        worker = CloudConnectionTester(provider, api_key)
        self.worker_thread = WorkerThread(self)
        self.worker_thread.worker = worker
        
        # Connect signals
        worker.finished.connect(self.worker_thread.quit)
        worker.finished.connect(worker.deleteLater)
        self.worker_thread.finished.connect(self.worker_thread.deleteLater)
        
        # Start thread
        self.worker_thread.start()
        
        return worker.result
    
    def _run_selected_tests(self):
        """Run the selected test category"""
        # Map UI selection to test categories
        category_map = {
            "All Tests": "all",
            "Core Tests": "core",
            "Hardware Tests": "hardware",
            "Model Tests": "models",
            "Training Tests": "training",
            "Integration Tests": "integration"
        }

        selected_text = self.test_category_combo.currentText()
        category = category_map.get(selected_text, "all")

        # Show progress dialog
        self._show_test_progress_dialog(category)

    def _show_test_progress_dialog(self, category):
        """Show test progress dialog and run tests"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QProgressBar, QPushButton

        # Create progress dialog
        self.test_dialog = QDialog(self)
        self.test_dialog.setWindowTitle("Running Tests")
        self.test_dialog.setFixedSize(500, 200)
        self.test_dialog.setModal(True)

        layout = QVBoxLayout(self.test_dialog)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # Title
        title_label = QLabel(f"Running {category.title()} Tests")
        title_label.setStyleSheet("""
            QLabel {
                color: #6C63FF;
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # Progress bar
        self.test_progress_bar = QProgressBar()
        self.test_progress_bar.setRange(0, 100)
        self.test_progress_bar.setValue(0)
        self.test_progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #3D3D4F;
                border-radius: 8px;
                text-align: center;
                background-color: #2D2D3F;
                color: white;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #6C63FF;
                border-radius: 6px;
            }
        """)
        layout.addWidget(self.test_progress_bar)

        # Status label
        self.test_status_label = QLabel("Initializing tests...")
        self.test_status_label.setStyleSheet("""
            QLabel {
                color: #D1D1E1;
                font-size: 12px;
                margin: 5px 0;
                padding: 8px;
                background-color: #2D2D3F;
                border-radius: 6px;
                border: 1px solid #3D3D4F;
            }
        """)
        self.test_status_label.setWordWrap(True)
        layout.addWidget(self.test_status_label)

        # Close button (initially disabled)
        self.test_close_btn = QPushButton("Close")
        self.test_close_btn.setEnabled(False)
        self.test_close_btn.setStyleSheet(self.get_button_style())
        self.test_close_btn.clicked.connect(self.test_dialog.close)
        layout.addWidget(self.test_close_btn)

        # Start test runner
        self._run_tests(category)

        # Show dialog
        self.test_dialog.show()

    def _run_tests(self, category):
        """Run tests in a separate thread"""
        # Create worker and thread
        worker = TestRunner(category)
        self.worker_thread = WorkerThread(self)
        self.worker_thread.worker = worker

        # Connect signals
        worker.progress.connect(self._update_test_progress)
        worker.status.connect(self._update_test_status)
        worker.finished.connect(self._on_tests_finished)
        worker.finished.connect(self.worker_thread.quit)
        worker.finished.connect(worker.deleteLater)
        self.worker_thread.finished.connect(self.worker_thread.deleteLater)

        # Start thread
        self.worker_thread.start()

    def _update_test_progress(self, value):
        """Update test progress bar"""
        if hasattr(self, 'test_progress_bar'):
            self.test_progress_bar.setValue(value)

    def _update_test_status(self, message):
        """Update test status label with color coding based on message content"""
        if hasattr(self, 'test_status_label'):
            self.test_status_label.setText(message)

            # Apply color coding based on message content
            if "✅ ALL TESTS PASSED" in message:
                # Success - green background
                self.test_status_label.setStyleSheet("""
                    QLabel {
                        color: #FFFFFF;
                        font-size: 13px;
                        font-weight: bold;
                        margin: 5px 0;
                        padding: 12px;
                        background-color: #4CAF50;
                        border-radius: 8px;
                        border: 2px solid #45A049;
                    }
                """)
            elif "⚠️ SOME TESTS FAILED" in message:
                # Warning - orange/red background
                self.test_status_label.setStyleSheet("""
                    QLabel {
                        color: #FFFFFF;
                        font-size: 13px;
                        font-weight: bold;
                        margin: 5px 0;
                        padding: 12px;
                        background-color: #FF9800;
                        border-radius: 8px;
                        border: 2px solid #F57C00;
                    }
                """)
            elif "❌" in message or "💥" in message or "⏰" in message:
                # Individual test failure - subtle red
                self.test_status_label.setStyleSheet("""
                    QLabel {
                        color: #FF6B6B;
                        font-size: 12px;
                        margin: 5px 0;
                        padding: 8px;
                        background-color: #2D2D3F;
                        border-radius: 6px;
                        border: 1px solid #FF6B6B;
                    }
                """)
            elif "✅" in message:
                # Individual test success - subtle green
                self.test_status_label.setStyleSheet("""
                    QLabel {
                        color: #4CAF50;
                        font-size: 12px;
                        margin: 5px 0;
                        padding: 8px;
                        background-color: #2D2D3F;
                        border-radius: 6px;
                        border: 1px solid #4CAF50;
                    }
                """)
            else:
                # Default status - neutral
                self.test_status_label.setStyleSheet("""
                    QLabel {
                        color: #D1D1E1;
                        font-size: 12px;
                        margin: 5px 0;
                        padding: 8px;
                        background-color: #2D2D3F;
                        border-radius: 6px;
                        border: 1px solid #3D3D4F;
                    }
                """)

    def _on_mcq_mode_changed(self, mode_text):
        """Handle MCQ generation mode change"""
        try:
            # Update status indicator
            if "Offline Mode" in mode_text:
                self.mcq_status_label.setText("🏠 Using local models")
                self.mcq_status_label.setStyleSheet("""
                    QLabel {
                        color: #FF9800;
                        font-size: 12px;
                        font-weight: 500;
                        padding: 4px 8px;
                        background-color: #2D2D3F;
                        border-radius: 4px;
                        border: 1px solid #FF9800;
                    }
                """)
                is_offline = True
            else:
                self.mcq_status_label.setText("🌐 Using external API")
                self.mcq_status_label.setStyleSheet("""
                    QLabel {
                        color: #4CAF50;
                        font-size: 12px;
                        font-weight: 500;
                        padding: 4px 8px;
                        background-color: #2D2D3F;
                        border-radius: 4px;
                        border: 1px solid #4CAF50;
                    }
                """)
                is_offline = False

            # Save setting to config
            if hasattr(self, 'config') and self.config:
                self.config.set_value('mcq_settings.offline_mode', is_offline)
                if hasattr(self.config, '_save_user_settings'):
                    self.config._save_user_settings()

            # Emit settings changed signal
            self.settings_changed.emit({'mcq_offline_mode': is_offline})

            logger.info(f"MCQ generation mode changed to: {'Offline' if is_offline else 'Online'}")

        except Exception as e:
            logger.error(f"Error changing MCQ mode: {e}")

    def _on_tests_finished(self):
        """Handle test completion"""
        if hasattr(self, 'test_close_btn'):
            self.test_close_btn.setEnabled(True)

            # Update button text and style based on final status
            if hasattr(self, 'test_status_label'):
                final_status = self.test_status_label.text()
                if "✅ ALL TESTS PASSED" in final_status:
                    self.test_close_btn.setText("✅ Close")
                    self.test_close_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #4CAF50;
                            color: white;
                            border: none;
                            border-radius: 8px;
                            padding: 10px 20px;
                            font-size: 13px;
                            font-weight: 600;
                            min-width: 120px;
                        }
                        QPushButton:hover {
                            background-color: #45A049;
                        }
                    """)
                elif "⚠️ SOME TESTS FAILED" in final_status:
                    self.test_close_btn.setText("⚠️ Close")
                    self.test_close_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #FF9800;
                            color: white;
                            border: none;
                            border-radius: 8px;
                            padding: 10px 20px;
                            font-size: 13px;
                            font-weight: 600;
                            min-width: 120px;
                        }
                        QPushButton:hover {
                            background-color: #F57C00;
                        }
                    """)
                else:
                    self.test_close_btn.setText("Close")
                    self.test_close_btn.setStyleSheet(self.get_button_style())
            else:
                self.test_close_btn.setText("Close")
                self.test_close_btn.setStyleSheet(self.get_button_style())

    def update_settings(self, settings: dict) -> None:
        """
        Update settings from external source
        
        Args:
            settings: Dictionary containing updated settings
        """
        try:
            # Update display settings
            if 'display' in settings:
                display = settings['display']
                if 'theme' in display:
                    theme_idx = self.theme_combo.findText(display['theme'])
                    if theme_idx >= 0:
                        self.theme_combo.setCurrentIndex(theme_idx)
                if 'font_size' in display:
                    self.font_size_spin.setValue(display['font_size'])
                if 'animations' in display:
                    self.animations_check.setChecked(display['animations'])
                    
            # Update storage settings
            if 'storage' in settings:
                storage = settings['storage']
                if 'cache_limit' in storage:
                    self.cache_limit_spin.setValue(storage['cache_limit'])
                if 'cleanup_threshold' in storage:
                    self.cleanup_threshold_spin.setValue(storage['cleanup_threshold'])
                    
            # Update performance settings
            if 'performance' in settings:
                perf = settings['performance']
                if 'gpu_enabled' in perf:
                    self.gpu_toggle.setChecked(perf['gpu_enabled'])
                if 'max_threads' in perf:
                    self.thread_count_spin.setValue(perf['max_threads'])
                    
            # Update cloud settings
            if 'cloud' in settings:
                cloud = settings['cloud']
                if 'provider' in cloud:
                    provider_idx = self.cloud_provider_combo.findText(cloud['provider'])
                    if provider_idx >= 0:
                        self.cloud_provider_combo.setCurrentIndex(provider_idx)
                if 'api_key' in cloud:
                    self.api_key_input.setText(cloud['api_key'])
                    
            # Emit settings changed signal
            self.settings_changed.emit(self.get_settings())
            
        except Exception as e:
            logger.error(f"Error updating settings: {e}")
            QMessageBox.warning(self, "Settings Error", 
                              f"Failed to update settings: {str(e)}")
