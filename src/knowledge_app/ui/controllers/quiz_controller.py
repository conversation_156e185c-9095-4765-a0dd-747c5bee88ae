"""
Quiz Controller - Business Logic for Quiz Management

This controller implements the Model-View-Controller pattern by separating
quiz business logic from the UI presentation layer. It handles:
- Quiz state management
- Timer logic
- Answer validation
- Question progression
- Score calculation
- History management

The controller communicates with the view through signals and slots,
ensuring clean separation of concerns.
"""

import logging
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
from PyQt5.QtCore import QObject, QTimer, pyqtSignal

logger = logging.getLogger(__name__)

class QuizState:
    """Represents the current state of a quiz session"""
    
    def __init__(self):
        self.current_question: Optional[Dict[str, Any]] = None
        self.selected_option_index: int = -1
        self.is_answered: bool = False
        self.is_correct: Optional[bool] = None
        self.remaining_time: int = 0
        self.timer_active: bool = False
        self.quiz_mode: str = "casual"  # casual, serious
        self.question_count: int = 0
        self.correct_answers: int = 0
        self.history: List[Dict[str, Any]] = []

class QuizController(QObject):
    """
    Controller for quiz business logic and state management.
    
    This controller handles all quiz-related business logic while keeping
    the view (QuizScreen) focused purely on presentation.
    """
    
    # Signals for communicating with the view
    question_loaded = pyqtSignal(dict)  # question_data
    answer_checked = pyqtSignal(bool, str, str)  # is_correct, feedback, explanation
    timer_updated = pyqtSignal(int)  # remaining_seconds
    timer_expired = pyqtSignal()
    quiz_completed = pyqtSignal(dict)  # final_stats
    state_changed = pyqtSignal(dict)  # state_data
    
    def __init__(self, question_service=None, parent=None):
        super().__init__(parent)
        
        # Dependencies
        self.question_service = question_service
        self.parent = parent
        
        # State management
        self.state = QuizState()
        
        # Timer setup
        self.timer = QTimer(self)
        self.timer.timeout.connect(self._update_timer)
        
        # Configuration
        self.serious_mode_time_limit = 300  # 5 minutes
        self.casual_mode_time_limit = 0  # No time limit
        
        logger.debug("QuizController initialized")
    
    def start_quiz(self, mode: str = "casual") -> None:
        """
        Start a new quiz session.
        
        Args:
            mode: Quiz mode ('casual' or 'serious')
        """
        try:
            # Reset state
            self.state = QuizState()
            self.state.quiz_mode = mode
            
            # Configure timer based on mode
            if mode == "serious":
                self.state.remaining_time = self.serious_mode_time_limit
            else:
                self.state.remaining_time = self.casual_mode_time_limit
            
            # Load first question
            self.load_next_question()
            
            logger.info(f"Quiz started in {mode} mode")
            
        except Exception as e:
            logger.error(f"Error starting quiz: {e}")
            raise
    
    def load_next_question(self) -> None:
        """Load the next question in the quiz"""
        try:
            # Check if we have a question service (adapter or direct service)
            if not self.question_service:
                logger.warning("No question service available, trying parent methods...")
                # Fall back to parent methods if no question service
                question_data = self._get_next_question_from_parent()
            else:
                # Use the question service (adapter)
                logger.info("Using question service to get next question")
                question_data = self._get_next_question_from_service()

            if question_data:
                self.state.current_question = question_data
                self.state.selected_option_index = -1
                self.state.is_answered = False
                self.state.is_correct = None

                # Start timer if in serious mode
                if self.state.quiz_mode == "serious" and self.state.remaining_time > 0:
                    self.start_timer()

                # Emit signal to update view
                self.question_loaded.emit(question_data)
                self._emit_state_changed()

                logger.debug("Next question loaded")
            else:
                # No more questions - complete quiz
                logger.warning("No question data received, completing quiz")
                self.complete_quiz()

        except Exception as e:
            logger.error(f"Error loading next question: {e}")
            raise
    
    def select_option(self, option_index: int) -> None:
        """
        Handle option selection.

        Args:
            option_index: Index of the selected option
        """
        try:
            if self.state.is_answered:
                logger.warning("Question already answered")
                return

            self.state.selected_option_index = option_index
            self._emit_state_changed()

            logger.debug(f"Option {option_index} selected")

        except Exception as e:
            logger.error(f"Error selecting option: {e}")
    
    def submit_answer(self) -> None:
        """Submit the current answer and provide feedback"""
        try:
            if not self.state.current_question:
                logger.warning("No current question to submit answer for")
                return
            
            if self.state.selected_option_index == -1:
                logger.warning("No option selected")
                return
            
            if self.state.is_answered:
                logger.warning("Question already answered")
                return
            
            # Stop timer
            self.stop_timer()
            
            # Check answer
            is_correct = self._check_answer()
            feedback = self._generate_feedback(is_correct)
            explanation = self._get_explanation()
            
            # Update state
            self.state.is_answered = True
            self.state.is_correct = is_correct
            self.state.question_count += 1
            
            if is_correct:
                self.state.correct_answers += 1
            
            # Add to history
            self._add_to_history()
            
            # Emit signals
            self.answer_checked.emit(is_correct, feedback, explanation)
            self._emit_state_changed()
            
            logger.debug(f"Answer submitted: correct={is_correct}")
            
        except Exception as e:
            logger.error(f"Error submitting answer: {e}")
            raise
    
    def start_timer(self) -> None:
        """Start the quiz timer"""
        try:
            if self.state.remaining_time > 0:
                self.state.timer_active = True
                self.timer.start(1000)  # Update every second
                logger.debug("Timer started")
                
        except Exception as e:
            logger.error(f"Error starting timer: {e}")
    
    def stop_timer(self) -> None:
        """Stop the quiz timer"""
        try:
            self.state.timer_active = False
            self.timer.stop()
            logger.debug("Timer stopped")
            
        except Exception as e:
            logger.error(f"Error stopping timer: {e}")
    
    def complete_quiz(self) -> None:
        """Complete the current quiz and emit final statistics"""
        try:
            self.stop_timer()
            
            # Calculate final statistics
            accuracy = (self.state.correct_answers / self.state.question_count * 100) if self.state.question_count > 0 else 0
            
            final_stats = {
                'total_questions': self.state.question_count,
                'correct_answers': self.state.correct_answers,
                'accuracy': accuracy,
                'quiz_mode': self.state.quiz_mode,
                'completion_time': datetime.now().isoformat(),
                'history': self.state.history.copy()
            }
            
            # Emit completion signal
            self.quiz_completed.emit(final_stats)
            
            logger.info(f"Quiz completed: {self.state.correct_answers}/{self.state.question_count} correct ({accuracy:.1f}%)")
            
        except Exception as e:
            logger.error(f"Error completing quiz: {e}")
            raise
    
    def get_current_state(self) -> Dict[str, Any]:
        """Get the current quiz state as a dictionary"""
        return {
            'has_question': self.state.current_question is not None,
            'selected_option': self.state.selected_option_index,
            'is_answered': self.state.is_answered,
            'is_correct': self.state.is_correct,
            'remaining_time': self.state.remaining_time,
            'timer_active': self.state.timer_active,
            'quiz_mode': self.state.quiz_mode,
            'question_count': self.state.question_count,
            'correct_answers': self.state.correct_answers,
            'can_submit': self.state.selected_option_index >= 0 and not self.state.is_answered
        }

    # Private methods for internal logic

    def _get_next_question_from_service(self) -> Optional[Dict[str, Any]]:
        """Get the next question from the question service (adapter)"""
        try:
            logger.info("🔄 Getting question from question service adapter")

            # Check if the service has the get_next_question method
            if hasattr(self.question_service, 'get_next_question'):
                question_data = self.question_service.get_next_question()
                if question_data:
                    logger.info("✅ Question service adapter provided question")
                    return question_data
                else:
                    logger.warning("⚠️ Question service adapter returned no question")
                    return None
            else:
                logger.warning("⚠️ Question service doesn't have get_next_question method")
                return None

        except Exception as e:
            logger.error(f"❌ Error getting question from service: {e}")
            return None

    def _get_next_question_from_parent(self) -> Optional[Dict[str, Any]]:
        """Get the next question from parent methods (fallback)"""
        try:
            print(f"DEBUG_RUNTIME: QuizController._get_next_question_from_parent called")
            print(f"DEBUG_RUNTIME: self.parent = {self.parent}")
            print(f"DEBUG_RUNTIME: self.parent type = {type(self.parent)}")
            print(f"DEBUG_RUNTIME: hasattr(self.parent, 'get_next_question_sync') = {hasattr(self.parent, 'get_next_question_sync') if self.parent else 'parent is None'}")

            # Try synchronous question generation first (for quiz controller)
            if hasattr(self.parent, 'get_next_question_sync'):
                print("DEBUG_RUNTIME: Calling get_next_question_sync...")
                logger.info("🔄 Using synchronous question generation")
                result = self.parent.get_next_question_sync()
                print(f"DEBUG_RUNTIME: get_next_question_sync returned: {result is not None}")
                return result

            # Fall back to async generation (triggers display but doesn't return question)
            elif hasattr(self.parent, 'generate_next_question'):
                logger.info("🔄 Using asynchronous question generation (fallback)")
                self.parent.generate_next_question()
                return None  # Async method doesn't return question directly

            # Legacy method support
            elif hasattr(self.parent, 'load_new_question'):
                return self.parent.load_new_question()
            else:
                logger.warning("No question generation method available")
                return None

        except Exception as e:
            logger.error(f"Error getting next question from parent: {e}")
            return None

    def _check_answer(self) -> bool:
        """Check if the selected answer is correct"""
        try:
            if not self.state.current_question:
                return False

            # Get correct answer
            correct_option_letter = self.state.current_question.get('correct_option_letter', '').upper()

            # Convert index to letter (A=0, B=1, C=2, D=3)
            selected_letter = chr(ord('A') + self.state.selected_option_index)

            return selected_letter == correct_option_letter

        except Exception as e:
            logger.error(f"Error checking answer: {e}")
            return False

    def _generate_feedback(self, is_correct: bool) -> str:
        """Generate feedback message based on answer correctness"""
        if is_correct:
            return "✅ Correct! Well done!"
        else:
            correct_letter = self.state.current_question.get('correct_option_letter', '').upper()
            return f"❌ Incorrect. The correct answer was {correct_letter}."

    def _get_explanation(self) -> str:
        """Get explanation for the current question"""
        if self.state.current_question:
            return self.state.current_question.get('explanation', 'No explanation available.')
        return ""

    def _add_to_history(self) -> None:
        """Add the current question and answer to history"""
        try:
            if not self.state.current_question:
                return

            selected_letter = chr(ord('A') + self.state.selected_option_index) if self.state.selected_option_index >= 0 else 'None'

            history_entry = {
                'question_data': self.state.current_question.copy(),
                'user_answer_letter': selected_letter,
                'is_correct': self.state.is_correct,
                'timestamp': datetime.now().isoformat(),
                'timed_out': False
            }

            self.state.history.append(history_entry)

            # Also add to parent's history if available
            if hasattr(self.parent, 'add_to_history'):
                self.parent.add_to_history(history_entry)

        except Exception as e:
            logger.error(f"Error adding to history: {e}")

    def _update_timer(self) -> None:
        """Update timer and handle expiration"""
        try:
            if not self.state.timer_active:
                return

            self.state.remaining_time -= 1
            self.timer_updated.emit(self.state.remaining_time)

            if self.state.remaining_time <= 0:
                self._handle_timer_expiration()

        except Exception as e:
            logger.error(f"Error updating timer: {e}")

    def _handle_timer_expiration(self) -> None:
        """Handle timer expiration"""
        try:
            self.stop_timer()

            # If an answer is selected, submit it
            if self.state.selected_option_index >= 0:
                self.submit_answer()
            else:
                # Mark as timed out
                self.state.is_answered = True
                self.state.is_correct = False
                self.state.question_count += 1

                # Add timeout entry to history
                if self.state.current_question:
                    timeout_entry = {
                        'question_data': self.state.current_question.copy(),
                        'user_answer_letter': 'None',
                        'is_correct': False,
                        'timestamp': datetime.now().isoformat(),
                        'timed_out': True
                    }
                    self.state.history.append(timeout_entry)

                    if hasattr(self.parent, 'add_to_history'):
                        self.parent.add_to_history(timeout_entry)

            # Emit timer expiration signal
            self.timer_expired.emit()
            self._emit_state_changed()

        except Exception as e:
            logger.error(f"Error handling timer expiration: {e}")

    def _emit_state_changed(self) -> None:
        """Emit state changed signal with current state"""
        try:
            current_state = self.get_current_state()
            self.state_changed.emit(current_state)

        except Exception as e:
            logger.error(f"Error emitting state changed: {e}")
