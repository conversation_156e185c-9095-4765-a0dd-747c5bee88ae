"""
Training Dialog for Knowledge App

This module provides a simplified training dialog for the 7B model
targeting 85% accuracy for MCQ generation.
"""

import logging
import time
import numpy as np
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QProgressBar, QGroupBox, QSpinBox, QRadioButton,
    QButtonGroup, QWidget, QScrollArea, QComboBox, QTabWidget
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPalette, QColor
import psutil
import torch
from typing import Dict, Any

from ..core.gpu_manager import GPUManager
from ..core.model_manager import ModelManager
from ..core.training_worker import TrainingWorker
from ..core.training_presets import (
    PresetConfig, OptimizerType, LearningRateSchedule
)
# CRITICAL MEMORY FIX: Import FIREEstimator lazily to prevent circular imports
from ..core.real_7b_trainer import Real7<PERSON><PERSON><PERSON><PERSON>, Real7BConfig
from ..core.real_7b_config import get_real_7b_config, SUPPORTED_MODELS
from .fire_progress_widget import FIREProgressWidget
# Legacy import removed - using enterprise design system
# Enterprise styling imports
from .enterprise_style_manager import get_style_manager
from .enterprise_design_system import get_design_system
from .style_migration_helper import get_migration_helper

logger = logging.getLogger(__name__)

class AITrainingDialog(QDialog):
    """Dialog for training the AI model with simplified settings"""
    
    # Signals
    training_started = pyqtSignal(dict)  # Emits training config
    training_cancelled = pyqtSignal()
    training_completed = pyqtSignal(bool, str)  # Success flag and message
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🔥 FIRE AI Training - Advanced Progress Monitoring")

        # Calculate responsive window size based on screen resolution
        from .fire_progress_widget import get_system_scale_factor
        self.scale_factor = get_system_scale_factor()

        # Improved window sizing with better bounds
        base_width, base_height = 1100, 800  # Slightly larger base size
        min_width = max(1000, min(1600, int(base_width * self.scale_factor)))
        min_height = max(700, min(1200, int(base_height * self.scale_factor)))
        self.setMinimumSize(min_width, min_height)

        # Set preferred size for better initial appearance
        preferred_width = max(1200, min(1800, int(1300 * self.scale_factor)))
        preferred_height = max(900, min(1400, int(1000 * self.scale_factor)))
        self.resize(preferred_width, preferred_height)

        # Initialize enterprise styling system
        self.style_manager = get_style_manager()
        self.design_system = get_design_system()
        self.migration_helper = get_migration_helper()

        # Initialize state
        self.is_training = False
        self.training_worker = None
        self.model_manager = ModelManager()

        # Initialize FIRE estimator with v2.1 compatibility wrapper
        self.fire_estimator = FIREEstimator()  # Uses FIREv21Estimator under the hood with compatibility layer
        self.fire_progress_widget = None

        # Real 7B training
        self.real_7b_trainer = None
        self.real_7b_config_manager = get_real_7b_config()

        # Metrics tracking
        self.training_start_time = None
        self.last_metrics_update = 0

        # Timer for generating synthetic metrics (for demo)
        self.metrics_timer = QTimer()
        self.metrics_timer.timeout.connect(self._generate_demo_metrics)

        # Timer for forcing UI updates during real training
        self.ui_update_timer = QTimer()
        self.ui_update_timer.timeout.connect(self._force_ui_update)
        self.ui_update_timer.setInterval(2000)  # Update every 2 seconds

        self._init_ui()
        
    def _init_ui(self):
        """Initialize the UI components with FIRE progress monitoring"""
        layout = QVBoxLayout()

        # Create tab widget for different views
        tab_widget = QTabWidget()

        # Tab 1: Training Setup
        setup_tab = QWidget()
        setup_layout = QVBoxLayout(setup_tab)

        # Real 7B Model Training Selection
        preset_group = QGroupBox("🔥 REAL 7B MODEL TRAINING")
        preset_layout = QVBoxLayout()

        # Description label
        description_label = QLabel("Professional-grade training with actual 7B+ parameter models using QLoRA optimization:")
        description_label.setStyleSheet("color: #666; font-size: 11px; margin-bottom: 10px;")
        description_label.setWordWrap(True)

        # 🔥 REAL 7B MODEL TRAINING BUTTONS with enterprise styling
        self.mistral_7b_button = QPushButton("🔥 Mistral-7B QLoRA (6-12 hours)")
        self.mistral_7b_button.setToolTip("REAL 7B parameter Mistral model with QLoRA - Actual transformer training!")
        self.mistral_7b_button.setStyleSheet(self._get_training_button_style("#FF6B35"))
        self.mistral_7b_button.clicked.connect(lambda: self.select_real_7b_training("mistral-7b"))

        self.llama_8b_button = QPushButton("🦙 Llama-3-8B QLoRA (8-16 hours)")
        self.llama_8b_button.setToolTip("REAL 8B parameter Llama-3 model with QLoRA - Maximum performance!")
        self.llama_8b_button.setStyleSheet(self._get_training_button_style("#4CAF50"))
        self.llama_8b_button.clicked.connect(lambda: self.select_real_7b_training("llama-3-8b"))

        self.qwen_7b_button = QPushButton("🔮 Qwen2.5-7B QLoRA (6-12 hours)")
        self.qwen_7b_button.setToolTip("REAL 7B parameter Qwen2.5 Instruct model with QLoRA - Latest Chinese AI!")
        self.qwen_7b_button.setStyleSheet(self._get_training_button_style("#9C27B0"))
        self.qwen_7b_button.clicked.connect(lambda: self.select_real_7b_training("qwen-7b"))

        self.gemma_9b_button = QPushButton("💎 Gemma-2-9B QLoRA (8-16 hours)")
        self.gemma_9b_button.setToolTip("REAL 9B parameter Gemma-2 model with QLoRA - Google's latest!")
        self.gemma_9b_button.setStyleSheet(self._get_training_button_style("#2196F3"))
        self.gemma_9b_button.clicked.connect(lambda: self.select_real_7b_training("gemma-7b"))

        self.mpt_7b_button = QPushButton("⚡ MPT-7B QLoRA (6-12 hours)")
        self.mpt_7b_button.setToolTip("REAL 7B parameter MPT Instruct model with QLoRA - MosaicML's open model!")
        self.mpt_7b_button.setStyleSheet(self._get_training_button_style("#FF9800"))
        self.mpt_7b_button.clicked.connect(lambda: self.select_real_7b_training("mpt-7b"))

        # Store training buttons for easy access
        self.training_buttons = [
            self.mistral_7b_button, self.llama_8b_button, self.qwen_7b_button,
            self.gemma_9b_button, self.mpt_7b_button
        ]

        preset_layout.addWidget(description_label)
        preset_layout.addWidget(self.mistral_7b_button)
        preset_layout.addWidget(self.llama_8b_button)
        preset_layout.addWidget(self.qwen_7b_button)
        preset_layout.addWidget(self.gemma_9b_button)
        preset_layout.addWidget(self.mpt_7b_button)

        preset_group.setLayout(preset_layout)

        # Hardware info
        hardware_group = QGroupBox("💻 Hardware Information")
        hardware_layout = QVBoxLayout()

        # CRITICAL MEMORY FIX: Get GPU info without importing torch during startup
        try:
            import torch
            gpu_info = "GPU: " + (torch.cuda.get_device_name(0) if torch.cuda.is_available() else "CPU Only")
            memory_info = f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB" if torch.cuda.is_available() else "No GPU"
        except ImportError:
            gpu_info = "GPU: PyTorch not available"
            memory_info = "GPU Memory: PyTorch not available"

        hardware_layout.addWidget(QLabel(gpu_info))
        hardware_layout.addWidget(QLabel(memory_info))
        hardware_layout.addWidget(QLabel(f"CPU Cores: {psutil.cpu_count()}"))
        hardware_layout.addWidget(QLabel(f"System RAM: {psutil.virtual_memory().total / 1e9:.1f}GB"))
        hardware_group.setLayout(hardware_layout)

        setup_layout.addWidget(preset_group)
        setup_layout.addWidget(hardware_group)
        setup_layout.addStretch()

        # Tab 2: FIRE Progress Monitoring
        progress_tab = QWidget()
        progress_layout = QVBoxLayout(progress_tab)
        progress_layout.setContentsMargins(self._get_scaled_spacing(8), self._get_scaled_spacing(8),
                                         self._get_scaled_spacing(8), self._get_scaled_spacing(8))

        # Initialize FIRE progress widget with proper vertical scaling
        self.fire_progress_widget = FIREProgressWidget()
        self.fire_progress_widget.training_cancelled.connect(self.cancel_training)

        # Add with stretch factor to allow vertical expansion
        progress_layout.addWidget(self.fire_progress_widget, 1)

        # Add tabs
        tab_widget.addTab(setup_tab, "🔧 Setup")
        tab_widget.addTab(progress_tab, "🔥 FIRE Progress")

        # Main control buttons
        button_layout = QHBoxLayout()

        self.checkpoints_button = QPushButton("📁 Manage Checkpoints")
        self.checkpoints_button.setStyleSheet(self.style_manager.get_style('button_secondary'))
        self.checkpoints_button.clicked.connect(self.show_checkpoints)
        button_layout.addWidget(self.checkpoints_button)

        self.cancel_button = QPushButton("❌ Cancel Training")
        self.cancel_button.setStyleSheet(self.style_manager.get_style('button_danger'))
        self.cancel_button.setEnabled(False)
        self.cancel_button.clicked.connect(self.cancel_training)

        self.close_button = QPushButton("✅ Close")
        self.close_button.setStyleSheet(self.style_manager.get_style('button_primary'))
        self.close_button.clicked.connect(self.close)

        button_layout.addWidget(self.cancel_button)
        button_layout.addStretch()
        button_layout.addWidget(self.close_button)

        # Add all components to main layout
        layout.addWidget(tab_widget)
        layout.addLayout(button_layout)

        self.setLayout(layout)

        # Apply enterprise styling
        self.apply_enterprise_styling()

    def _get_system_scale_factor(self):
        """Get system DPI scale factor for responsive UI scaling"""
        try:
            from PyQt5.QtWidgets import QApplication
            app = QApplication.instance()
            if app:
                screen = app.primaryScreen()
                if screen:
                    dpi = screen.logicalDotsPerInch()
                    # Standard DPI is 96, calculate scale factor
                    return max(1.0, dpi / 96.0)
        except Exception:
            pass
        return 1.0

    def _get_scaled_spacing(self, base_spacing: int) -> int:
        """Get spacing scaled for current system DPI"""
        return max(2, int(base_spacing * self.scale_factor))

    def apply_enterprise_styling(self):
        """Apply enterprise styling to the training dialog"""
        try:
            # Apply main dialog styling
            dialog_style = f"""
                QDialog {{
                    background: {self.design_system.color('bg_primary')};
                    color: {self.design_system.color('text_primary')};
                    font-family: {self.design_system._typography['font_family_primary']};
                }}
                QTabWidget::pane {{
                    border: 1px solid {self.design_system.color('border')};
                    border-radius: {self.design_system.radius('lg')}px;
                    background: {self.design_system.color('surface')};
                }}
                QTabBar::tab {{
                    background: {self.design_system.color('surface')};
                    border: 1px solid {self.design_system.color('border')};
                    padding: {self.design_system.spacing('sm')}px {self.design_system.spacing('lg')}px;
                    margin-right: 2px;
                    border-top-left-radius: {self.design_system.radius('md')}px;
                    border-top-right-radius: {self.design_system.radius('md')}px;
                    color: {self.design_system.color('text_primary')};
                    font-size: {self.design_system.font_size('base')}px;
                }}
                QTabBar::tab:selected {{
                    background: {self.design_system.color('primary')};
                    color: {self.design_system.color('text_on_primary')};
                    font-weight: {self.design_system._typography['font_weight_semibold']};
                }}
                QTabBar::tab:hover {{
                    background: {self.design_system.color('hover')};
                }}
                QGroupBox {{
                    font-size: {self.design_system.font_size('lg')}px;
                    font-weight: {self.design_system._typography['font_weight_semibold']};
                    color: {self.design_system.color('text_primary')};
                    border: 2px solid {self.design_system.color('border')};
                    border-radius: {self.design_system.radius('lg')}px;
                    margin-top: {self.design_system.spacing('md')}px;
                    padding-top: {self.design_system.spacing('sm')}px;
                }}
                QGroupBox::title {{
                    subcontrol-origin: margin;
                    left: {self.design_system.spacing('md')}px;
                    padding: 0 {self.design_system.spacing('sm')}px 0 {self.design_system.spacing('sm')}px;
                }}
            """
            self.setStyleSheet(dialog_style)

            logger.info("✅ Enterprise styling applied to training dialog")

        except Exception as e:
            logger.warning(f"Enterprise styling failed for training dialog, using fallback: {e}")
            # Fallback to basic styling
            self.setStyleSheet("""
                QDialog {
                    background-color: #2b2b2b;
                    color: white;
                }
            """)

    def _get_training_button_style(self, color: str) -> str:
        """Get enterprise-styled training button with custom color and responsive scaling"""
        try:
            # Calculate responsive values
            scaled_padding = max(12, int(self.design_system.spacing('lg') * self.scale_factor))
            scaled_font_size = max(14, int(self.design_system.font_size('base') * self.scale_factor))
            scaled_min_height = max(40, int(self.design_system.dimension('button_height_lg') * self.scale_factor))
            scaled_border_radius = max(6, int(self.design_system.radius('lg') * self.scale_factor))

            return f"""
                QPushButton {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 {color},
                        stop: 1 {color}CC);
                    color: {self.design_system.color('text_on_primary')};
                    border: none;
                    border-radius: {scaled_border_radius}px;
                    padding: {scaled_padding}px;
                    font-size: {scaled_font_size}px;
                    font-weight: {self.design_system._typography['font_weight_semibold']};
                    min-height: {scaled_min_height}px;
                }}
                QPushButton:hover {{
                    background: {color}DD;
                    transform: translateY(-1px);
                }}
                QPushButton:pressed {{
                    background: {color}AA;
                    transform: translateY(0px);
                }}
                QPushButton:disabled {{
                    background: {self.design_system.color('disabled')};
                    color: {self.design_system.color('text_disabled')};
                }}
            """
        except:
            # Fallback to simple styling with responsive scaling
            scaled_padding = max(12, int(15 * self.scale_factor))
            scaled_font_size = max(12, int(13 * self.scale_factor))
            scaled_border_radius = max(6, int(8 * self.scale_factor))

            return f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    font-weight: bold;
                    padding: {scaled_padding}px;
                    font-size: {scaled_font_size}px;
                    border: none;
                    border-radius: {scaled_border_radius}px;
                }}
            """

    def _disable_training_buttons(self):
        """Disable all training buttons to prevent multiple training instances"""
        for button in self.training_buttons:
            button.setEnabled(False)
            button.setStyleSheet(button.styleSheet() + "background-color: #888888 !important;")

    def _enable_training_buttons(self):
        """Re-enable all training buttons after training completes"""
        for button in self.training_buttons:
            button.setEnabled(True)
            # Remove the disabled styling
            style = button.styleSheet().replace("background-color: #888888 !important;", "")
            button.setStyleSheet(style)

    def _cleanup_previous_trainer(self):
        """Clean up any existing trainer instance to prevent memory leaks"""
        if hasattr(self, 'real_7b_trainer') and self.real_7b_trainer:
            try:
                # Disconnect signals to prevent issues
                self.real_7b_trainer.progress_update.disconnect()
                self.real_7b_trainer.epoch_progress.disconnect()
                self.real_7b_trainer.step_progress.disconnect()
                self.real_7b_trainer.training_complete.disconnect()
                self.real_7b_trainer.memory_update.disconnect()

                # Stop the trainer if it's running
                if self.real_7b_trainer.isRunning():
                    self.real_7b_trainer.stop()
                    self.real_7b_trainer.wait(3000)  # Wait up to 3 seconds

                # Delete the trainer
                self.real_7b_trainer.deleteLater()
                self.real_7b_trainer = None
                logger.info("🧹 Previous trainer instance cleaned up")

            except Exception as e:
                logger.warning(f"Error cleaning up previous trainer: {e}")

    def select_real_7b_training(self, model_name: str):
        """Start REAL 7B model training with QLoRA/LoRA

        Args:
            model_name: Model to train ('mistral-7b', 'llama-3-8b')
        """
        try:
            # Prevent multiple training instances
            if self.is_training:
                logger.warning("🚫 Training already in progress! Cannot start another training session.")
                return

            logger.info(f"🔥 Starting REAL {model_name} training!")

            # Clean up any previous trainer instance
            self._cleanup_previous_trainer()

            # Disable training buttons to prevent multiple clicks
            self._disable_training_buttons()

            # Validate configuration
            self.real_7b_config_manager.update_model_selection(model_name)
            issues = self.real_7b_config_manager.validate_config()

            if issues:
                error_msg = "Configuration issues found:\n" + "\n".join(issues)
                logger.error(error_msg)
                self._enable_training_buttons()  # Re-enable buttons on error
                self._handle_training_complete(False, error_msg)
                return

            # Get optimized config for hardware - this now returns a Real7BConfig object
            real_config = self.real_7b_config_manager.get_optimized_config_for_hardware()
            model_config = self.real_7b_config_manager.get_model_config(model_name)

            # Update the model name in the config
            real_config.model_name = model_config.model_id

            # Get training data path from the config manager and make it absolute
            training_data_path = self.real_7b_config_manager.config["data"]["train_data_path"]

            # Convert to absolute path to avoid working directory issues
            import os
            if not os.path.isabs(training_data_path):
                training_data_path = os.path.abspath(training_data_path)

            # Verify the training data file exists before starting training
            if not os.path.exists(training_data_path):
                error_msg = f"Training data file not found: {training_data_path}"
                logger.error(f"❌ {error_msg}")
                self.show_error(error_msg)
                self._enable_training_buttons()
                return

            logger.info(f"✅ Training data verified: {training_data_path} ({os.path.getsize(training_data_path) / 1024 / 1024:.1f} MB)")

            # Show training summary
            summary = self.real_7b_config_manager.get_training_summary()
            logger.info("🔥 REAL 7B Training Configuration:")
            for key, value in summary.items():
                logger.info(f"   {key}: {value}")

            # Switch to progress tab
            tab_widget = self.findChild(QTabWidget)
            if tab_widget:
                tab_widget.setCurrentIndex(1)

            # Calculate total steps for accurate progress tracking
            # Estimate dataset size and calculate steps
            dataset_size = 100000  # Estimate based on training data
            effective_batch_size = real_config.per_device_train_batch_size * real_config.gradient_accumulation_steps
            steps_per_epoch = dataset_size // effective_batch_size
            total_steps = steps_per_epoch * real_config.num_train_epochs

            # 🔥 FIRE v2.1 - Truth-based estimation configuration
            fire_config = {
                'epochs': real_config.num_train_epochs,
                'batch_size': real_config.per_device_train_batch_size,
                'learning_rate': real_config.learning_rate,
                'dataset_size': dataset_size,
                'total_steps': total_steps,
                'steps_per_epoch': steps_per_epoch,
                'use_mixed_precision': True,
                'gradient_accumulation_steps': real_config.gradient_accumulation_steps,
                'warmup_steps': 100,
                'model_size': '7B',
                'use_qlora': real_config.use_qlora,
                # 🔥 NEW: Pass the actual trainer and dataset for truth-based estimation
                'trainer': None,  # Will be set after trainer creation
                'train_dataset': None  # Will be set after dataset preparation
            }

            if self.fire_progress_widget:
                # Use legacy estimator for now (will be updated when trainer is ready)
                self.fire_progress_widget.start_training_session(self.fire_estimator, fire_config)

            # Create and start real 7B trainer with checkpoint support
            resume_checkpoint = getattr(self, 'resume_checkpoint_path', None)
            self.real_7b_trainer = Real7BTrainer(real_config, training_data_path, resume_checkpoint)

            # Connect signals
            self.real_7b_trainer.progress_update.connect(self._update_real_7b_progress)
            self.real_7b_trainer.epoch_progress.connect(self._update_real_7b_epoch)
            self.real_7b_trainer.step_progress.connect(self._update_real_7b_step)
            self.real_7b_trainer.training_complete.connect(self._handle_real_7b_complete)
            self.real_7b_trainer.memory_update.connect(self._update_memory)

            # Update UI state
            self.is_training = True
            self.training_start_time = time.time()
            self.cancel_button.setEnabled(True)

            # Stop demo metrics timer since we're doing real training
            self.metrics_timer.stop()
            logger.info("🔥 Demo metrics timer stopped - using real training progress")

            # Start UI update timer to ensure responsiveness
            self.ui_update_timer.start()
            self.last_ui_update_time = time.time()
            logger.info("🔥 UI update timer started for real training")

            # Update UI immediately to show training has started
            if self.fire_progress_widget:
                self.fire_progress_widget.status_label.setText("Status: Training Starting 🚀 - Initializing...")
                self.fire_progress_widget.update()

            # Start the REAL training
            self.real_7b_trainer.start()

            # Update UI to show training is running
            if self.fire_progress_widget:
                self.fire_progress_widget.status_label.setText("Status: Training Active 🔥 - Loading model...")
                self.fire_progress_widget.update()

            # Emit training started signal
            self.training_started.emit({
                'model': model_name,
                'type': 'real_7b',
                'parameters': summary['parameters'],
                'method': summary['method']
            })

            logger.info(f"🔥 REAL {model_name} training started!")

        except Exception as e:
            logger.error(f"Error starting real 7B training: {str(e)}")
            self._enable_training_buttons()  # Re-enable buttons on error
            self._handle_training_complete(False, str(e))
            
    def cancel_training(self):
        """Cancel the training process"""
        if self.is_training:
            # Cancel regular training worker
            if self.training_worker:
                self.training_worker.stop()
                logger.info("🔥 FIRE training cancelled by user")

            # Cancel real 7B training
            if self.real_7b_trainer:
                self.real_7b_trainer.stop()
                logger.info("🔥 REAL 7B training cancelled by user")

            self.is_training = False
            self.cancel_button.setEnabled(False)
            self.metrics_timer.stop()

            # Re-enable training buttons
            self._enable_training_buttons()

            # Stop FIRE progress widget
            if self.fire_progress_widget:
                self.fire_progress_widget.stop_training_session(completed=False)

            self.training_cancelled.emit()

    def _generate_demo_metrics(self):
        """Generate demo training metrics for FIRE estimation (for demonstration)"""
        if not self.is_training or not self.fire_progress_widget:
            return

        # Don't generate demo metrics if real 7B training is active
        if hasattr(self, 'real_7b_trainer') and self.real_7b_trainer and self.real_7b_trainer.isRunning():
            return

        try:
            # Calculate elapsed time
            elapsed_time = time.time() - self.training_start_time if self.training_start_time else 0

            # Generate realistic training metrics
            epoch = min(int(elapsed_time / 30), 30)  # 30 seconds per epoch for demo
            batch = int((elapsed_time % 30) * 10)  # 10 batches per epoch for demo

            # Simulate improving accuracy and decreasing loss
            progress_ratio = min(elapsed_time / 600, 1.0)  # 10 minutes total for demo
            accuracy = 0.3 + (0.6 * progress_ratio) + np.random.normal(0, 0.02)  # 30% to 90% with noise
            loss = 2.0 * (1 - progress_ratio) + np.random.normal(0, 0.1)  # 2.0 to 0.0 with noise

            # Simulate learning rate decay
            learning_rate = 0.001 * (0.95 ** epoch)

            # Simulate GPU utilization
            gpu_utilization = 85 + np.random.normal(0, 5)
            memory_usage = 70 + np.random.normal(0, 3)

            # Create training metrics
            metrics = TrainingMetrics(
                epoch=epoch,
                batch=batch,
                loss=max(0.01, loss),
                accuracy=max(0.1, min(0.95, accuracy)),
                learning_rate=learning_rate,
                gpu_utilization=max(0, min(100, gpu_utilization)),
                gpu_memory_usage=max(0, min(100, memory_usage)),  # Fixed: was 'memory_usage'
                time_elapsed=elapsed_time
            )

            # Update FIRE progress widget
            self.fire_progress_widget.update_training_metrics(metrics)

        except Exception as e:
            logger.error(f"Error generating demo metrics: {e}")

    def _force_ui_update(self):
        """Force UI update during real training to ensure responsiveness"""
        try:
            if self.is_training and self.fire_progress_widget:
                # Force widget to update and process events
                self.fire_progress_widget.update()
                self.fire_progress_widget.repaint()

                # Update status if no recent updates
                current_time = time.time()
                if hasattr(self, 'last_ui_update_time'):
                    time_since_update = current_time - self.last_ui_update_time
                    if time_since_update > 10:  # No updates for 10 seconds
                        self.fire_progress_widget.status_label.setText("Status: Training Active 🔥 - Processing...")
                else:
                    self.last_ui_update_time = current_time

        except Exception as e:
            logger.debug(f"Error in force UI update: {e}")

    def _update_phase_progress(self, progress: int):
        """Update phase progress from training worker"""
        # This method handles progress updates from the actual training worker
        pass

    def _update_real_7b_progress(self, message: str):
        """Update progress from real 7B trainer"""
        logger.info(f"🔥 REAL 7B PROGRESS: {message}")

        # Track last update time
        self.last_ui_update_time = time.time()

        # Update FIRE progress widget status
        if self.fire_progress_widget:
            # Always update the status label with the latest message
            self.fire_progress_widget.status_label.setText(f"Status: {message}")

            # Extract step information if available
            if "Step" in message and "/" in message:
                try:
                    # Parse step progress: "📊 Step 50/7362 (0.7%)"
                    parts = message.split()
                    for i, part in enumerate(parts):
                        if "/" in part and part.replace("/", "").replace("(", "").replace(")", "").replace("%", "").replace(",", "").isdigit():
                            step_info = part.split("/")
                            current_step = int(step_info[0])
                            total_steps = int(step_info[1].replace("(", "").replace(",", ""))

                            # Update FIRE widget with step progress
                            progress_pct = (current_step / total_steps) * 100
                            self.fire_progress_widget.status_label.setText(f"Status: Training Active 🔥 - Step {current_step}/{total_steps} ({progress_pct:.1f}%)")

                            # Update progress bars if they exist
                            if hasattr(self.fire_progress_widget, 'progress_bar'):
                                self.fire_progress_widget.progress_bar.setValue(int(progress_pct))
                            break
                except Exception as e:
                    logger.debug(f"Error parsing step progress: {e}")

            # Update for epoch messages
            elif "epoch" in message.lower():
                self.fire_progress_widget.status_label.setText(f"Status: {message}")

            # Update for training start
            elif "Training started" in message:
                self.fire_progress_widget.status_label.setText("Status: Training Active 🔥 - Starting...")

            # Force widget update
            self.fire_progress_widget.update()

        # Always print progress updates to console for debugging
        print(f"🔥 {message}")

    def _update_real_7b_epoch(self, current_epoch: int, total_epochs: int, loss: float):
        """Update epoch progress from real 7B trainer"""
        logger.info(f"🔥 Real 7B Epoch {current_epoch}/{total_epochs}, Loss: {loss:.4f}")

        # Update FIRE progress widget status immediately
        if self.fire_progress_widget:
            epoch_message = f"Epoch {current_epoch}/{total_epochs}, Loss: {loss:.4f}"
            self.fire_progress_widget.status_label.setText(f"Status: Training Active 🔥 - {epoch_message}")

            # Update epoch progress if widget has epoch display
            if hasattr(self.fire_progress_widget, 'epoch_label'):
                self.fire_progress_widget.epoch_label.setText(f"Epoch: {current_epoch}/{total_epochs}")
            if hasattr(self.fire_progress_widget, 'loss_label'):
                self.fire_progress_widget.loss_label.setText(f"Loss: {loss:.4f}")

            # Force widget update
            self.fire_progress_widget.update()

        # Create real training metrics for FIRE estimator
        if self.fire_progress_widget and self.training_start_time:
            elapsed_time = time.time() - self.training_start_time

            # Calculate realistic accuracy based on loss
            # Lower loss = higher accuracy (simplified)
            estimated_accuracy = max(0.1, min(0.95, 1.0 - (loss * 0.4)))  # More realistic scaling

            # Estimate batch number based on epoch progress
            estimated_batch = current_epoch * 100  # Rough estimate

            metrics = TrainingMetrics(
                epoch=current_epoch,
                batch=estimated_batch,
                loss=loss,
                accuracy=estimated_accuracy,
                learning_rate=2e-4,  # From config
                gpu_utilization=90.0,  # High for real 7B training
                gpu_memory_usage=85.0,    # Fixed: was 'memory_usage'
                time_elapsed=elapsed_time
            )

            # Update FIRE progress widget with real metrics
            self.fire_progress_widget.update_training_metrics(metrics)

        # Print to console for immediate feedback
        print(f"🔥 Epoch {current_epoch}/{total_epochs}, Loss: {loss:.4f}")

        # Update progress bars in FIRE widget
        if self.fire_progress_widget:
            if hasattr(self.fire_progress_widget, 'epoch_progress'):
                epoch_progress = int((current_epoch / total_epochs) * 100)
                self.fire_progress_widget.epoch_progress.setValue(epoch_progress)

            if hasattr(self.fire_progress_widget, 'epoch_label'):
                self.fire_progress_widget.epoch_label.setText(f"Epoch: {current_epoch}/{total_epochs}")

            if hasattr(self.fire_progress_widget, 'loss_label'):
                self.fire_progress_widget.loss_label.setText(f"Loss: {loss:.4f}")

            if hasattr(self.fire_progress_widget, 'accuracy_label'):
                estimated_accuracy = max(0.1, min(0.95, 1.0 - (loss * 0.4)))
                self.fire_progress_widget.accuracy_label.setText(f"Accuracy: {estimated_accuracy:.1%}")

    def _update_real_7b_step(self, current_step: int, total_steps: int, loss: float, learning_rate: float):
        """Update step progress from real 7B trainer with detailed metrics"""
        logger.info(f"🔥 Real 7B Step {current_step}/{total_steps}, Loss: {loss:.4f}, LR: {learning_rate:.2e}")

        # Print to console for immediate feedback
        progress_pct = (current_step / total_steps) * 100 if total_steps > 0 else 0
        print(f"🔥 Step {current_step}/{total_steps} ({progress_pct:.1f}%) - Loss: {loss:.4f}")

        # Create comprehensive training metrics for FIRE estimator
        if self.fire_progress_widget and self.training_start_time:
            elapsed_time = time.time() - self.training_start_time

            # Calculate current epoch based on step progress
            # Assuming roughly equal steps per epoch
            steps_per_epoch = total_steps / 3 if total_steps > 0 else 1  # 3 epochs default
            current_epoch = int(current_step / steps_per_epoch)

            # Calculate realistic accuracy based on loss and progress
            progress_ratio = current_step / total_steps if total_steps > 0 else 0
            base_accuracy = 0.3 + (0.6 * progress_ratio)  # Progress from 30% to 90%
            loss_factor = max(0.1, min(1.0, 1.0 - (loss * 0.3)))  # Loss influence
            estimated_accuracy = base_accuracy * loss_factor

            # Calculate batch number within current epoch
            current_batch = current_step % int(steps_per_epoch) if steps_per_epoch > 0 else current_step

            # Create detailed training metrics
            metrics = TrainingMetrics(
                epoch=current_epoch,
                batch=current_batch,
                loss=loss,
                accuracy=estimated_accuracy,
                learning_rate=learning_rate,
                gpu_utilization=90.0,  # High for real 7B training
                gpu_memory_usage=85.0,     # Fixed: was 'memory_usage'
                time_elapsed=elapsed_time
            )

            # Update FIRE progress widget with real-time metrics
            self.fire_progress_widget.update_training_metrics(metrics)

            # Update progress bars and labels directly for immediate feedback
            if hasattr(self.fire_progress_widget, 'overall_progress'):
                overall_progress = int((current_step / total_steps) * 100) if total_steps > 0 else 0
                self.fire_progress_widget.overall_progress.setValue(overall_progress)

            if hasattr(self.fire_progress_widget, 'batch_label'):
                self.fire_progress_widget.batch_label.setText(f"Batch: {current_batch}")

            if hasattr(self.fire_progress_widget, 'loss_label'):
                self.fire_progress_widget.loss_label.setText(f"Loss: {loss:.4f}")

            if hasattr(self.fire_progress_widget, 'lr_label'):
                self.fire_progress_widget.lr_label.setText(f"Learning Rate: {learning_rate:.6f}")

            if hasattr(self.fire_progress_widget, 'accuracy_label'):
                self.fire_progress_widget.accuracy_label.setText(f"Accuracy: {estimated_accuracy:.1%}")

            # Update status with step progress
            if hasattr(self.fire_progress_widget, 'status_label'):
                progress_pct = (current_step / total_steps) * 100 if total_steps > 0 else 0
                self.fire_progress_widget.status_label.setText(
                    f"Status: Training Active 🔥 - Step {current_step}/{total_steps} ({progress_pct:.1f}%)"
                )

    def _handle_real_7b_complete(self, success: bool, message: str, metrics: dict):
        """Handle completion of real 7B training"""
        self.is_training = False
        self.cancel_button.setEnabled(False)

        # Stop UI update timer
        if hasattr(self, 'ui_update_timer'):
            self.ui_update_timer.stop()

        # Re-enable training buttons
        self._enable_training_buttons()

        if self.fire_progress_widget:
            self.fire_progress_widget.stop_training_session(completed=success)

        if success:
            logger.info(f"🎉 REAL 7B training completed successfully!")
            logger.info(f"📊 Training metrics: {metrics}")
            status_text = f"🎉 REAL 7B training completed! Time: {metrics.get('training_time_hours', 0):.2f}h"
        else:
            logger.error(f"❌ REAL 7B training failed: {message}")
            status_text = f"❌ REAL 7B training failed: {message}"

        self.training_completed.emit(success, status_text)
            
    def _update_progress(self, progress_message: str):
        """Update the progress display from training worker

        Args:
            progress_message: Progress message string from training worker
        """
        try:
            # Log the progress message
            logger.info(f"Training progress: {progress_message}")

            # Extract information from progress message if possible
            # Format: "Epoch X/Y, Batch Z/W, Loss: L.LLLL"
            if "Epoch" in progress_message and "Loss:" in progress_message:
                # Parse epoch and loss information
                parts = progress_message.split(", ")
                epoch_part = parts[0] if len(parts) > 0 else ""
                loss_part = parts[-1] if len(parts) > 0 else ""

                # Update FIRE progress widget if available
                if self.fire_progress_widget and self.is_training:
                    # This is handled by the demo metrics generator
                    # Real implementation would extract actual values here
                    pass

        except Exception as e:
            logger.error(f"Error updating progress: {str(e)}")
            
    def _update_memory(self, memory_stats: dict):
        """Update memory usage display - now routes to FIRE progress widget

        Args:
            memory_stats: Dictionary containing memory statistics
        """
        try:
            # Route memory updates to FIRE progress widget if available
            if self.fire_progress_widget and self.is_training:
                # Convert memory stats to format expected by FIRE widget
                gpu_utilization = 85.0  # Placeholder - would get from actual monitoring

                if 'allocated' in memory_stats and 'total' in memory_stats:
                    allocated = memory_stats.get('allocated', 0)
                    total = memory_stats.get('total', 1)
                    memory_usage = (allocated / total) * 100 if total > 0 else 0
                else:
                    memory_usage = 70.0  # Fallback

                # Create synthetic metrics for memory update
                # In a real implementation, you'd get these from the actual training process
                current_time = time.time()
                elapsed = current_time - self.training_start_time if self.training_start_time else 0

                # This is handled by the demo metrics generator, so we don't need to do anything here
                logger.debug(f"Memory stats received: {memory_stats}")
            else:
                logger.debug("No FIRE progress widget available for memory update")

        except Exception as e:
            logger.error(f"Error updating memory stats: {str(e)}")
            
    def _handle_training_complete(self, success: bool, message: str):
        """Handle training completion with FIRE session finalization

        Args:
            success: Whether training completed successfully
            message: Completion message
        """
        self.is_training = False
        self.cancel_button.setEnabled(False)
        self.metrics_timer.stop()

        # Re-enable training buttons
        self._enable_training_buttons()

        # Stop FIRE progress widget
        if self.fire_progress_widget:
            self.fire_progress_widget.stop_training_session(completed=success)

        # Create final metrics for FIRE estimator
        if self.fire_estimator and self.training_start_time:
            total_time = time.time() - self.training_start_time
            final_metrics = TrainingMetrics(
                epoch=30,  # Final epoch
                batch=300,  # Final batch
                loss=0.1 if success else 1.0,
                accuracy=0.9 if success else 0.5,
                learning_rate=0.0001,
                gpu_utilization=0.0,  # Training finished
                gpu_memory_usage=50.0,  # Fixed: was 'memory_usage'
                time_elapsed=total_time
            )

            # No basic preset config needed for real 7B training
            config = {}
            self.fire_estimator.finish_training_session(final_metrics, config)

        # Show completion status
        if success:
            status_text = "🎉 Training completed successfully!"
            logger.info("🔥 FIRE training completed successfully")
        else:
            status_text = f"❌ Training failed: {message}"
            logger.error(f"🔥 FIRE training failed: {message}")

        # Update any remaining status labels
        if hasattr(self, 'status_label'):
            self.status_label.setText(status_text)

        self.training_completed.emit(success, message)

    def show_checkpoints(self):
        """Show checkpoint management dialog"""
        try:
            from .checkpoint_dialog import CheckpointDialog
            import os

            # Determine checkpoint directory based on current training type
            checkpoint_dir = None

            if hasattr(self, 'real_7b_trainer') and self.real_7b_trainer:
                checkpoint_dir = str(self.real_7b_trainer.checkpoint_dir)
            else:
                # Default checkpoint directory
                from ..core.app_config import AppConfig
                config = AppConfig()
                output_dir = config.get_setting('training.output_dir', 'data/lora_adapters_mistral/real_7b')
                checkpoint_dir = os.path.join(output_dir, 'checkpoints')

            if not checkpoint_dir or not os.path.exists(checkpoint_dir):
                self.show_info("No checkpoints found. Start training to create checkpoints.")
                return

            # Show checkpoint dialog
            dialog = CheckpointDialog(checkpoint_dir, self)
            dialog.checkpoint_selected.connect(self.resume_from_checkpoint)
            dialog.exec_()

        except Exception as e:
            logger.error(f"Failed to show checkpoints: {e}")
            self.show_error(f"Failed to show checkpoints: {e}")

    def closeEvent(self, event):
        """Handle dialog close event - clean up resources"""
        try:
            # Cancel any ongoing training
            if self.is_training:
                self.cancel_training()

            # Clean up trainer instance
            self._cleanup_previous_trainer()

            # Stop any timers
            if hasattr(self, 'metrics_timer'):
                self.metrics_timer.stop()

            logger.info("🧹 Training dialog closed and cleaned up")

        except Exception as e:
            logger.error(f"Error during dialog cleanup: {e}")

        super().closeEvent(event)

    def resume_from_checkpoint(self, checkpoint_path: str):
        """Resume training from a specific checkpoint"""
        try:
            logger.info(f"🔄 Resuming training from checkpoint: {checkpoint_path}")

            # Store checkpoint path for next training session
            self.resume_checkpoint_path = checkpoint_path

            # Show confirmation
            import os
            checkpoint_name = os.path.basename(checkpoint_path)
            self.show_info(f"Training will resume from checkpoint: {checkpoint_name}\n\nClick 'Start Training' to begin.")

        except Exception as e:
            logger.error(f"Failed to set resume checkpoint: {e}")
            self.show_error(f"Failed to set resume checkpoint: {e}")

    def show_info(self, message: str):
        """Show info message"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "Information", message)

    def show_error(self, message: str):
        """Show error message"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.critical(self, "Error", message)

class TrainingConfigDialog(QDialog):
    """Dialog for configuring model training"""
    
    def __init__(self, parent=None):
        """Initialize dialog"""
        super().__init__(parent)
        self.setWindowTitle("Training Configuration")
        self.setModal(True)
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the user interface"""
        layout = QVBoxLayout()
        
        # Device selection
        device_layout = QHBoxLayout()
        device_label = QLabel("Device:")
        self.device_combo = QComboBox()
        self.device_combo.addItems(["CPU", "GPU"])
        device_layout.addWidget(device_label)
        device_layout.addWidget(self.device_combo)
        layout.addLayout(device_layout)
        
        # Batch size
        batch_layout = QHBoxLayout()
        batch_label = QLabel("Batch Size:")
        self.batch_spin = QSpinBox()
        self.batch_spin.setRange(1, 128)
        self.batch_spin.setValue(32)
        batch_layout.addWidget(batch_label)
        batch_layout.addWidget(self.batch_spin)
        layout.addLayout(batch_layout)
        
        # Epochs
        epochs_layout = QHBoxLayout()
        epochs_label = QLabel("Epochs:")
        self.epochs_spin = QSpinBox()
        self.epochs_spin.setRange(1, 50)
        self.epochs_spin.setValue(10)
        epochs_layout.addWidget(epochs_label)
        epochs_layout.addWidget(self.epochs_spin)
        layout.addLayout(epochs_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        ok_button = QPushButton("Start Training")
        cancel_button = QPushButton("Cancel")
        ok_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
    def get_config(self) -> Dict[str, Any]:
        """Get training configuration"""
        return {
            'device': self.device_combo.currentText().lower(),
            'batch_size': self.batch_spin.value(),
            'epochs': self.epochs_spin.value()
        }

