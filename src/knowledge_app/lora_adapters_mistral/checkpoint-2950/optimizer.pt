PK                      optimizer/data.pklFB ZZZZZZZZZZZZ�}q (X   stateq}qX   param_groupsq]q(}q(X   weight_decayqG        X   lrqG>�axEA+X   betasqG?�������G?�����+�q	X   epsq
G>Ey��0�:X   amsgradq�X   maximizeq�X   foreachq
NX
   capturableq�X   differentiableq�X   fusedqNX   decoupled_weight_decayq�X
   initial_lrqG?*6��C-X   paramsq]qu}q(hG        hG>�axEA+hG?�������G?�����+�qh
G>Ey��0�:h�h�h
Nh�h�hNh�hG?*6��C-h]queu.PK 7.�  �  PK                      optimizer/.format_versionFB ZZZZZZZZZZZZZZZZZZ1PK��܃      PK                     5 optimizer/.storage_alignmentFB1 ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ64PK?wq�      PK                     = optimizer/byteorderFB9 ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZlittlePK�=�      PK                     ; optimizer/versionFB7 ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ3
PKўgU      PK                      0 optimizer/.data/serialization_idFB, ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ0290846867776292310400052664738387986688PK� �;(   (   PK           7.�  �                   optimizer/data.pklPK          ��܃                   �  optimizer/.format_versionPK          ?wq�                   Q  optimizer/.storage_alignmentPK          �=�                   �  optimizer/byteorderPK          ўgU                   V  optimizer/versionPK          � �;(   (                 �  optimizer/.data/serialization_idPK,       -                       �      x      PK             PK      �  x    