{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 2850.0, "eval_steps": 500, "global_step": 2850, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 1.0, "grad_norm": 0.0, "learning_rate": 0.0, "loss": 3.5297, "step": 1}, {"epoch": 10.0, "grad_norm": 0.0, "learning_rate": 1.2e-05, "loss": 3.5297, "step": 10}, {"epoch": 20.0, "grad_norm": 0.0, "learning_rate": 2.5333333333333337e-05, "loss": 3.5297, "step": 20}, {"epoch": 30.0, "grad_norm": 0.0, "learning_rate": 3.866666666666667e-05, "loss": 3.5297, "step": 30}, {"epoch": 40.0, "grad_norm": 0.0, "learning_rate": 5.2000000000000004e-05, "loss": 3.5297, "step": 40}, {"epoch": 50.0, "grad_norm": 0.0, "learning_rate": 6.533333333333334e-05, "loss": 3.5297, "step": 50}, {"epoch": 60.0, "grad_norm": 0.0, "learning_rate": 7.866666666666666e-05, "loss": 3.5297, "step": 60}, {"epoch": 70.0, "grad_norm": 0.0, "learning_rate": 9.200000000000001e-05, "loss": 3.5297, "step": 70}, {"epoch": 80.0, "grad_norm": 0.0, "learning_rate": 0.00010533333333333332, "loss": 3.5297, "step": 80}, {"epoch": 90.0, "grad_norm": 0.0, "learning_rate": 0.00011866666666666669, "loss": 3.5297, "step": 90}, {"epoch": 100.0, "grad_norm": 0.0, "learning_rate": 0.000132, "loss": 3.5297, "step": 100}, {"epoch": 110.0, "grad_norm": 0.0, "learning_rate": 0.00014533333333333333, "loss": 3.5297, "step": 110}, {"epoch": 120.0, "grad_norm": 0.0, "learning_rate": 0.00015866666666666668, "loss": 3.5297, "step": 120}, {"epoch": 130.0, "grad_norm": 0.0, "learning_rate": 0.000172, "loss": 3.5297, "step": 130}, {"epoch": 140.0, "grad_norm": 0.0, "learning_rate": 0.00018533333333333333, "loss": 3.5297, "step": 140}, {"epoch": 150.0, "grad_norm": 0.0, "learning_rate": 0.00019866666666666668, "loss": 3.5297, "step": 150}, {"epoch": 160.0, "grad_norm": 0.0, "learning_rate": 0.00019999507890797408, "loss": 3.5297, "step": 160}, {"epoch": 170.0, "grad_norm": 0.0, "learning_rate": 0.00019997806834748456, "loss": 3.5297, "step": 170}, {"epoch": 180.0, "grad_norm": 0.0, "learning_rate": 0.00019994890963073947, "loss": 3.5297, "step": 180}, {"epoch": 190.0, "grad_norm": 0.0, "learning_rate": 0.00019990760630076237, "loss": 3.5297, "step": 190}, {"epoch": 200.0, "grad_norm": 0.0, "learning_rate": 0.000199854163376247, "loss": 3.5297, "step": 200}, {"epoch": 210.0, "grad_norm": 0.0, "learning_rate": 0.00019978858735094753, "loss": 3.5297, "step": 210}, {"epoch": 220.0, "grad_norm": 0.0, "learning_rate": 0.0001997108861928895, "loss": 3.5297, "step": 220}, {"epoch": 230.0, "grad_norm": 0.0, "learning_rate": 0.0001996210693434016, "loss": 3.5297, "step": 230}, {"epoch": 240.0, "grad_norm": 0.0, "learning_rate": 0.0001995191477159686, "loss": 3.5297, "step": 240}, {"epoch": 250.0, "grad_norm": 0.0, "learning_rate": 0.00019940513369490516, "loss": 3.5297, "step": 250}, {"epoch": 260.0, "grad_norm": 0.0, "learning_rate": 0.00019927904113385098, "loss": 3.5297, "step": 260}, {"epoch": 270.0, "grad_norm": 0.0, "learning_rate": 0.00019914088535408767, "loss": 3.5297, "step": 270}, {"epoch": 280.0, "grad_norm": 0.0, "learning_rate": 0.00019899068314267688, "loss": 3.5297, "step": 280}, {"epoch": 290.0, "grad_norm": 0.0, "learning_rate": 0.0001988284527504207, "loss": 3.5297, "step": 290}, {"epoch": 300.0, "grad_norm": 0.0, "learning_rate": 0.00019865421388964383, "loss": 3.5297, "step": 300}, {"epoch": 310.0, "grad_norm": 0.0, "learning_rate": 0.00019846798773179866, "loss": 3.5297, "step": 310}, {"epoch": 320.0, "grad_norm": 0.0, "learning_rate": 0.00019826979690489252, "loss": 3.5297, "step": 320}, {"epoch": 330.0, "grad_norm": 0.0, "learning_rate": 0.00019805966549073825, "loss": 3.5297, "step": 330}, {"epoch": 340.0, "grad_norm": 0.0, "learning_rate": 0.00019783761902202813, "loss": 3.5297, "step": 340}, {"epoch": 350.0, "grad_norm": 0.0, "learning_rate": 0.00019760368447923146, "loss": 3.5297, "step": 350}, {"epoch": 360.0, "grad_norm": 0.0, "learning_rate": 0.00019735789028731604, "loss": 3.5297, "step": 360}, {"epoch": 370.0, "grad_norm": 0.0, "learning_rate": 0.0001971002663122945, "loss": 3.5297, "step": 370}, {"epoch": 380.0, "grad_norm": 0.0, "learning_rate": 0.00019683084385759523, "loss": 3.5297, "step": 380}, {"epoch": 390.0, "grad_norm": 0.0, "learning_rate": 0.0001965496556602588, "loss": 3.5297, "step": 390}, {"epoch": 400.0, "grad_norm": 0.0, "learning_rate": 0.00019625673588696008, "loss": 3.5297, "step": 400}, {"epoch": 410.0, "grad_norm": 0.0, "learning_rate": 0.00019595212012985682, "loss": 3.5297, "step": 410}, {"epoch": 420.0, "grad_norm": 0.0, "learning_rate": 0.00019563584540226481, "loss": 3.5297, "step": 420}, {"epoch": 430.0, "grad_norm": 0.0, "learning_rate": 0.00019530795013416046, "loss": 3.5297, "step": 430}, {"epoch": 440.0, "grad_norm": 0.0, "learning_rate": 0.00019496847416751125, "loss": 3.5297, "step": 440}, {"epoch": 450.0, "grad_norm": 0.0, "learning_rate": 0.00019461745875143477, "loss": 3.5297, "step": 450}, {"epoch": 460.0, "grad_norm": 0.0, "learning_rate": 0.0001942549465371863, "loss": 3.5297, "step": 460}, {"epoch": 470.0, "grad_norm": 0.0, "learning_rate": 0.0001938809815729766, "loss": 3.5297, "step": 470}, {"epoch": 480.0, "grad_norm": 0.0, "learning_rate": 0.00019349560929861958, "loss": 3.5297, "step": 480}, {"epoch": 490.0, "grad_norm": 0.0, "learning_rate": 0.00019309887654001096, "loss": 3.5297, "step": 490}, {"epoch": 500.0, "grad_norm": 0.0, "learning_rate": 0.00019269083150343859, "loss": 3.5297, "step": 500}, {"epoch": 510.0, "grad_norm": 0.0, "learning_rate": 0.00019227152376972506, "loss": 3.5297, "step": 510}, {"epoch": 520.0, "grad_norm": 0.0, "learning_rate": 0.000191841004288203, "loss": 3.5297, "step": 520}, {"epoch": 530.0, "grad_norm": 0.0, "learning_rate": 0.00019139932537052463, "loss": 3.5297, "step": 530}, {"epoch": 540.0, "grad_norm": 0.0, "learning_rate": 0.0001909465406843052, "loss": 3.5297, "step": 540}, {"epoch": 550.0, "grad_norm": 0.0, "learning_rate": 0.00019048270524660196, "loss": 3.5297, "step": 550}, {"epoch": 560.0, "grad_norm": 0.0, "learning_rate": 0.0001900078754172294, "loss": 3.5297, "step": 560}, {"epoch": 570.0, "grad_norm": 0.0, "learning_rate": 0.00018952210889191067, "loss": 3.5297, "step": 570}, {"epoch": 580.0, "grad_norm": 0.0, "learning_rate": 0.00018902546469526743, "loss": 3.5297, "step": 580}, {"epoch": 590.0, "grad_norm": 0.0, "learning_rate": 0.0001885180031736477, "loss": 3.5297, "step": 590}, {"epoch": 600.0, "grad_norm": 0.0, "learning_rate": 0.00018799978598779322, "loss": 3.5297, "step": 600}, {"epoch": 610.0, "grad_norm": 0.0, "learning_rate": 0.00018747087610534736, "loss": 3.5297, "step": 610}, {"epoch": 620.0, "grad_norm": 0.0, "learning_rate": 0.00018693133779320385, "loss": 3.5297, "step": 620}, {"epoch": 630.0, "grad_norm": 0.0, "learning_rate": 0.00018638123660969796, "loss": 3.5297, "step": 630}, {"epoch": 640.0, "grad_norm": 0.0, "learning_rate": 0.0001858206393966405, "loss": 3.5297, "step": 640}, {"epoch": 650.0, "grad_norm": 0.0, "learning_rate": 0.00018524961427119615, "loss": 3.5297, "step": 650}, {"epoch": 660.0, "grad_norm": 0.0, "learning_rate": 0.00018466823061760653, "loss": 3.5297, "step": 660}, {"epoch": 670.0, "grad_norm": 0.0, "learning_rate": 0.0001840765590787594, "loss": 3.5297, "step": 670}, {"epoch": 680.0, "grad_norm": 0.0, "learning_rate": 0.00018347467154760516, "loss": 3.5297, "step": 680}, {"epoch": 690.0, "grad_norm": 0.0, "learning_rate": 0.00018286264115842117, "loss": 3.5297, "step": 690}, {"epoch": 700.0, "grad_norm": 0.0, "learning_rate": 0.00018224054227792524, "loss": 3.5297, "step": 700}, {"epoch": 710.0, "grad_norm": 0.0, "learning_rate": 0.00018160845049623964, "loss": 3.5297, "step": 710}, {"epoch": 720.0, "grad_norm": 0.0, "learning_rate": 0.0001809664426177061, "loss": 3.5297, "step": 720}, {"epoch": 730.0, "grad_norm": 0.0, "learning_rate": 0.00018031459665155363, "loss": 3.5297, "step": 730}, {"epoch": 740.0, "grad_norm": 0.0, "learning_rate": 0.00017965299180241963, "loss": 3.5297, "step": 740}, {"epoch": 750.0, "grad_norm": 0.0, "learning_rate": 0.00017898170846072592, "loss": 3.5297, "step": 750}, {"epoch": 760.0, "grad_norm": 0.0, "learning_rate": 0.0001783008281929106, "loss": 3.5297, "step": 760}, {"epoch": 770.0, "grad_norm": 0.0, "learning_rate": 0.00017761043373151715, "loss": 3.5297, "step": 770}, {"epoch": 780.0, "grad_norm": 0.0, "learning_rate": 0.0001769106089651417, "loss": 3.5297, "step": 780}, {"epoch": 790.0, "grad_norm": 0.0, "learning_rate": 0.00017620143892823977, "loss": 3.5297, "step": 790}, {"epoch": 800.0, "grad_norm": 0.0, "learning_rate": 0.00017548300979079414, "loss": 3.5297, "step": 800}, {"epoch": 810.0, "grad_norm": 0.0, "learning_rate": 0.00017475540884784424, "loss": 3.5297, "step": 810}, {"epoch": 820.0, "grad_norm": 0.0, "learning_rate": 0.00017401872450887917, "loss": 3.5297, "step": 820}, {"epoch": 830.0, "grad_norm": 0.0, "learning_rate": 0.0001732730462870953, "loss": 3.5297, "step": 830}, {"epoch": 840.0, "grad_norm": 0.0, "learning_rate": 0.00017251846478851955, "loss": 3.5297, "step": 840}, {"epoch": 850.0, "grad_norm": 0.0, "learning_rate": 0.0001717550717010001, "loss": 3.5297, "step": 850}, {"epoch": 860.0, "grad_norm": 0.0, "learning_rate": 0.00017098295978306552, "loss": 3.5297, "step": 860}, {"epoch": 870.0, "grad_norm": 0.0, "learning_rate": 0.00017020222285265397, "loss": 3.5297, "step": 870}, {"epoch": 880.0, "grad_norm": 0.0, "learning_rate": 0.0001694129557757133, "loss": 3.5297, "step": 880}, {"epoch": 890.0, "grad_norm": 0.0, "learning_rate": 0.0001686152544546743, "loss": 3.5297, "step": 890}, {"epoch": 900.0, "grad_norm": 0.0, "learning_rate": 0.00016780921581679764, "loss": 3.5297, "step": 900}, {"epoch": 910.0, "grad_norm": 0.0, "learning_rate": 0.0001669949378023965, "loss": 3.5297, "step": 910}, {"epoch": 920.0, "grad_norm": 0.0, "learning_rate": 0.0001661725193529359, "loss": 3.5297, "step": 920}, {"epoch": 930.0, "grad_norm": 0.0, "learning_rate": 0.00016534206039901057, "loss": 3.5297, "step": 930}, {"epoch": 940.0, "grad_norm": 0.0, "learning_rate": 0.00016450366184820255, "loss": 3.5297, "step": 940}, {"epoch": 950.0, "grad_norm": 0.0, "learning_rate": 0.00016365742557282017, "loss": 3.5297, "step": 950}, {"epoch": 960.0, "grad_norm": 0.0, "learning_rate": 0.00016280345439751958, "loss": 3.5297, "step": 960}, {"epoch": 970.0, "grad_norm": 0.0, "learning_rate": 0.00016194185208681083, "loss": 3.5297, "step": 970}, {"epoch": 980.0, "grad_norm": 0.0, "learning_rate": 0.00016107272333244953, "loss": 3.5297, "step": 980}, {"epoch": 990.0, "grad_norm": 0.0, "learning_rate": 0.00016019617374071597, "loss": 3.5297, "step": 990}, {"epoch": 1000.0, "grad_norm": 0.0, "learning_rate": 0.00015931230981958326, "loss": 3.5297, "step": 1000}, {"epoch": 1010.0, "grad_norm": 0.0, "learning_rate": 0.00015842123896577545, "loss": 3.5297, "step": 1010}, {"epoch": 1020.0, "grad_norm": 0.0, "learning_rate": 0.0001575230694517182, "loss": 3.5297, "step": 1020}, {"epoch": 1030.0, "grad_norm": 0.0, "learning_rate": 0.00015661791041238256, "loss": 3.5297, "step": 1030}, {"epoch": 1040.0, "grad_norm": 0.0, "learning_rate": 0.00015570587183202434, "loss": 3.5297, "step": 1040}, {"epoch": 1050.0, "grad_norm": 0.0, "learning_rate": 0.00015478706453082017, "loss": 3.5297, "step": 1050}, {"epoch": 1060.0, "grad_norm": 0.0, "learning_rate": 0.00015386160015140168, "loss": 3.5297, "step": 1060}, {"epoch": 1070.0, "grad_norm": 0.0, "learning_rate": 0.00015292959114529027, "loss": 3.5297, "step": 1070}, {"epoch": 1080.0, "grad_norm": 0.0, "learning_rate": 0.00015199115075923325, "loss": 3.5297, "step": 1080}, {"epoch": 1090.0, "grad_norm": 0.0, "learning_rate": 0.00015104639302144327, "loss": 3.5297, "step": 1090}, {"epoch": 1100.0, "grad_norm": 0.0, "learning_rate": 0.00015009543272774325, "loss": 3.5297, "step": 1100}, {"epoch": 1110.0, "grad_norm": 0.0, "learning_rate": 0.0001491383854276175, "loss": 3.5297, "step": 1110}, {"epoch": 1120.0, "grad_norm": 0.0, "learning_rate": 0.00014817536741017152, "loss": 3.5297, "step": 1120}, {"epoch": 1130.0, "grad_norm": 0.0, "learning_rate": 0.0001472064956900022, "loss": 3.5297, "step": 1130}, {"epoch": 1140.0, "grad_norm": 0.0, "learning_rate": 0.00014623188799297915, "loss": 3.5297, "step": 1140}, {"epoch": 1150.0, "grad_norm": 0.0, "learning_rate": 0.00014525166274194037, "loss": 3.5297, "step": 1150}, {"epoch": 1160.0, "grad_norm": 0.0, "learning_rate": 0.00014426593904230288, "loss": 3.5297, "step": 1160}, {"epoch": 1170.0, "grad_norm": 0.0, "learning_rate": 0.0001432748366675902, "loss": 3.5297, "step": 1170}, {"epoch": 1180.0, "grad_norm": 0.0, "learning_rate": 0.00014227847604487913, "loss": 3.5297, "step": 1180}, {"epoch": 1190.0, "grad_norm": 0.0, "learning_rate": 0.0001412769782401668, "loss": 3.5297, "step": 1190}, {"epoch": 1200.0, "grad_norm": 0.0, "learning_rate": 0.00014027046494366, "loss": 3.5297, "step": 1200}, {"epoch": 1210.0, "grad_norm": 0.0, "learning_rate": 0.00013925905845498914, "loss": 3.5297, "step": 1210}, {"epoch": 1220.0, "grad_norm": 0.0, "learning_rate": 0.00013824288166834752, "loss": 3.5297, "step": 1220}, {"epoch": 1230.0, "grad_norm": 0.0, "learning_rate": 0.00013722205805755892, "loss": 3.5297, "step": 1230}, {"epoch": 1240.0, "grad_norm": 0.0, "learning_rate": 0.0001361967116610743, "loss": 3.5297, "step": 1240}, {"epoch": 1250.0, "grad_norm": 0.0, "learning_rate": 0.0001351669670669003, "loss": 3.5297, "step": 1250}, {"epoch": 1260.0, "grad_norm": 0.0, "learning_rate": 0.00013413294939746063, "loss": 3.5297, "step": 1260}, {"epoch": 1270.0, "grad_norm": 0.0, "learning_rate": 0.00013309478429439283, "loss": 3.5297, "step": 1270}, {"epoch": 1280.0, "grad_norm": 0.0, "learning_rate": 0.00013205259790328162, "loss": 3.5297, "step": 1280}, {"epoch": 1290.0, "grad_norm": 0.0, "learning_rate": 0.00013100651685833117, "loss": 3.5297, "step": 1290}, {"epoch": 1300.0, "grad_norm": 0.0, "learning_rate": 0.00012995666826697819, "loss": 3.5297, "step": 1300}, {"epoch": 1310.0, "grad_norm": 0.0, "learning_rate": 0.00012890317969444716, "loss": 3.5297, "step": 1310}, {"epoch": 1320.0, "grad_norm": 0.0, "learning_rate": 0.0001278461791482502, "loss": 3.5297, "step": 1320}, {"epoch": 1330.0, "grad_norm": 0.0, "learning_rate": 0.00012678579506263297, "loss": 3.5297, "step": 1330}, {"epoch": 1340.0, "grad_norm": 0.0, "learning_rate": 0.00012572215628296908, "loss": 3.5297, "step": 1340}, {"epoch": 1350.0, "grad_norm": 0.0, "learning_rate": 0.00012465539205010407, "loss": 3.5297, "step": 1350}, {"epoch": 1360.0, "grad_norm": 0.0, "learning_rate": 0.00012358563198465182, "loss": 3.5297, "step": 1360}, {"epoch": 1370.0, "grad_norm": 0.0, "learning_rate": 0.00012251300607124443, "loss": 3.5297, "step": 1370}, {"epoch": 1380.0, "grad_norm": 0.0, "learning_rate": 0.00012143764464273802, "loss": 3.5297, "step": 1380}, {"epoch": 1390.0, "grad_norm": 0.0, "learning_rate": 0.00012035967836437625, "loss": 3.5297, "step": 1390}, {"epoch": 1400.0, "grad_norm": 0.0, "learning_rate": 0.00011927923821791352, "loss": 3.5297, "step": 1400}, {"epoch": 1410.0, "grad_norm": 0.0, "learning_rate": 0.00011819645548569941, "loss": 3.5297, "step": 1410}, {"epoch": 1420.0, "grad_norm": 0.0, "learning_rate": 0.000117111461734727, "loss": 3.5297, "step": 1420}, {"epoch": 1430.0, "grad_norm": 0.0, "learning_rate": 0.00011602438880064623, "loss": 3.5297, "step": 1430}, {"epoch": 1440.0, "grad_norm": 0.0, "learning_rate": 0.0001149353687717449, "loss": 3.5297, "step": 1440}, {"epoch": 1450.0, "grad_norm": 0.0, "learning_rate": 0.00011384453397289876, "loss": 3.5297, "step": 1450}, {"epoch": 1460.0, "grad_norm": 0.0, "learning_rate": 0.00011275201694949313, "loss": 3.5297, "step": 1460}, {"epoch": 1470.0, "grad_norm": 0.0, "learning_rate": 0.00011165795045131733, "loss": 3.5297, "step": 1470}, {"epoch": 1480.0, "grad_norm": 0.0, "learning_rate": 0.0001105624674164346, "loss": 3.5297, "step": 1480}, {"epoch": 1490.0, "grad_norm": 0.0, "learning_rate": 0.00010946570095502902, "loss": 3.5297, "step": 1490}, {"epoch": 1500.0, "grad_norm": 0.0, "learning_rate": 0.00010836778433323158, "loss": 3.5297, "step": 1500}, {"epoch": 1510.0, "grad_norm": 0.0, "learning_rate": 0.00010726885095692712, "loss": 3.5297, "step": 1510}, {"epoch": 1520.0, "grad_norm": 0.0, "learning_rate": 0.00010616903435554457, "loss": 3.5297, "step": 1520}, {"epoch": 1530.0, "grad_norm": 0.0, "learning_rate": 0.00010506846816583195, "loss": 3.5297, "step": 1530}, {"epoch": 1540.0, "grad_norm": 0.0, "learning_rate": 0.00010396728611561844, "loss": 3.5297, "step": 1540}, {"epoch": 1550.0, "grad_norm": 0.0, "learning_rate": 0.00010286562200756522, "loss": 3.5297, "step": 1550}, {"epoch": 1560.0, "grad_norm": 0.0, "learning_rate": 0.00010176360970290755, "loss": 3.5297, "step": 1560}, {"epoch": 1570.0, "grad_norm": 0.0, "learning_rate": 0.00010066138310518942, "loss": 3.5297, "step": 1570}, {"epoch": 1580.0, "grad_norm": 0.0, "learning_rate": 9.95590761439932e-05, "loss": 3.5297, "step": 1580}, {"epoch": 1590.0, "grad_norm": 0.0, "learning_rate": 9.845682275866603e-05, "loss": 3.5297, "step": 1590}, {"epoch": 1600.0, "grad_norm": 0.0, "learning_rate": 9.735475688204521e-05, "loss": 3.5297, "step": 1600}, {"epoch": 1610.0, "grad_norm": 0.0, "learning_rate": 9.625301242418417e-05, "loss": 3.5297, "step": 1610}, {"epoch": 1620.0, "grad_norm": 0.0, "learning_rate": 9.515172325608127e-05, "loss": 3.5297, "step": 1620}, {"epoch": 1630.0, "grad_norm": 0.0, "learning_rate": 9.405102319341344e-05, "loss": 3.5297, "step": 1630}, {"epoch": 1640.0, "grad_norm": 0.0, "learning_rate": 9.295104598027656e-05, "loss": 3.5297, "step": 1640}, {"epoch": 1650.0, "grad_norm": 0.0, "learning_rate": 9.185192527293425e-05, "loss": 3.5297, "step": 1650}, {"epoch": 1660.0, "grad_norm": 0.0, "learning_rate": 9.075379462357766e-05, "loss": 3.5297, "step": 1660}, {"epoch": 1670.0, "grad_norm": 0.0, "learning_rate": 8.965678746409782e-05, "loss": 3.5297, "step": 1670}, {"epoch": 1680.0, "grad_norm": 0.0, "learning_rate": 8.856103708987245e-05, "loss": 3.5297, "step": 1680}, {"epoch": 1690.0, "grad_norm": 0.0, "learning_rate": 8.746667664356956e-05, "loss": 3.5297, "step": 1690}, {"epoch": 1700.0, "grad_norm": 0.0, "learning_rate": 8.637383909896957e-05, "loss": 3.5297, "step": 1700}, {"epoch": 1710.0, "grad_norm": 0.0, "learning_rate": 8.528265724480775e-05, "loss": 3.5297, "step": 1710}, {"epoch": 1720.0, "grad_norm": 0.0, "learning_rate": 8.419326366863938e-05, "loss": 3.5297, "step": 1720}, {"epoch": 1730.0, "grad_norm": 0.0, "learning_rate": 8.310579074072932e-05, "loss": 3.5297, "step": 1730}, {"epoch": 1740.0, "grad_norm": 0.0, "learning_rate": 8.202037059796797e-05, "loss": 3.5297, "step": 1740}, {"epoch": 1750.0, "grad_norm": 0.0, "learning_rate": 8.093713512781534e-05, "loss": 3.5297, "step": 1750}, {"epoch": 1760.0, "grad_norm": 0.0, "learning_rate": 7.985621595227581e-05, "loss": 3.5297, "step": 1760}, {"epoch": 1770.0, "grad_norm": 0.0, "learning_rate": 7.877774441190505e-05, "loss": 3.5297, "step": 1770}, {"epoch": 1780.0, "grad_norm": 0.0, "learning_rate": 7.770185154985085e-05, "loss": 3.5297, "step": 1780}, {"epoch": 1790.0, "grad_norm": 0.0, "learning_rate": 7.662866809593042e-05, "loss": 3.5297, "step": 1790}, {"epoch": 1800.0, "grad_norm": 0.0, "learning_rate": 7.55583244507457e-05, "loss": 3.5297, "step": 1800}, {"epoch": 1810.0, "grad_norm": 0.0, "learning_rate": 7.449095066983849e-05, "loss": 3.5297, "step": 1810}, {"epoch": 1820.0, "grad_norm": 0.0, "learning_rate": 7.342667644788773e-05, "loss": 3.5297, "step": 1820}, {"epoch": 1830.0, "grad_norm": 0.0, "learning_rate": 7.236563110295045e-05, "loss": 3.5297, "step": 1830}, {"epoch": 1840.0, "grad_norm": 0.0, "learning_rate": 7.130794356074859e-05, "loss": 3.5297, "step": 1840}, {"epoch": 1850.0, "grad_norm": 0.0, "learning_rate": 7.025374233900364e-05, "loss": 3.5297, "step": 1850}, {"epoch": 1860.0, "grad_norm": 0.0, "learning_rate": 6.920315553182035e-05, "loss": 3.5297, "step": 1860}, {"epoch": 1870.0, "grad_norm": 0.0, "learning_rate": 6.815631079412248e-05, "loss": 3.5297, "step": 1870}, {"epoch": 1880.0, "grad_norm": 0.0, "learning_rate": 6.711333532614168e-05, "loss": 3.5297, "step": 1880}, {"epoch": 1890.0, "grad_norm": 0.0, "learning_rate": 6.607435585796147e-05, "loss": 3.5297, "step": 1890}, {"epoch": 1900.0, "grad_norm": 0.0, "learning_rate": 6.503949863411865e-05, "loss": 3.5297, "step": 1900}, {"epoch": 1910.0, "grad_norm": 0.0, "learning_rate": 6.400888939826345e-05, "loss": 3.5297, "step": 1910}, {"epoch": 1920.0, "grad_norm": 0.0, "learning_rate": 6.298265337788069e-05, "loss": 3.5297, "step": 1920}, {"epoch": 1930.0, "grad_norm": 0.0, "learning_rate": 6.196091526907355e-05, "loss": 3.5297, "step": 1930}, {"epoch": 1940.0, "grad_norm": 0.0, "learning_rate": 6.0943799221412076e-05, "loss": 3.5297, "step": 1940}, {"epoch": 1950.0, "grad_norm": 0.0, "learning_rate": 5.9931428822847944e-05, "loss": 3.5297, "step": 1950}, {"epoch": 1960.0, "grad_norm": 0.0, "learning_rate": 5.8923927084697475e-05, "loss": 3.5297, "step": 1960}, {"epoch": 1970.0, "grad_norm": 0.0, "learning_rate": 5.792141642669473e-05, "loss": 3.5297, "step": 1970}, {"epoch": 1980.0, "grad_norm": 0.0, "learning_rate": 5.692401866211658e-05, "loss": 3.5297, "step": 1980}, {"epoch": 1990.0, "grad_norm": 0.0, "learning_rate": 5.593185498298141e-05, "loss": 3.5297, "step": 1990}, {"epoch": 2000.0, "grad_norm": 0.0, "learning_rate": 5.494504594532324e-05, "loss": 3.5297, "step": 2000}, {"epoch": 2010.0, "grad_norm": 0.0, "learning_rate": 5.396371145454302e-05, "loss": 3.5297, "step": 2010}, {"epoch": 2020.0, "grad_norm": 0.0, "learning_rate": 5.2987970750839555e-05, "loss": 3.5297, "step": 2020}, {"epoch": 2030.0, "grad_norm": 0.0, "learning_rate": 5.201794239472035e-05, "loss": 3.5297, "step": 2030}, {"epoch": 2040.0, "grad_norm": 0.0, "learning_rate": 5.105374425259598e-05, "loss": 3.5297, "step": 2040}, {"epoch": 2050.0, "grad_norm": 0.0, "learning_rate": 5.0095493482457955e-05, "loss": 3.5297, "step": 2050}, {"epoch": 2060.0, "grad_norm": 0.0, "learning_rate": 4.914330651964339e-05, "loss": 3.5297, "step": 2060}, {"epoch": 2070.0, "grad_norm": 0.0, "learning_rate": 4.8197299062686995e-05, "loss": 3.5297, "step": 2070}, {"epoch": 2080.0, "grad_norm": 0.0, "learning_rate": 4.72575860592627e-05, "loss": 3.5297, "step": 2080}, {"epoch": 2090.0, "grad_norm": 0.0, "learning_rate": 4.632428169221673e-05, "loss": 3.5297, "step": 2090}, {"epoch": 2100.0, "grad_norm": 0.0, "learning_rate": 4.5397499365693374e-05, "loss": 3.5297, "step": 2100}, {"epoch": 2110.0, "grad_norm": 0.0, "learning_rate": 4.447735169135533e-05, "loss": 3.5297, "step": 2110}, {"epoch": 2120.0, "grad_norm": 0.0, "learning_rate": 4.356395047470073e-05, "loss": 3.5297, "step": 2120}, {"epoch": 2130.0, "grad_norm": 0.0, "learning_rate": 4.265740670147753e-05, "loss": 3.5297, "step": 2130}, {"epoch": 2140.0, "grad_norm": 0.0, "learning_rate": 4.1757830524198195e-05, "loss": 3.5297, "step": 2140}, {"epoch": 2150.0, "grad_norm": 0.0, "learning_rate": 4.086533124875487e-05, "loss": 3.5297, "step": 2150}, {"epoch": 2160.0, "grad_norm": 0.0, "learning_rate": 3.998001732113816e-05, "loss": 3.5297, "step": 2160}, {"epoch": 2170.0, "grad_norm": 0.0, "learning_rate": 3.910199631425989e-05, "loss": 3.5297, "step": 2170}, {"epoch": 2180.0, "grad_norm": 0.0, "learning_rate": 3.8231374914882045e-05, "loss": 3.5297, "step": 2180}, {"epoch": 2190.0, "grad_norm": 0.0, "learning_rate": 3.736825891065362e-05, "loss": 3.5297, "step": 2190}, {"epoch": 2200.0, "grad_norm": 0.0, "learning_rate": 3.6512753177256476e-05, "loss": 3.5297, "step": 2200}, {"epoch": 2210.0, "grad_norm": 0.0, "learning_rate": 3.5664961665661975e-05, "loss": 3.5297, "step": 2210}, {"epoch": 2220.0, "grad_norm": 0.0, "learning_rate": 3.482498738950035e-05, "loss": 3.5297, "step": 2220}, {"epoch": 2230.0, "grad_norm": 0.0, "learning_rate": 3.399293241254336e-05, "loss": 3.5297, "step": 2230}, {"epoch": 2240.0, "grad_norm": 0.0, "learning_rate": 3.316889783630304e-05, "loss": 3.5297, "step": 2240}, {"epoch": 2250.0, "grad_norm": 0.0, "learning_rate": 3.235298378774674e-05, "loss": 3.5297, "step": 2250}, {"epoch": 2260.0, "grad_norm": 0.0, "learning_rate": 3.154528940713113e-05, "loss": 3.5297, "step": 2260}, {"epoch": 2270.0, "grad_norm": 0.0, "learning_rate": 3.07459128359557e-05, "loss": 3.5297, "step": 2270}, {"epoch": 2280.0, "grad_norm": 0.0, "learning_rate": 2.995495120503775e-05, "loss": 3.5297, "step": 2280}, {"epoch": 2290.0, "grad_norm": 0.0, "learning_rate": 2.9172500622710263e-05, "loss": 3.5297, "step": 2290}, {"epoch": 2300.0, "grad_norm": 0.0, "learning_rate": 2.8398656163144e-05, "loss": 3.5297, "step": 2300}, {"epoch": 2310.0, "grad_norm": 0.0, "learning_rate": 2.7633511854795004e-05, "loss": 3.5297, "step": 2310}, {"epoch": 2320.0, "grad_norm": 0.0, "learning_rate": 2.687716066897964e-05, "loss": 3.5297, "step": 2320}, {"epoch": 2330.0, "grad_norm": 0.0, "learning_rate": 2.6129694508577594e-05, "loss": 3.5297, "step": 2330}, {"epoch": 2340.0, "grad_norm": 0.0, "learning_rate": 2.5391204196865005e-05, "loss": 3.5297, "step": 2340}, {"epoch": 2350.0, "grad_norm": 0.0, "learning_rate": 2.466177946647874e-05, "loss": 3.5297, "step": 2350}, {"epoch": 2360.0, "grad_norm": 0.0, "learning_rate": 2.3941508948513125e-05, "loss": 3.5297, "step": 2360}, {"epoch": 2370.0, "grad_norm": 0.0, "learning_rate": 2.32304801617504e-05, "loss": 3.5297, "step": 2370}, {"epoch": 2380.0, "grad_norm": 0.0, "learning_rate": 2.2528779502026652e-05, "loss": 3.5297, "step": 2380}, {"epoch": 2390.0, "grad_norm": 0.0, "learning_rate": 2.1836492231733928e-05, "loss": 3.5297, "step": 2390}, {"epoch": 2400.0, "grad_norm": 0.0, "learning_rate": 2.115370246946019e-05, "loss": 3.5297, "step": 2400}, {"epoch": 2410.0, "grad_norm": 0.0, "learning_rate": 2.048049317976809e-05, "loss": 3.5297, "step": 2410}, {"epoch": 2420.0, "grad_norm": 0.0, "learning_rate": 1.9816946163114303e-05, "loss": 3.5297, "step": 2420}, {"epoch": 2430.0, "grad_norm": 0.0, "learning_rate": 1.9163142045909976e-05, "loss": 3.5297, "step": 2430}, {"epoch": 2440.0, "grad_norm": 0.0, "learning_rate": 1.8519160270723857e-05, "loss": 3.5297, "step": 2440}, {"epoch": 2450.0, "grad_norm": 0.0, "learning_rate": 1.78850790866296e-05, "loss": 3.5297, "step": 2450}, {"epoch": 2460.0, "grad_norm": 0.0, "learning_rate": 1.7260975539697753e-05, "loss": 3.5297, "step": 2460}, {"epoch": 2470.0, "grad_norm": 0.0, "learning_rate": 1.6646925463633922e-05, "loss": 3.5297, "step": 2470}, {"epoch": 2480.0, "grad_norm": 0.0, "learning_rate": 1.6043003470564533e-05, "loss": 3.5297, "step": 2480}, {"epoch": 2490.0, "grad_norm": 0.0, "learning_rate": 1.5449282941970757e-05, "loss": 3.5297, "step": 2490}, {"epoch": 2500.0, "grad_norm": 0.0, "learning_rate": 1.4865836019771995e-05, "loss": 3.5297, "step": 2500}, {"epoch": 2510.0, "grad_norm": 0.0, "learning_rate": 1.4292733597560192e-05, "loss": 3.5297, "step": 2510}, {"epoch": 2520.0, "grad_norm": 0.0, "learning_rate": 1.3730045311985596e-05, "loss": 3.5297, "step": 2520}, {"epoch": 2530.0, "grad_norm": 0.0, "learning_rate": 1.3177839534295277e-05, "loss": 3.5297, "step": 2530}, {"epoch": 2540.0, "grad_norm": 0.0, "learning_rate": 1.2636183362025544e-05, "loss": 3.5297, "step": 2540}, {"epoch": 2550.0, "grad_norm": 0.0, "learning_rate": 1.2105142610849062e-05, "loss": 3.5297, "step": 2550}, {"epoch": 2560.0, "grad_norm": 0.0, "learning_rate": 1.158478180657766e-05, "loss": 3.5297, "step": 2560}, {"epoch": 2570.0, "grad_norm": 0.0, "learning_rate": 1.1075164177321874e-05, "loss": 3.5297, "step": 2570}, {"epoch": 2580.0, "grad_norm": 0.0, "learning_rate": 1.057635164580828e-05, "loss": 3.5297, "step": 2580}, {"epoch": 2590.0, "grad_norm": 0.0, "learning_rate": 1.0088404821855412e-05, "loss": 3.5297, "step": 2590}, {"epoch": 2600.0, "grad_norm": 0.0, "learning_rate": 9.61138299500901e-06, "loss": 3.5297, "step": 2600}, {"epoch": 2610.0, "grad_norm": 0.0, "learning_rate": 9.14534412733804e-06, "loss": 3.5297, "step": 2610}, {"epoch": 2620.0, "grad_norm": 0.0, "learning_rate": 8.690344846391729e-06, "loss": 3.5297, "step": 2620}, {"epoch": 2630.0, "grad_norm": 0.0, "learning_rate": 8.246440438318836e-06, "loss": 3.5297, "step": 2630}, {"epoch": 2640.0, "grad_norm": 0.0, "learning_rate": 7.81368484114996e-06, "loss": 3.5297, "step": 2640}, {"epoch": 2650.0, "grad_norm": 0.0, "learning_rate": 7.392130638243666e-06, "loss": 3.5297, "step": 2650}, {"epoch": 2660.0, "grad_norm": 0.0, "learning_rate": 6.981829051897149e-06, "loss": 3.5297, "step": 2660}, {"epoch": 2670.0, "grad_norm": 0.0, "learning_rate": 6.5828299371222145e-06, "loss": 3.5297, "step": 2670}, {"epoch": 2680.0, "grad_norm": 0.0, "learning_rate": 6.195181775587655e-06, "loss": 3.5297, "step": 2680}, {"epoch": 2690.0, "grad_norm": 0.0, "learning_rate": 5.818931669728189e-06, "loss": 3.5297, "step": 2690}, {"epoch": 2700.0, "grad_norm": 0.0, "learning_rate": 5.4541253370211656e-06, "loss": 3.5297, "step": 2700}, {"epoch": 2710.0, "grad_norm": 0.0, "learning_rate": 5.100807104431571e-06, "loss": 3.5297, "step": 2710}, {"epoch": 2720.0, "grad_norm": 0.0, "learning_rate": 4.759019903025874e-06, "loss": 3.5297, "step": 2720}, {"epoch": 2730.0, "grad_norm": 0.0, "learning_rate": 4.428805262755564e-06, "loss": 3.5297, "step": 2730}, {"epoch": 2740.0, "grad_norm": 0.0, "learning_rate": 4.1102033074108985e-06, "loss": 3.5297, "step": 2740}, {"epoch": 2750.0, "grad_norm": 0.0, "learning_rate": 3.803252749745623e-06, "loss": 3.5297, "step": 2750}, {"epoch": 2760.0, "grad_norm": 0.0, "learning_rate": 3.5079908867729717e-06, "loss": 3.5297, "step": 2760}, {"epoch": 2770.0, "grad_norm": 0.0, "learning_rate": 3.2244535952337562e-06, "loss": 3.5297, "step": 2770}, {"epoch": 2780.0, "grad_norm": 0.0, "learning_rate": 2.9526753272371198e-06, "loss": 3.5297, "step": 2780}, {"epoch": 2790.0, "grad_norm": 0.0, "learning_rate": 2.6926891060742976e-06, "loss": 3.5297, "step": 2790}, {"epoch": 2800.0, "grad_norm": 0.0, "learning_rate": 2.44452652220597e-06, "loss": 3.5297, "step": 2800}, {"epoch": 2810.0, "grad_norm": 0.0, "learning_rate": 2.208217729423823e-06, "loss": 3.5297, "step": 2810}, {"epoch": 2820.0, "grad_norm": 0.0, "learning_rate": 1.9837914411866e-06, "loss": 3.5297, "step": 2820}, {"epoch": 2830.0, "grad_norm": 0.0, "learning_rate": 1.771274927131139e-06, "loss": 3.5297, "step": 2830}, {"epoch": 2840.0, "grad_norm": 0.0, "learning_rate": 1.5706940097589329e-06, "loss": 3.5297, "step": 2840}, {"epoch": 2850.0, "grad_norm": 0.0, "learning_rate": 1.3820730612984833e-06, "loss": 3.5297, "step": 2850}], "logging_steps": 10, "max_steps": 3000, "num_input_tokens_seen": 0, "num_train_epochs": 3000, "save_steps": 50, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 1.643069297664e+16, "train_batch_size": 4, "trial_name": null, "trial_params": null}