{"alpha_pattern": {}, "auto_mapping": null, "base_model_name_or_path": "mistralai/Mistral-7B-Instruct-v0.2", "bias": "none", "corda_config": null, "eva_config": null, "exclude_modules": null, "fan_in_fan_out": false, "inference_mode": true, "init_lora_weights": true, "layer_replication": null, "layers_pattern": null, "layers_to_transform": null, "loftq_config": {}, "lora_alpha": 32, "lora_bias": false, "lora_dropout": 0.05, "megatron_config": null, "megatron_core": "megatron.core", "modules_to_save": null, "peft_type": "LORA", "r": 16, "rank_pattern": {}, "revision": null, "target_modules": ["v_proj", "q_proj"], "task_type": "CAUSAL_LM", "trainable_token_indices": null, "use_dora": false, "use_rslora": false}