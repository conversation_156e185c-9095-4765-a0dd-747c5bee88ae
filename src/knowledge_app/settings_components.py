from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class SettingsGroup(QGroupBox):
    """Custom settings group with better styling"""
    def __init__(self, title: str, parent=None):
        super().__init__(title, parent)
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #ffffff;
                border: 2px solid #3a3a3a;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 15px;
                background-color: #2b2b2b;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #4a9eff;
            }
        """
        )
        self.layout = QVBoxLayout(self)
        self.layout.setSpacing(10)
        self.layout.setContentsMargins(15, 20, 15, 15)

class SettingsRow(QWidget):
    """Individual setting row with label and control"""
    def __init__(self, label: str, control: QWidget, description: str = ""):
        super().__init__()
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(0, 5, 0, 5)
        self.label = QLabel(label)
        self.label.setStyleSheet("color: #ffffff; font-size: 12px;")
        self.label.setMinimumWidth(150)
        control.setStyleSheet("""
            QComboBox, QSpinBox, QLineEdit, QPushButton {
                background-color: #3a3a3a;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 5px;
                color: #ffffff;
                min-height: 25px;
            }
            QComboBox:hover, QSpinBox:hover, QLineEdit:hover, QPushButton:hover {
                border-color: #4a9eff;
            }
        """)
        self.layout.addWidget(self.label)
        self.layout.addWidget(control)
        self.layout.addStretch()
        if description:
            self.setToolTip(description)

class AIModelManagementSection(SettingsGroup):
    """AI Model settings with actual controls"""
    def __init__(self, model_manager, parent=None):
        super().__init__("AI Model Management", parent)
        self.model_manager = model_manager
        self._setup_ui()
    def _setup_ui(self):
        model_combo = QComboBox()
        model_combo.addItems(["GPT-2", "Mistral-7B", "Llama-2", "Gemma"])
        model_combo.setCurrentText(getattr(self.model_manager, 'current_model', "GPT-2"))
        model_combo.currentTextChanged.connect(self._on_model_changed)
        model_row = SettingsRow(
            "Current Model:", 
            model_combo,
            "Select the AI model for question generation"
        )
        self.layout.addWidget(model_row)
        gpu_memory = QSpinBox()
        gpu_memory.setRange(1, 32)
        gpu_memory.setValue(8)
        gpu_memory.setSuffix(" GB")
        memory_row = SettingsRow(
            "GPU Memory Limit:", 
            gpu_memory,
            "Maximum GPU memory to use for model loading"
        )
        self.layout.addWidget(memory_row)
        model_controls = QWidget()
        model_controls_layout = QHBoxLayout(model_controls)
        model_controls_layout.setContentsMargins(0, 0, 0, 0)
        download_btn = QPushButton("Download Model")
        download_btn.clicked.connect(self._download_model)
        delete_btn = QPushButton("Delete Model")
        delete_btn.clicked.connect(self._delete_model)
        delete_btn.setStyleSheet("QPushButton { background-color: #8b0000; }")
        model_controls_layout.addWidget(download_btn)
        model_controls_layout.addWidget(delete_btn)
        model_controls_layout.addStretch()
        control_row = SettingsRow("Model Actions:", model_controls)
        self.layout.addWidget(control_row)
        self.status_label = QLabel("Model Status: Ready")
        self.status_label.setStyleSheet("color: #90EE90; font-size: 11px;")
        self.layout.addWidget(self.status_label)
    def _on_model_changed(self, model_name: str):
        if hasattr(self.model_manager, 'switch_model'):
            self.model_manager.switch_model(model_name)
        self.status_label.setText(f"Model Status: Switched to {model_name}")
    def _download_model(self):
        self.status_label.setText("Downloading model...")
        self.status_label.setStyleSheet("color: #FFD700;")
    def _delete_model(self):
        reply = QMessageBox.question(self, "Delete Model", 
                                   "Are you sure you want to delete this model?")
        if reply == QMessageBox.Yes:
            self.status_label.setText("Model deleted")

class SettingsPage(QWidget):
    """Main settings page with all sections"""
    def __init__(self, app_config, model_manager, parent=None):
        super().__init__(parent)
        self.app_config = app_config
        self.model_manager = model_manager
        self._setup_ui()
    def _setup_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: #1e1e1e;
            }
            QScrollBar:vertical {
                background-color: #2b2b2b;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #4a9eff;
                border-radius: 6px;
            }
        """)
        content = QWidget()
        content_layout = QVBoxLayout(content)
        content_layout.setSpacing(15)
        content_layout.addWidget(AIModelManagementSection(self.model_manager))
        content_layout.addWidget(self._create_storage_section())
        content_layout.addWidget(self._create_display_section())
        content_layout.addWidget(self._create_performance_section())
        content_layout.addStretch()
        scroll.setWidget(content)
        main_layout.addWidget(scroll)
    def _create_storage_section(self):
        section = SettingsGroup("Storage Settings")
        cache_widget = QWidget()
        cache_layout = QHBoxLayout(cache_widget)
        cache_layout.setContentsMargins(0, 0, 0, 0)
        cache_path = QLineEdit(str(getattr(self.app_config, 'image_cache_dir', '')))
        browse_btn = QPushButton("Browse")
        browse_btn.clicked.connect(self._browse_cache_dir)
        cache_layout.addWidget(cache_path)
        cache_layout.addWidget(browse_btn)
        cache_row = SettingsRow("Cache Directory:", cache_widget)
        section.layout.addWidget(cache_row)
        cache_size = QSpinBox()
        cache_size.setRange(100, 10000)
        cache_size.setValue(1000)
        cache_size.setSuffix(" MB")
        size_row = SettingsRow("Cache Size Limit:", cache_size)
        section.layout.addWidget(size_row)
        return section
    def _create_display_section(self):
        section = SettingsGroup("Display Settings")
        theme_combo = QComboBox()
        theme_combo.addItems(["Dark", "Light", "Auto"])
        theme_combo.setCurrentText("Dark")
        theme_row = SettingsRow("Theme:", theme_combo)
        section.layout.addWidget(theme_row)
        font_size = QSpinBox()
        font_size.setRange(8, 24)
        font_size.setValue(12)
        font_size.setSuffix(" pt")
        font_row = SettingsRow("Font Size:", font_size)
        section.layout.addWidget(font_row)
        return section
    def _create_performance_section(self):
        section = SettingsGroup("Performance & Hardware Adaptation")
        cpu_threads = QSpinBox()
        cpu_threads.setRange(1, 16)
        cpu_threads.setValue(4)
        cpu_row = SettingsRow("CPU Threads:", cpu_threads)
        section.layout.addWidget(cpu_row)
        gpu_checkbox = QCheckBox("Enable GPU Acceleration")
        gpu_checkbox.setChecked(True)
        gpu_checkbox.setStyleSheet("QCheckBox { color: #ffffff; }")
        gpu_row = SettingsRow("Hardware:", gpu_checkbox)
        section.layout.addWidget(gpu_row)
        return section
    def _browse_cache_dir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "Select Cache Directory")
        if dir_path:
            pass
