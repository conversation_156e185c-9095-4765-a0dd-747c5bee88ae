#!/usr/bin/env python3
"""
Dependency installer for Knowledge App.

This script installs all required dependencies for the Knowledge App
one by one, with error handling and progress reporting.
"""

import os
import sys
import subprocess
import time
from datetime import datetime

print(f"DEBUG: Starting install_dependencies.py. sys.argv: {sys.argv}")
print(f"DEBUG: Current working directory: {os.getcwd()}")

def log(message, level="INFO"):
    """Log a message with timestamp and level."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def install_package(package):
    """
    Install a single package using pip.
    
    Args:
        package: The package specification (name>=version)
        
    Returns:
        bool: True if installation was successful, False otherwise
    """
    log(f"Installing {package}...")
    
    try:
        # Run pip install with the package
        process = subprocess.run(
            [sys.executable, "-m", "pip", "install", package],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        log(f"Successfully installed {package}")
        return True
        
    except subprocess.CalledProcessError as e:
        log(f"Failed to install {package}: {e.stderr}", "ERROR")
        return False
    except Exception as e:
        log(f"Unexpected error installing {package}: {str(e)}", "ERROR")
        return False

def parse_requirements(file_path):
    """
    Parse requirements.txt file into a list of package specifications.
    
    Args:
        file_path: Path to requirements.txt
        
    Returns:
        list: List of package specifications
    """
    packages = []
    
    try:
        with open(file_path, 'r') as f:
            for line in f:
                # Skip empty lines, comments, and section headers
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                packages.append(line)
        
        return packages
    except Exception as e:
        log(f"Error parsing requirements file: {str(e)}", "ERROR")
        return []

def main():
    """Main function to install all dependencies."""
    log("Entered main() for dependency installation")
    script_dir = os.path.dirname(os.path.abspath(__file__))
    log(f"script_dir = {script_dir}")
    requirements_path = os.path.join(script_dir, "requirements.txt")
    log(f"requirements_path = {requirements_path}")
    if os.path.exists(requirements_path):
        log(f"requirements.txt exists at {requirements_path}")
    else:
        log(f"ERROR: requirements.txt does not exist at {requirements_path}", "ERROR")
        return 1
    
    # Parse requirements
    packages = parse_requirements(requirements_path)
    
    if not packages:
        log("No packages found to install", "ERROR")
        return 1
    
    log(f"Found {len(packages)} packages to install")
    
    # Install each package
    successful = 0
    failed = 0
    
    for i, package in enumerate(packages, 1):
        log(f"[{i}/{len(packages)}] Processing {package}")
        
        if install_package(package):
            successful += 1
        else:
            failed += 1
        
        # Small delay between installations to avoid overwhelming the system
        if i < len(packages):
            time.sleep(1)
    
    # Summary
    log(f"Installation complete. {successful} packages installed successfully, {failed} failed.")
    
    if failed > 0:
        log("Some packages failed to install. Check the log for details.", "WARNING")
        return 1
    else:
        log("All packages installed successfully!")
        return 0

if __name__ == "__main__":
    sys.exit(main())