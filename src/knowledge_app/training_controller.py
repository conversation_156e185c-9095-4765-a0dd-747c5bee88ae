from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox, QMessageBox
from PyQt5.QtCore import Qt, pyqtSignal
import torch

class TrainingController(QWidget):
    training_started = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        self.layout = QVBoxLayout()
        
        # Domain selection
        self.domain_combo = QComboBox()
        self.domain_combo.addItems(["Physics", "Chemistry", "Mathematics", "Biology"])
        
        # Hardware info
        hardware_info = self._get_hardware_info()
        hardware_label = QLabel(hardware_info)
        hardware_label.setWordWrap(True)
        
        # Start button
        self.start_btn = QPushButton("Start Training")
        self.start_btn.clicked.connect(self.start_training)
        
        # Add widgets to layout
        self.layout.addWidget(QLabel("Select Domain:"))
        self.layout.addWidget(self.domain_combo)
        self.layout.addWidget(hardware_label)
        self.layout.addWidget(self.start_btn)
        
        self.setLayout(self.layout)

    def _get_hardware_info(self):
        """Get available hardware information"""
        try:
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                return f"Hardware: {gpu_name} ({gpu_memory:.1f}GB VRAM)\nThe app will automatically use GPU acceleration for optimal performance."
            else:
                return "Hardware: CPU Only\nThe app will optimize training for your CPU."
        except:
            return "Hardware detection failed. The app will use default settings."

    def start_training(self):
        """Start the training process with automatic hardware optimization"""
        try:
            # Get selected domain
            domain = self.domain_combo.currentText()
            
            # Automatically configure hardware settings
            config = self._get_optimized_config()
            
            # Emit training configuration
            self.training_started.emit({
                'domain': domain,
                'config': config
            })
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "Training Error",
                f"Failed to start training: {str(e)}"
            )

    def _get_optimized_config(self):
        """Get optimized training configuration based on available hardware"""
        config = {
            'device': 'cpu',
            'batch_size': 1,
            'precision': 'float32'
        }
        
        try:
            if torch.cuda.is_available():
                config['device'] = 'cuda'
                
                # Get GPU memory and capabilities
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                compute_capability = torch.cuda.get_device_capability(0)[0]
                
                # Optimize batch size based on available memory
                if gpu_memory >= 16:
                    config['batch_size'] = 8
                elif gpu_memory >= 8:
                    config['batch_size'] = 4
                elif gpu_memory >= 4:
                    config['batch_size'] = 2
                
                # Use mixed precision if supported
                if compute_capability >= 7:
                    config['precision'] = 'float16'
        except:
            pass  # Use default CPU settings if GPU detection fails
        
        return config 