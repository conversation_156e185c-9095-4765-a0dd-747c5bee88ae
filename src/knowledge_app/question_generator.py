"""
Question generator module for Knowledge App.

This module is responsible for generating quiz questions based on 
provided knowledge content.
"""

# CRITICAL MEMORY FIX: Import only lightweight modules during startup
import random
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

# CRITICAL MEMORY FIX: Heavy ML imports will be done lazily when question generation is first used

logger = logging.getLogger(__name__)

def auto_train_from_books(book_paths: List[str], output_dir: str, config: Optional[Dict[str, Any]] = None) -> bool:
    """
    Automatically train the question generator on provided books.
    
    Args:
        book_paths: List of paths to book files
        output_dir: Directory to save trained model
        config: Optional configuration dictionary
        
    Returns:
        bool: True if training was successful
    """
    try:
        logger.info(f"Starting automated training on {len(book_paths)} books")
        
        # For now, just return success
        # In a real implementation, this would:
        # 1. Load and preprocess the books
        # 2. Fine-tune a language model
        # 3. Save the model
        return True
        
    except Exception as e:
        logger.error(f"Error during automated training: {e}")
        return False

class QuestionGenerator:
    """Generates quiz questions from knowledge content."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.question_types = ['multiple_choice', 'true_false', 'short_answer']
        
    def generate_questions(self, content: str, num_questions: int = 5) -> List[Dict[str, Any]]:
        """
        Generate questions from the provided content.
        
        Args:
            content: The text content to generate questions from
            num_questions: Number of questions to generate
            
        Returns:
            List of question dictionaries
        """
        questions = []
        
        try:
            # For now, generate simple placeholder questions
            for i in range(num_questions):
                question_type = random.choice(self.question_types)
                
                if question_type == 'multiple_choice':
                    questions.append(self._generate_multiple_choice())
                elif question_type == 'true_false':
                    questions.append(self._generate_true_false())
                else:
                    questions.append(self._generate_short_answer())
                    
            logger.info(f"Generated {len(questions)} questions")
            return questions
            
        except Exception as e:
            logger.error(f"Error generating questions: {e}")
            return []
            
    def _generate_multiple_choice(self) -> Dict[str, Any]:
        """Generate a multiple choice question."""
        return {
            'type': 'multiple_choice',
            'question': 'What is the capital of France?',
            'options': ['Paris', 'London', 'Berlin', 'Madrid'],
            'correct_answer': 'A',  # Changed to use letter
            'explanation': 'Paris is the capital and largest city of France.'
        }
        
    def _generate_true_false(self) -> Dict[str, Any]:
        """Generate a true/false question."""
        return {
            'type': 'true_false',
            'question': 'The Earth is flat.',
            'options': ['True', 'False'],
            'correct_answer': 'B',  # Changed to use letter
            'explanation': 'The Earth is approximately spherical in shape.'
        }
        
    def _generate_short_answer(self) -> Dict[str, Any]:
        """Generate a short answer question."""
        return {
            'type': 'short_answer',
            'question': 'What is the chemical symbol for water?',
            'options': ['H2O', 'CO2', 'NaCl', 'O2'],
            'correct_answer': 'A',  # Changed to use letter
            'explanation': 'Water molecules consist of two hydrogen atoms and one oxygen atom.'
        }
        
    def validate_question(self, question: Dict[str, Any]) -> bool:
        """
        Validate a question dictionary.
        
        Args:
            question: Question dictionary to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        required_fields = ['type', 'question', 'options', 'correct_answer', 'explanation']
        
        try:
            # Check required fields
            if not all(field in question for field in required_fields):
                return False
                
            # Validate based on question type
            if question['type'] == 'multiple_choice':
                if not isinstance(question['options'], list):
                    return False
                if len(question['options']) < 2:
                    return False
                if not question['correct_answer'] in ['A', 'B', 'C', 'D']:
                    return False
                    
            elif question['type'] == 'true_false':
                if not isinstance(question['options'], list):
                    return False
                if len(question['options']) != 2:
                    return False
                if not question['correct_answer'] in ['A', 'B']:
                    return False
                    
            return True
            
        except Exception as e:
            logger.error(f"Error validating question: {e}")
            return False 