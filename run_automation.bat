@echo off
echo 🔥 FIRE AI Training - GUI Automation Debugger
echo ===============================================
echo.
echo This will automatically test your training workflow:
echo 1. Launch the app
echo 2. Click "Train AI Model"
echo 3. Select Mistral-7B model
echo 4. Monitor for errors and provide debugging info
echo.
echo Press Ctrl+C to stop at any time.
echo.
pause

echo Starting automation...
python run_gui_automation.py

echo.
echo Automation completed. Check the automation_screenshots folder for:
echo - Screenshots of each step
echo - Error reports (if any)
echo - Detailed logs
echo.
pause
