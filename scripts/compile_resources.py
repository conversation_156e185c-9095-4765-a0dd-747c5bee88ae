#!/usr/bin/env python3
"""
Script to compile Qt resources into a Python module
"""

import os
import subprocess
import sys
from pathlib import Path

def compile_resources():
    """Compile Qt resources into a Python module"""
    try:
        # Get the project root directory
        project_root = Path(__file__).parent.parent.absolute()
        resources_dir = project_root / "src" / "knowledge_app" / "resources"
        output_dir = resources_dir
        
        # Create output directory if it doesn't exist
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Resource file path
        qrc_file = resources_dir / "resources.qrc"
        py_file = output_dir / "resources_rc.py"
        
        if not qrc_file.exists():
            print(f"Error: Resource file not found at {qrc_file}")
            return False
            
        # Try PyQt5 tools in order of preference
        rcc_tools = [
            "pyrcc5",  # PyQt5 resource compiler
            "pyqt5-tools rcc",  # PyQt5-tools package
            "rcc",  # Qt resource compiler if in PATH
        ]
        
        success = False
        for rcc_tool in rcc_tools:
            try:
                print(f"Trying to compile with {rcc_tool}...")
                cmd = f"{rcc_tool} -o {str(py_file)} {str(qrc_file)}"
                result = subprocess.run(
                    cmd,
                    shell=True,
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    success = True
                    break
                    
            except Exception as e:
                print(f"Failed with {rcc_tool}: {e}")
                continue
        
        if not success:
            print("Error: Failed to compile resources with any available tool")
            return False
            
        print("Resources compiled successfully!")
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    success = compile_resources()
    sys.exit(0 if success else 1) 