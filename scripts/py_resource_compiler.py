#!/usr/bin/env python3
"""
Python-based resource compiler for PyQt5
"""

import os
import sys
import base64
from pathlib import Path
from typing import Dict, List

def generate_resource_module(qrc_file: Path, output_file: Path) -> bool:
    """
    Generate a Python module from a Qt resource file
    """
    try:
        # Parse QRC file
        import xml.etree.ElementTree as ET
        tree = ET.parse(qrc_file)
        root = tree.getroot()
        
        resources: Dict[str, List[Dict]] = {}
        
        # Process each resource
        for qresource in root.findall('qresource'):
            prefix = qresource.get('prefix', '')
            if prefix not in resources:
                resources[prefix] = []
                
            # Process each file in the resource
            for file_elem in qresource.findall('file'):
                file_path = file_elem.text
                if not file_path:
                    continue
                    
                # Get the full path to the file
                full_path = qrc_file.parent / file_path
                if not full_path.exists():
                    print(f"Warning: File {full_path} not found")
                    continue
                    
                # Read and encode the file
                with open(full_path, 'rb') as f:
                    file_data = f.read()
                    encoded_data = base64.b64encode(file_data).decode('utf-8')
                    
                resources[prefix].append({
                    'alias': file_elem.get('alias', file_path),
                    'path': file_path,
                    'data': encoded_data
                })
        
        # Generate Python module
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('"""\nAutomatically generated resource module\n"""\n\n')
            f.write('import base64\nfrom PyQt5.QtCore import QByteArray\n\n')
            
            # Write resource data
            f.write('def get_resource(path: str) -> QByteArray:\n')
            f.write('    """Get resource data by path"""\n')
            f.write('    resource_map = {\n')
            
            for prefix, files in resources.items():
                for file_info in files:
                    full_path = f"{prefix}/{file_info['path']}"
                    f.write(f'        "{full_path}": """{file_info["data"]}""",\n')
                    
            f.write('    }\n\n')
            f.write('    if path not in resource_map:\n')
            f.write('        return QByteArray()\n')
            f.write('    return QByteArray(base64.b64decode(resource_map[path]))\n')
            
        print(f"Resource module generated successfully: {output_file}")
        return True
        
    except Exception as e:
        print(f"Error generating resource module: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python py_resource_compiler.py input.qrc output.py")
        sys.exit(1)
        
    qrc_file = Path(sys.argv[1])
    output_file = Path(sys.argv[2])
    
    if not qrc_file.exists():
        print(f"Error: QRC file {qrc_file} not found")
        sys.exit(1)
        
    success = generate_resource_module(qrc_file, output_file)
    sys.exit(0 if success else 1) 