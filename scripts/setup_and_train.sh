#!/bin/bash

# --- Configuration ---
PYTHON_VERSION="3.11.9"
PYTHON_INSTALL_DIR="$HOME/python3.11"
VENV_DIR="$HOME/llm-env-py311"
PROJECT_DIR="/mnt/c/shared folder/knowledge_app" # Adjust if your project path is different in WSL

# --- Error Handling and Logging ---
# Log all output to a file
LOG_FILE="$PROJECT_DIR/setup_and_train.log"
exec > >(tee -a "$LOG_FILE") 2>&1

trap 'echo "❌ ERROR: Script failed at line $LINENO. Check $LOG_FILE for details."' ERR
set -e # Exit immediately if a command exits with a non-zero status
set -x # Print each command before executing

echo "--- Starting Automated Setup and Training Script ---"
echo "Log file: $LOG_FILE"
date

# --- Step 1: Install System Dependencies ---
echo "--- Installing system build tools and libraries ---"
# This part is specifically for Debian/Ubuntu based systems like WSL
# If running on pure Git Bash/Windows, these commands will likely fail or are not needed
if command -v apt-get &> /dev/null; then
  sudo apt update || { echo "Failed to update apt. Continuing, but some dependencies might be missing."; }
  sudo apt install -y wget build-essential libssl-dev zlib1g-dev \
    libncurses5-dev libncursesw5-dev libreadline-dev libsqlite3-dev \
    libgdbm-dev libdb5.3-dev libbz2-dev libexpat1-dev liblzma-dev tk-dev \
    libffi-dev uuid-dev pkg-config python3-dev python3-pip python3-venv || { echo "Failed to install core system dependencies. Please check your apt sources and network."; }
else
  echo "Skipping apt-get system dependency installation (Not a Debian/Ubuntu system)."
fi

# --- Step 2: Install Rust ---
echo "--- Installing Rust (if not already installed) ---"
if ! command -v rustc &> /dev/null; then
  echo "Rust not found, installing..."
  curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
  source $HOME/.cargo/env
  echo "Rust installed."
else
  echo "Rust is already installed."
fi
# Ensure cargo env is sourced for current script execution
source $HOME/.cargo/env

# --- Step 3: Download and build Python ${PYTHON_VERSION} if not present ---
echo "--- Checking for and building Python ${PYTHON_VERSION} (if not present) ---"
if ! command -v python3.11 &> /dev/null; then
  echo "Python 3.11 not found, building from source..."
  PYTHON_TGZ="Python-${PYTHON_VERSION}.tgz"
  cd /tmp
  wget "https://www.python.org/ftp/python/${PYTHON_VERSION}/${PYTHON_TGZ}" || { echo "Failed to download Python source. Check URL and network."; exit 1; }
  tar -xf "${PYTHON_TGZ}" || { echo "Failed to extract Python source."; exit 1; }
  cd "Python-${PYTHON_VERSION}"
  ./configure --enable-optimizations --prefix="$PYTHON_INSTALL_DIR" || { echo "Failed to configure Python build."; exit 1; }
  make -j$(nproc) || { echo "Failed to build Python."; exit 1; }
  make install || { echo "Failed to install Python."; exit 1; }
  echo "Python ${PYTHON_VERSION} built and installed to $PYTHON_INSTALL_DIR."
else
  echo "Python 3.11 is already installed."
fi
# Ensure the custom Python install directory is in PATH for subsequent commands
export PATH="$PYTHON_INSTALL_DIR/bin:$PATH"
echo "Updated PATH: $PATH"

# --- Step 4: Create and activate the venv ---
echo "--- Creating and activating virtual environment at $VENV_DIR ---"
# Use the specific python3.11 command from the installed location
if [ ! -d "$VENV_DIR" ]; then
  echo "Creating venv..."
  python3.11 -m venv "$VENV_DIR" || { echo "Failed to create virtual environment."; exit 1; }
  echo "Virtual environment created."
else
  echo "Virtual environment already exists."
fi

echo "Activating virtual environment..."
source "$VENV_DIR/bin/activate" || { echo "Failed to activate virtual environment."; exit 1; }
echo "Virtual environment activated: $VIRTUAL_ENV"

# --- Step 5: Upgrade pip, setuptools, wheel ---
echo "--- Upgrading pip, setuptools, wheel ---"
pip install --upgrade pip setuptools wheel || { echo "Failed to upgrade core pip packages."; }

# --- Step 6: Navigate to project directory ---
echo "--- Navigating to project directory: $PROJECT_DIR ---"
cd "$PROJECT_DIR" || { echo "Failed to change directory to $PROJECT_DIR. Please check the path."; exit 1; }
echo "Current directory: $(pwd)"

# --- Step 7: Install project requirements ---
echo "--- Installing/Upgrading project requirements from requirements.txt ---"
if [ -f requirements.txt ]; then
  # Install requirements, allowing downgrades if necessary to resolve conflicts
  pip install --upgrade --no-deps -r requirements.txt || { echo "Failed to install requirements from requirements.txt (initial pass)."; }
  echo "Installing dependencies with dependency resolution."
  pip install --upgrade -r requirements.txt || { echo "Failed to install requirements from requirements.txt (full pass)."; exit 1; }
else
  echo "❌ Error: requirements.txt not found in the project directory!"
  exit 1
fi

# --- Step 8: Ensure compatible transformers and peft versions ---
echo "--- Ensuring compatible transformers and peft versions ---"
# We need to check the installed versions and potentially adjust if conflicts arise
# Based on previous errors, we know transformers=4.30.2 and peft=0.15.2 had issues.
# Let's try to ensure a known working combination if the above step didn't fix it.
# A safer approach might be to explicitly set the versions if required.
# For now, relying on requirements.txt and upgrade should be the primary method.
# If specific versions are needed, uncomment and adjust the lines below:
# pip install "transformers==4.40.0" || { echo "Failed to install specific transformers version."; exit 1; }
# pip install "peft==0.15.2" || { echo "Failed to install specific peft version."; exit 1; }
echo "Checking installed versions..."
pip show transformers peft || { echo "Could not show installed versions of transformers or peft."; }

# --- Step 9: Install local package in editable mode ---
echo "--- Installing local package in editable mode ---"
pip install -e . || { echo "Failed to install local package in editable mode. Check setup.py and src directory."; exit 1; }

# --- Step 10: Verify Python and venv ---
echo "--- Verification ---"
echo "Python version being used:"
python --version
echo "Active virtual environment:"
echo "$VIRTUAL_ENV"

# --- Step 11: Run the training script ---
echo "--- Running the training script (test_train.py) ---"
TRAIN_SCRIPT="test_train.py"
if [ -f "$TRAIN_SCRIPT" ]; then
  echo "Executing: python $TRAIN_SCRIPT"
  python "$TRAIN_SCRIPT"
  echo "--- Training script finished ---"
else
  echo "Skipping training script run: $TRAIN_SCRIPT not found in $(pwd)."
fi

echo "--- Script finished successfully ---"
date

# The script will exit here due to set -e if any command failed after trap ERR.
# If it reaches here, everything before was successful. 