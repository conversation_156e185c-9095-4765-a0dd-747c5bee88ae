#!/bin/bash
# Activate venv or create if missing
if [ ! -d "$HOME/llm-env" ]; then
  python3 -m venv $HOME/llm-env
fi
source $HOME/llm-env/bin/activate

# Install dependencies if needed
pip install --upgrade pip
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
pip install transformers accelerate peft bitsandbytes

# Run the training script
cd "/mnt/c/shared folder/knowledge_app"
python3 test_train.py 