#!/usr/bin/env python3
"""
Setup script to generate icons and compile resources
"""

import os
import sys
from pathlib import Path

def setup_resources():
    """Set up all application resources"""
    try:
        # Get project paths
        project_root = Path(__file__).parent.parent.absolute()
        resources_dir = project_root / "src" / "knowledge_app" / "resources"
        icons_dir = resources_dir / "icons"
        
        # Create directories if they don't exist
        icons_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate placeholder icons
        print("Generating placeholder icons...")
        sys.path.insert(0, str(resources_dir))
        from icons.placeholder import generate_icons
        generate_icons()
        print("Icons generated successfully!")
        
        # Compile resources using our Python-based compiler
        print("Compiling resources...")
        from py_resource_compiler import generate_resource_module
        
        qrc_file = resources_dir / "resources.qrc"
        output_file = resources_dir / "resources_rc.py"
        
        if not generate_resource_module(qrc_file, output_file):
            print("Failed to compile resources")
            return False
            
        print("Resources setup completed successfully!")
        return True
        
    except Exception as e:
        print(f"Error setting up resources: {e}")
        return False

if __name__ == "__main__":
    success = setup_resources()
    sys.exit(0 if success else 1) 