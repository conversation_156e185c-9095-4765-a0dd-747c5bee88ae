#!/usr/bin/env python3
"""
Test script to verify Qt graphics effect fixes.

This script tests the RippleButton implementation to ensure it handles
rapid hover events without crashing due to Qt object lifecycle issues.
"""

import sys
import time
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Q<PERSON>imer, QThread, pyqtSignal
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

# Add the src directory to the path
sys.path.insert(0, 'src')

from knowledge_app.ui.seductive_main_menu import RippleButton
from knowledge_app.utils.qt_safe_access import is_qt_object_valid

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class TestWindow(QMainWindow):
    """Test window for verifying RippleButton fixes"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Qt Graphics Effect Fix Test")
        self.setGeometry(100, 100, 400, 300)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Add title
        title = QLabel("Qt Graphics Effect Fix Test")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Add test buttons
        self.test_button1 = RippleButton("Test Button 1")
        self.test_button2 = RippleButton("Test Button 2")
        self.test_button3 = RippleButton("Test Button 3")
        
        layout.addWidget(self.test_button1)
        layout.addWidget(self.test_button2)
        layout.addWidget(self.test_button3)
        
        # Add status label
        self.status_label = QLabel("Ready for testing...")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        # Test counters
        self.hover_count = 0
        self.error_count = 0
        
        # Connect button clicks
        self.test_button1.clicked.connect(lambda: self.on_button_click("Button 1"))
        self.test_button2.clicked.connect(lambda: self.on_button_click("Button 2"))
        self.test_button3.clicked.connect(lambda: self.on_button_click("Button 3"))
        
        # Start automated testing
        self.start_automated_test()
    
    def on_button_click(self, button_name):
        """Handle button click"""
        logger.info(f"{button_name} clicked")
        self.status_label.setText(f"{button_name} clicked - Hover count: {self.hover_count}")
    
    def start_automated_test(self):
        """Start automated hover testing"""
        self.status_label.setText("Starting automated hover test...")
        
        # Create timer for rapid hover simulation
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self.simulate_hover_events)
        self.test_timer.start(100)  # Every 100ms
        
        # Stop test after 10 seconds
        QTimer.singleShot(10000, self.stop_automated_test)
    
    def simulate_hover_events(self):
        """Simulate rapid hover events"""
        try:
            buttons = [self.test_button1, self.test_button2, self.test_button3]
            
            for button in buttons:
                if is_qt_object_valid(button):
                    # Simulate enter and leave events
                    QTest.mouseMove(button)
                    QTest.mouseMove(self)  # Move away
                    
                    self.hover_count += 1
                    
                    # Check if button's Qt objects are still valid
                    shadow_effect = button.qt_manager.get_object("shadow_effect", auto_recreate=False)
                    if shadow_effect and not is_qt_object_valid(shadow_effect):
                        logger.warning(f"Shadow effect became invalid for {button.text()}")
                        self.error_count += 1
                
        except Exception as e:
            logger.error(f"Error during hover simulation: {e}")
            self.error_count += 1
        
        # Update status
        if self.hover_count % 10 == 0:
            self.status_label.setText(f"Hover events: {self.hover_count}, Errors: {self.error_count}")
    
    def stop_automated_test(self):
        """Stop automated testing"""
        if hasattr(self, 'test_timer'):
            self.test_timer.stop()
        
        # Final status
        if self.error_count == 0:
            self.status_label.setText(f"✅ Test PASSED! {self.hover_count} hover events, 0 errors")
            logger.info("Qt graphics effect fix test PASSED!")
        else:
            self.status_label.setText(f"❌ Test FAILED! {self.hover_count} hover events, {self.error_count} errors")
            logger.error("Qt graphics effect fix test FAILED!")
        
        # Clean up buttons
        for button in [self.test_button1, self.test_button2, self.test_button3]:
            try:
                button.cleanup()
            except Exception as e:
                logger.warning(f"Error during button cleanup: {e}")

def main():
    """Main test function"""
    app = QApplication(sys.argv)
    
    # Create test window
    window = TestWindow()
    window.show()
    
    # Run for a limited time
    QTimer.singleShot(15000, app.quit)  # Quit after 15 seconds
    
    # Start event loop
    try:
        exit_code = app.exec_()
        logger.info(f"Test completed with exit code: {exit_code}")
        return exit_code
    except Exception as e:
        logger.error(f"Test failed with exception: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
