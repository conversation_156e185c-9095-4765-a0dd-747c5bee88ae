
--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 47, in <module>
    from knowledge_app.app_health import setup_logging, check_dependencies
ModuleNotFoundError: No module named 'knowledge_app'

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 47, in <module>
    from knowledge_app.app_health import setup_logging, check_dependencies
ModuleNotFoundError: No module named 'knowledge_app'

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 1543, in <module>
    window = QuizApp(
        question_service=di.question_service,
        app_config=di.app_config,
        model_manager=di.model_manager
    )
  File "c:\shared folder\knowledge_app\main.py", line 261, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\main.py", line 266, in setup_ui
    from PyQt5.QtWidgets import QApplication, QThread, QWidget, QVBoxLayout, QLabel
ImportError: cannot import name 'QThread' from 'PyQt5.QtWidgets' (C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\PyQt5\QtWidgets.pyd)

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 1589, in <module>
    window = QuizApp(
        question_service=di.question_service,
        app_config=di.app_config,
        model_manager=di.model_manager
    )
  File "c:\shared folder\knowledge_app\main.py", line 261, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\main.py", line 368, in setup_ui
    self._settings_menu = SettingsMenu(self)
                          ~~~~~~~~~~~~^^^^^^
  File "c:\shared folder\knowledge_app\settings_menu.py", line 21, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\settings_menu.py", line 57, in init_ui
    self.update_styles()
    ~~~~~~~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\settings_menu.py", line 620, in update_styles
    self.update_storage_label()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\settings_menu.py", line 303, in update_storage_label
    self.storage_label.setText(f"Storage: {used:.2f} MB used / {limit:.0f} MB limit")
    ^^^^^^^^^^^^^^^^^^
AttributeError: 'SettingsMenu' object has no attribute 'storage_label'

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 1618, in <module>
    window = QuizApp(
        question_service=di.question_service,
        app_config=di.app_config,
        model_manager=di.model_manager
    )
  File "c:\shared folder\knowledge_app\main.py", line 261, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\main.py", line 361, in setup_ui
    self._main_menu = MainMenu(self)
                      ~~~~~~~~^^^^^^
  File "c:\shared folder\knowledge_app\main_menu.py", line 27, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\main_menu.py", line 65, in init_ui
    upload_images_btn.clicked.connect(self.parent.handle_add_images)
                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'QuizApp' object has no attribute 'handle_add_images'

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 1649, in <module>
    window = QuizApp(
        question_service=di.question_service,
        app_config=di.app_config,
        model_manager=di.model_manager
    )
  File "c:\shared folder\knowledge_app\main.py", line 261, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\main.py", line 368, in setup_ui
    self._settings_menu = SettingsMenu(self)
                          ~~~~~~~~~~~~^^^^^^
  File "c:\shared folder\knowledge_app\settings_menu.py", line 21, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\settings_menu.py", line 57, in init_ui
    self.update_styles()
    ~~~~~~~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\settings_menu.py", line 622, in update_styles
    self.update_storage_label()
    ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SettingsMenu' object has no attribute 'update_storage_label'

--------------------------------------------------
Traceback (most recent call last):
  File "/media/sf_shared_folder/knowledge_app/main.py", line 44, in <module>
    from core.secure_config import SecureConfigManager
  File "/media/sf_shared_folder/knowledge_app/core/secure_config.py", line 1, in <module>
    import keyring
ModuleNotFoundError: No module named 'keyring'

--------------------------------------------------
Traceback (most recent call last):
  File "/media/sf_shared_folder/knowledge_app/main.py", line 44, in <module>
    from core.secure_config import SecureConfigManager
  File "/media/sf_shared_folder/knowledge_app/core/secure_config.py", line 1, in <module>
    import keyring
ModuleNotFoundError: No module named 'keyring'

--------------------------------------------------
Traceback (most recent call last):
  File "/media/sf_shared_folder/knowledge_app/main.py", line 44, in <module>
    from core.secure_config import SecureConfigManager
  File "/media/sf_shared_folder/knowledge_app/core/secure_config.py", line 1, in <module>
    import keyring
ModuleNotFoundError: No module named 'keyring'

--------------------------------------------------
Traceback (most recent call last):
  File "/media/sf_shared_folder/knowledge_app/main.py", line 44, in <module>
    from core.secure_config import SecureConfigManager
  File "/media/sf_shared_folder/knowledge_app/core/secure_config.py", line 1, in <module>
    import keyring
ModuleNotFoundError: No module named 'keyring'

--------------------------------------------------
Traceback (most recent call last):
  File "/media/sf_shared_folder/knowledge_app/main.py", line 44, in <module>
    from core.secure_config import SecureConfigManager
  File "/media/sf_shared_folder/knowledge_app/core/secure_config.py", line 1, in <module>
    import keyring
ModuleNotFoundError: No module named 'keyring'

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 185, in <module>
    main_window = MainMenu()  # Change to your main window class if needed
TypeError: MainMenu.__init__() missing 1 required positional argument: 'parent'

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 185, in <module>
    main_window = MainMenu(parent=None)
  File "c:\shared folder\knowledge_app\main_menu.py", line 27, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\main_menu.py", line 65, in init_ui
    upload_images_btn.clicked.connect(self.parent.handle_add_images)
                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'handle_add_images'

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 185, in <module>
    main_window = MainMenu(parent=None)
  File "c:\shared folder\knowledge_app\main_menu.py", line 27, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\main_menu.py", line 74, in init_ui
    self.update_storage_info_label()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\main_menu.py", line 246, in update_storage_info_label
    limit = self.parent.storage_limit / (1024 * 1024)
            ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'storage_limit'

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 185, in <module>
    main_window = QuizApp(
        question_service=question_service,
        app_config=app_config,
        model_manager=model_manager
    )
  File "c:\shared folder\knowledge_app\quiz_app.py", line 24, in __init__
    from core.di_container import di_container
ImportError: cannot import name 'di_container' from 'core.di_container' (c:\shared folder\knowledge_app\core\di_container.py)

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 185, in <module>
    main_window = QuizApp(
        question_service=question_service,
        app_config=app_config,
        model_manager=model_manager
    )
  File "c:\shared folder\knowledge_app\quiz_app.py", line 45, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\quiz_app.py", line 105, in setup_ui
    if sys.platform.startswith('linux'):
       ^^^
NameError: name 'sys' is not defined. Did you forget to import 'sys'?

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 185, in <module>
    main_window = QuizApp(
        question_service=question_service,
        app_config=app_config,
        model_manager=model_manager
    )
  File "c:\shared folder\knowledge_app\quiz_app.py", line 46, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\quiz_app.py", line 134, in setup_ui
    self.settings_menu = SettingsMenu(self)
                         ~~~~~~~~~~~~^^^^^^
  File "c:\shared folder\knowledge_app\settings_menu.py", line 22, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\settings_menu.py", line 46, in init_ui
    main_content_layout.addWidget(self._create_training_books_section())  # NEW SECTION
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\settings_menu.py", line 152, in _create_training_books_section
    train_btn.clicked.connect(self.parent.start_ai_training_pipeline_dialog)
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'QuizApp' object has no attribute 'start_ai_training_pipeline_dialog'

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 185, in <module>
    main_window = QuizApp(
        question_service=question_service,
        app_config=app_config,
        model_manager=model_manager
    )
  File "c:\shared folder\knowledge_app\quiz_app.py", line 46, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\quiz_app.py", line 134, in setup_ui
    self.settings_menu = SettingsMenu(self)
                         ~~~~~~~~~~~~^^^^^^
  File "c:\shared folder\knowledge_app\settings_menu.py", line 22, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\settings_menu.py", line 46, in init_ui
    main_content_layout.addWidget(self._create_training_books_section())  # NEW SECTION
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\settings_menu.py", line 157, in _create_training_books_section
    upload_btn.clicked.connect(self.parent.handle_upload_books_dialog)
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'QuizApp' object has no attribute 'handle_upload_books_dialog'

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\settings_menu.py", line 1116, in run_model_loading_test
    QThreadPool.globalInstance().start(worker)
    ^^^^^^^^^^^
NameError: name 'QThreadPool' is not defined

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 185, in <module>
    main_window = QuizApp(
        question_service=question_service,
        app_config=app_config,
        model_manager=model_manager
    )
  File "c:\shared folder\knowledge_app\quiz_app.py", line 47, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\quiz_app.py", line 135, in setup_ui
    self.settings_menu = SettingsMenu(self)
                         ~~~~~~~~~~~~^^^^^^
  File "c:\shared folder\knowledge_app\settings_menu.py", line 22, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\settings_menu.py", line 57, in init_ui
    self.time_limit_spinner.setValue(self.parent.serious_mode_time_limit // 60)  # Convert seconds to minutes
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'QuizApp' object has no attribute 'serious_mode_time_limit'

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 185, in <module>
    main_window = QuizApp(
        question_service=question_service,
        app_config=app_config,
        model_manager=model_manager
    )
  File "c:\shared folder\knowledge_app\quiz_app.py", line 53, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\quiz_app.py", line 164, in setup_ui
    self.load_user_images()
    ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'QuizApp' object has no attribute 'load_user_images'

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 239, in <module>
    main_window = initialize_app(splash)
  File "c:\shared folder\knowledge_app\main.py", line 194, in initialize_app
    main_window = QuizApp(
        question_service=question_service,
        app_config=app_config,
        model_manager=model_manager
    )
  File "c:\shared folder\knowledge_app\quiz_app.py", line 59, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "c:\shared folder\knowledge_app\quiz_app.py", line 171, in setup_ui
    self.apply_theme_and_fonts()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'QuizApp' object has no attribute 'apply_theme_and_fonts'

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 175, in <module>
    from knowledge_app.quiz_app import QuizApp
  File "c:\shared folder\knowledge_app\quiz_app.py", line 131
    self.settings_file_path = os.path.join(self.base_path, "user_data", "settings.json")
    ^^^^
SyntaxError: expected 'except' or 'finally' block

--------------------------------------------------
Traceback (most recent call last):
  File "c:\shared folder\knowledge_app\main.py", line 175, in <module>
    from knowledge_app.quiz_app import QuizApp
  File "c:\shared folder\knowledge_app\quiz_app.py", line 248
    for screen in [self.main_menu, self.quiz_screen, self.settings_menu, self.quiz_setup_screen]:
    ^^^
SyntaxError: expected 'except' or 'finally' block
