#!/usr/bin/env python3
"""
Unified Test Runner - Enterprise Edition

This script replaces multiple test runners (run_tests.py, run_clean.py, run_rigorous_tests.py)
with a single, comprehensive pytest-based test runner that supports different test categories
and execution modes.

Features:
- Pytest-based execution with markers
- Multiple test categories (unit, integration, ui, slow, etc.)
- Flexible execution modes (fast, comprehensive, specific)
- Professional reporting and logging
- Environment validation
- Parallel execution support
"""

import sys
import os
import subprocess
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Optional
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UnifiedTestRunner:
    """Unified test runner with pytest integration"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.src_dir = self.project_root / "src"
        
        # Test categories and their markers
        self.test_categories = {
            'unit': 'unit',
            'integration': 'integration', 
            'ui': 'ui',
            'enterprise': 'enterprise',
            'mvc': 'mvc',
            'styling': 'styling',
            'ml': 'ml',
            'gpu': 'gpu',
            'slow': 'slow',
            'network': 'network'
        }
        
        # Predefined test suites
        self.test_suites = {
            'fast': ['unit', 'enterprise', 'mvc'],
            'comprehensive': ['unit', 'integration', 'enterprise', 'mvc', 'styling'],
            'ui': ['ui', 'styling'],
            'ml': ['ml', 'gpu'],
            'all': list(self.test_categories.keys())
        }
    
    def run_tests(self, 
                  suite: Optional[str] = None,
                  categories: Optional[List[str]] = None,
                  pattern: Optional[str] = None,
                  parallel: bool = False,
                  verbose: bool = True,
                  stop_on_fail: bool = False) -> bool:
        """
        Run tests with specified parameters
        
        Args:
            suite: Predefined test suite name
            categories: List of test categories to run
            pattern: File pattern to match
            parallel: Run tests in parallel
            verbose: Verbose output
            stop_on_fail: Stop on first failure
            
        Returns:
            True if all tests passed, False otherwise
        """
        try:
            print("🧪 UNIFIED TEST RUNNER - ENTERPRISE EDITION")
            print("=" * 60)
            
            # Validate environment
            if not self._validate_environment():
                return False
            
            # Build pytest command
            cmd = self._build_pytest_command(
                suite, categories, pattern, parallel, verbose, stop_on_fail
            )
            
            # Execute tests
            start_time = time.time()
            result = self._execute_tests(cmd)
            end_time = time.time()
            
            # Report results
            self._report_results(result, end_time - start_time, suite, categories)
            
            return result.returncode == 0
            
        except Exception as e:
            logger.error(f"Test execution failed: {e}")
            return False
    
    def _validate_environment(self) -> bool:
        """Validate test environment"""
        try:
            # Check if pytest is available
            result = subprocess.run(['python', '-m', 'pytest', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ pytest not available. Install with: pip install pytest")
                return False
            
            # Check if src directory exists
            if not self.src_dir.exists():
                print("❌ src directory not found")
                return False
            
            # Add src to Python path
            if str(self.src_dir) not in sys.path:
                sys.path.insert(0, str(self.src_dir))
            
            print("✅ Environment validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Environment validation failed: {e}")
            return False
    
    def _build_pytest_command(self,
                             suite: Optional[str],
                             categories: Optional[List[str]],
                             pattern: Optional[str],
                             parallel: bool,
                             verbose: bool,
                             stop_on_fail: bool) -> List[str]:
        """Build pytest command with appropriate options"""
        cmd = ['python', '-m', 'pytest']
        
        # Add verbosity
        if verbose:
            cmd.append('-v')
        else:
            cmd.append('-q')
        
        # Add stop on fail
        if stop_on_fail:
            cmd.append('-x')
        
        # Add parallel execution
        if parallel:
            try:
                import pytest_xdist
                cmd.extend(['-n', 'auto'])
            except ImportError:
                logger.warning("pytest-xdist not available, running sequentially")
        
        # Add markers based on suite or categories
        markers = self._get_test_markers(suite, categories)
        if markers:
            cmd.extend(['-m', markers])
        
        # Add file pattern
        if pattern:
            cmd.append(pattern)
        
        # Add coverage if available
        try:
            import pytest_cov
            cmd.extend(['--cov=src', '--cov-report=term-missing'])
        except ImportError:
            pass
        
        return cmd
    
    def _get_test_markers(self, 
                         suite: Optional[str], 
                         categories: Optional[List[str]]) -> Optional[str]:
        """Get pytest markers for test selection"""
        if suite and suite in self.test_suites:
            selected_categories = self.test_suites[suite]
        elif categories:
            selected_categories = categories
        else:
            return None
        
        # Build marker expression
        valid_categories = [cat for cat in selected_categories if cat in self.test_categories]
        if not valid_categories:
            return None
        
        markers = [self.test_categories[cat] for cat in valid_categories]
        return ' or '.join(markers)
    
    def _execute_tests(self, cmd: List[str]) -> subprocess.CompletedProcess:
        """Execute pytest command"""
        print(f"🚀 Executing: {' '.join(cmd)}")
        print("-" * 60)
        
        # Set environment variables
        env = os.environ.copy()
        env['PYTHONPATH'] = str(self.src_dir)
        
        # Execute command
        result = subprocess.run(cmd, env=env, cwd=self.project_root)
        
        return result
    
    def _report_results(self, 
                       result: subprocess.CompletedProcess,
                       duration: float,
                       suite: Optional[str],
                       categories: Optional[List[str]]):
        """Report test execution results"""
        print("\n" + "=" * 60)
        print("📊 TEST EXECUTION RESULTS")
        print("=" * 60)
        
        # Test configuration
        if suite:
            print(f"📋 Test Suite: {suite}")
        elif categories:
            print(f"📋 Test Categories: {', '.join(categories)}")
        else:
            print("📋 Test Categories: All")
        
        # Execution time
        print(f"⏱️ Duration: {duration:.2f} seconds")
        
        # Result
        if result.returncode == 0:
            print("✅ ALL TESTS PASSED! 🎉")
            print("🔥 Ready for production deployment!")
        else:
            print("❌ SOME TESTS FAILED")
            print("⚠️ Please review and fix failing tests before proceeding")
        
        print(f"📈 Exit Code: {result.returncode}")
    
    def list_available_options(self):
        """List available test suites and categories"""
        print("🧪 AVAILABLE TEST OPTIONS")
        print("=" * 40)
        
        print("\n📋 Test Suites:")
        for suite_name, categories in self.test_suites.items():
            print(f"  • {suite_name}: {', '.join(categories)}")
        
        print("\n🏷️ Test Categories:")
        for category, marker in self.test_categories.items():
            print(f"  • {category} (marker: {marker})")
        
        print("\n💡 Usage Examples:")
        print("  python run_unified_tests.py --suite fast")
        print("  python run_unified_tests.py --categories unit mvc")
        print("  python run_unified_tests.py --pattern test_mvc_*")
        print("  python run_unified_tests.py --suite comprehensive --parallel")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Unified Test Runner for Knowledge App",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        '--suite', '-s',
        choices=['fast', 'comprehensive', 'ui', 'ml', 'all'],
        help='Run predefined test suite'
    )
    
    parser.add_argument(
        '--categories', '-c',
        nargs='+',
        choices=list(UnifiedTestRunner().test_categories.keys()),
        help='Run specific test categories'
    )
    
    parser.add_argument(
        '--pattern', '-p',
        help='File pattern to match (e.g., test_mvc_*)'
    )
    
    parser.add_argument(
        '--parallel', '-j',
        action='store_true',
        help='Run tests in parallel (requires pytest-xdist)'
    )
    
    parser.add_argument(
        '--quiet', '-q',
        action='store_true',
        help='Quiet output'
    )
    
    parser.add_argument(
        '--stop-on-fail', '-x',
        action='store_true',
        help='Stop on first failure'
    )
    
    parser.add_argument(
        '--list', '-l',
        action='store_true',
        help='List available test options'
    )
    
    args = parser.parse_args()
    
    runner = UnifiedTestRunner()
    
    if args.list:
        runner.list_available_options()
        return 0
    
    # Default to fast suite if no options specified
    if not args.suite and not args.categories and not args.pattern:
        args.suite = 'fast'
        print("ℹ️ No test selection specified, running 'fast' suite")
    
    success = runner.run_tests(
        suite=args.suite,
        categories=args.categories,
        pattern=args.pattern,
        parallel=args.parallel,
        verbose=not args.quiet,
        stop_on_fail=args.stop_on_fail
    )
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
