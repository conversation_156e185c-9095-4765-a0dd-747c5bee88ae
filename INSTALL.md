# Knowledge App Installation Guide

## Quick Start

### 1. Check Your Hardware

First, run the hardware detection script to see what your system supports:

```bash
python check_attention.py
```

This will tell you:
- Whether you have CUDA support
- Your GPU memory
- Recommended PyTorch version

### 2. Install Based on Your Hardware

#### For NVIDIA GPUs (CUDA):
```bash
# Install with CUDA support
pip install -e .[cuda]

# Or manually install PyTorch with CUDA
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install -e .
```

#### For CPU-only systems:
```bash
# Install with CPU-only PyTorch
pip install -e .[cpu]

# Or manually install CPU PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
pip install -e .
```

#### For macOS with Apple Silicon:
```bash
# Install with Metal support
pip install -e .[metal]

# Or manually install
pip install torch torchvision torchaudio
pip install -e .
```

### 3. Run the Application

```bash
python main.py
```

## Development Installation

For development work:

```bash
# Install with development dependencies
pip install -e .[dev,test]

# Set up pre-commit hooks
pre-commit install
```

## Testing

Run tests with different configurations:

```bash
# Run all tests
pytest

# Run only fast tests (skip slow model tests)
pytest -m "not slow"

# Run only UI tests
pytest -m ui

# Run only GPU tests (if you have CUDA)
pytest -m gpu

# Run with coverage
pytest --cov=src/knowledge_app --cov-report=html
```

## Troubleshooting

### Common Issues

1. **PyQt5 Installation Issues**:
   ```bash
   # On Ubuntu/Debian
   sudo apt-get install python3-pyqt5

   # On macOS
   brew install pyqt5

   # On Windows, use pip (should work automatically)
   ```

2. **CUDA Issues**:
   - Make sure you have NVIDIA drivers installed
   - Check CUDA version with `nvidia-smi`
   - Install matching PyTorch version

3. **Memory Issues**:
   - For systems with <8GB RAM, use CPU-only mode
   - Close other applications when training models
   - Use smaller batch sizes in training config

4. **Model Loading Issues**:
   - Check internet connection for first-time model downloads
   - Ensure you have enough disk space (models can be 5-15GB)
   - Try CPU mode if GPU memory is insufficient

### Hardware Requirements

**Minimum**:
- Python 3.8+
- 4GB RAM
- 10GB free disk space

**Recommended for GPU training**:
- NVIDIA GPU with 8GB+ VRAM
- 16GB+ system RAM
- 50GB+ free disk space

**Recommended for CPU-only**:
- 8GB+ RAM
- Multi-core CPU
- 20GB+ free disk space

## Architecture Notes

The application uses a **process-based model server** for maximum stability:

- AI models run in a separate process
- UI crashes cannot affect model inference
- Better memory management
- Easier debugging

This replaces the previous threading-based approach that was prone to crashes.

## Performance Tips

1. **For faster startup**:
   - Use SSD storage
   - Pre-download models
   - Enable model caching

2. **For better training performance**:
   - Use CUDA if available
   - Increase batch size (if memory allows)
   - Use mixed precision training

3. **For stable operation**:
   - Keep the app updated
   - Monitor system resources
   - Use the process-based model server (default)

## Getting Help

1. Check the logs in `logs/` directory
2. Run diagnostic scripts in the project root
3. Check GitHub issues for known problems
4. Use the built-in error reporting system

The application includes comprehensive error handling and diagnostic tools to help identify and resolve issues quickly.