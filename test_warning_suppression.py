#!/usr/bin/env python3
"""
Test Warning Suppression

This script tests that CSS property warnings and QPainter warnings are properly suppressed.
Run this to verify that the warning suppression system is working correctly.
"""

import sys
import os
import warnings
from pathlib import Path

# Add src directory to path
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

def test_warning_suppression():
    """Test that warning suppression is working"""
    print("🧪 Testing Warning Suppression System")
    print("=" * 50)
    
    # Test 1: Import and activate warning suppression
    print("\n1. Testing warning suppression import...")
    try:
        from knowledge_app.utils.warning_suppressor import suppress_enterprise_warnings
        suppress_enterprise_warnings()
        print("✅ Enterprise warning suppression activated")
    except ImportError as e:
        print(f"❌ Failed to import warning suppressor: {e}")
        return False
    
    # Test 2: Test CSS warning suppression
    print("\n2. Testing CSS warning suppression...")
    try:
        from knowledge_app.utils.css_warning_suppressor import setup_global_css_warning_suppression
        setup_global_css_warning_suppression()
        print("✅ CSS warning suppression activated")
    except ImportError as e:
        print(f"❌ Failed to import CSS warning suppressor: {e}")
        return False

    # Test 2.5: Test Qt-level warning suppression
    print("\n2.5. Testing Qt-level warning suppression...")
    try:
        from knowledge_app.utils.qt_warning_suppressor import install_global_qt_warning_suppression
        install_global_qt_warning_suppression()
        print("✅ Qt-level warning suppression activated")
    except ImportError as e:
        print(f"❌ Failed to import Qt warning suppressor: {e}")
        return False
    
    # Test 3: Create a widget with unsupported CSS properties
    print("\n3. Testing CSS properties that should be suppressed...")
    try:
        from PyQt5.QtWidgets import QApplication, QWidget, QPushButton
        from PyQt5.QtCore import QPropertyAnimation, QEasingCurve
        from PyQt5.QtGui import QPainter
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create a widget with unsupported CSS properties
        widget = QWidget()
        button = QPushButton("Test Button", widget)
        
        # Apply CSS with unsupported properties (should not generate warnings)
        css_with_unsupported_properties = """
            QPushButton {
                background: #4f46e5;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                transform: translateY(-1px);
                transition: all 0.3s ease;
                animation: pulse 2s infinite;
                filter: blur(1px);
                backdrop-filter: blur(10px);
            }
            QPushButton:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
            }
        """
        
        button.setStyleSheet(css_with_unsupported_properties)
        print("✅ Applied CSS with unsupported properties (no warnings should appear)")
        
        # Test QPainter operations that might generate warnings
        widget.show()
        widget.repaint()
        print("✅ Performed QPainter operations (no warnings should appear)")
        
        widget.close()
        
    except Exception as e:
        print(f"❌ Error testing CSS properties: {e}")
        return False
    
    # Test 4: Test that warnings are actually being suppressed
    print("\n4. Testing warning filter effectiveness...")
    
    # Capture warnings
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")  # Temporarily enable all warnings
        
        # Generate some warnings that should be suppressed
        warnings.warn("Unknown CSS property: box-shadow", UserWarning)
        warnings.warn("CSS property not supported: text-shadow", UserWarning)
        warnings.warn("QPainter: painter not active", UserWarning)
        warnings.warn("transform property ignored", UserWarning)
        
        # Check if warnings were captured (they should be suppressed)
        if len(w) == 0:
            print("✅ All test warnings were properly suppressed")
        else:
            print(f"⚠️ {len(w)} warnings were not suppressed:")
            for warning in w:
                print(f"   - {warning.message}")
    
    print("\n5. Testing CSS cleaner utility...")
    try:
        from knowledge_app.utils.css_cleaner import quick_clean_css
        
        dirty_css = """
            .button {
                background: #4f46e5;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                transform: translateY(-1px);
                transition: all 0.3s ease;
                border-radius: 8px;
            }
        """
        
        clean_css = quick_clean_css(dirty_css)
        print("✅ CSS cleaner utility working")
        print(f"   Original CSS lines: {len(dirty_css.splitlines())}")
        print(f"   Cleaned CSS lines: {len(clean_css.splitlines())}")
        
    except ImportError as e:
        print(f"❌ Failed to import CSS cleaner: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All warning suppression tests passed!")
    print("✅ CSS property warnings are suppressed")
    print("✅ QPainter warnings are suppressed")
    print("✅ Warning suppression system is working correctly")
    
    return True

def test_specific_ui_components():
    """Test specific UI components that were causing warnings"""
    print("\n🧪 Testing Specific UI Components")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Test seductive main menu
        print("\n1. Testing SeductiveMainMenu...")
        try:
            from knowledge_app.ui.seductive_main_menu import SeductiveMainMenu
            menu = SeductiveMainMenu()
            menu.show()
            menu.close()
            print("✅ SeductiveMainMenu created without warnings")
        except Exception as e:
            print(f"❌ Error with SeductiveMainMenu: {e}")
        
        # Test seductive transitions
        print("\n2. Testing ProfessionalStackedWidget...")
        try:
            from knowledge_app.ui.seductive_transitions import ProfessionalStackedWidget
            widget = ProfessionalStackedWidget()
            widget.show()
            widget.close()
            print("✅ ProfessionalStackedWidget created without warnings")
        except Exception as e:
            print(f"❌ Error with ProfessionalStackedWidget: {e}")
        
        # Test feedback card
        print("\n3. Testing ProfessionalFeedbackCard...")
        try:
            from knowledge_app.ui.seductive_transitions import ProfessionalFeedbackCard
            card = ProfessionalFeedbackCard("Test message", True)
            card.show()
            card.close()
            print("✅ ProfessionalFeedbackCard created without warnings")
        except Exception as e:
            print(f"❌ Error with ProfessionalFeedbackCard: {e}")
        
        print("\n✅ All UI component tests completed")
        
    except Exception as e:
        print(f"❌ Error testing UI components: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Starting Warning Suppression Tests")
    
    # Test basic warning suppression
    success1 = test_warning_suppression()
    
    # Test specific UI components
    success2 = test_specific_ui_components()
    
    if success1 and success2:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Warning suppression is working correctly")
        print("✅ CSS property warnings are suppressed")
        print("✅ QPainter warnings are suppressed")
        print("✅ UI components work without warnings")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("⚠️ Warning suppression may not be working correctly")
        sys.exit(1)
