#!/usr/bin/env python3
"""
Fire Estimator Import Test

This script tests exactly what's causing the fire_estimator to import heavy libraries.
"""

import sys
import os

# Add src directory to path
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

def test_individual_imports():
    """Test individual imports from fire_estimator to find the culprit"""
    
    heavy_modules = ['torch', 'transformers', 'peft', 'datasets']
    
    print("🔍 Testing individual imports from fire_estimator...")
    
    # Test basic imports first
    test_imports = [
        "import os",
        "import time", 
        "import logging",
        "import threading",
        "import json",
        "from pathlib import Path",
        "from typing import Dict, List, Any, Optional, Callable, Tuple",
        "from collections import deque",
        "from concurrent.futures import ThreadPoolExecutor",
        "import numpy as np",
        "from .training_metrics import TrainingMetrics",
    ]
    
    for test_import in test_imports:
        print(f"\n🔄 Testing: {test_import}")
        
        # Check before
        before = [m for m in heavy_modules if m in sys.modules]
        print(f"  📦 Heavy modules before: {before}")
        
        try:
            # Try the import
            if test_import.startswith("from ."):
                # Handle relative imports
                if "training_metrics" in test_import:
                    from knowledge_app.core.training_metrics import TrainingMetrics
            else:
                exec(test_import)
            
            # Check after
            after = [m for m in heavy_modules if m in sys.modules]
            newly_imported = [m for m in after if m not in before]
            
            print(f"  📦 Heavy modules after: {after}")
            if newly_imported:
                print(f"  ⚠️ FOUND CULPRIT! {test_import} imported: {newly_imported}")
                return test_import, newly_imported
            else:
                print(f"  ✅ {test_import} is clean")
                
        except Exception as e:
            print(f"  ❌ Failed: {e}")
    
    print("\n🔍 Testing numpy import specifically...")
    
    # Check before numpy
    before = [m for m in heavy_modules if m in sys.modules]
    print(f"📦 Heavy modules before numpy: {before}")
    
    import numpy as np
    
    # Check after numpy
    after = [m for m in heavy_modules if m in sys.modules]
    newly_imported = [m for m in after if m not in before]
    
    print(f"📦 Heavy modules after numpy: {after}")
    if newly_imported:
        print(f"⚠️ NUMPY IMPORTED: {newly_imported}")
        return "numpy", newly_imported
    else:
        print("✅ numpy is clean")
    
    return None, []

def main():
    """Run the test"""
    print("🚀 Starting fire estimator import test...")
    print("=" * 60)
    
    culprit, imported = test_individual_imports()
    
    print("\n" + "=" * 60)
    print("📊 RESULTS")
    print("=" * 60)
    
    if culprit:
        print(f"❌ CULPRIT FOUND: {culprit}")
        print(f"❌ Imported heavy modules: {imported}")
    else:
        print("✅ No culprit found in basic imports")
        print("⚠️ The issue might be in a deeper dependency chain")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
