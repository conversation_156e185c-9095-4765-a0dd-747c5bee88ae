#!/usr/bin/env python3
"""
Advanced Quiz System Demo - Phase 2A Implementation

This script demonstrates the advanced quiz system with spaced repetition,
performance analytics, and adaptive learning features.

Features:
- Spaced repetition learning algorithm (SM-2)
- Performance tracking and analytics
- Weakness-focused learning
- Multiple quiz modes
- Learning insights and recommendations

Run this to experience the scientifically-backed learning system!
"""

import sys
import logging
import random
import time
from pathlib import Path
from datetime import datetime, timedelta

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QPushButton, QFrame, QGridLayout, QProgressBar,
    QTextEdit, QComboBox, QSpinBox, QGroupBox, QTabWidget
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# Import our advanced quiz components
from knowledge_app.core.spaced_repetition_engine import (
    SpacedRepetitionEngine, LearningAnalytics, DifficultyRating, LearningPhase
)
from knowledge_app.core.advanced_quiz_controller import AdvancedQuizController
from knowledge_app.ui.professional_buttons import ProfessionalButton
from knowledge_app.ui.seductive_transitions import ProfessionalToastNotification

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockQuestionGenerator:
    """Mock question generator for demo purposes"""
    
    def __init__(self):
        self.sample_questions = {
            "Python Programming": [
                {
                    "id": "py_001",
                    "question": "What is the output of: print(type([]))?",
                    "options": ["<class 'list'>", "<class 'tuple'>", "<class 'dict'>", "<class 'set'>"],
                    "correct_answer": 0,
                    "explanation": "The type() function returns the class type of an object. [] is a list."
                },
                {
                    "id": "py_002", 
                    "question": "Which method is used to add an element to a list?",
                    "options": ["add()", "append()", "insert()", "push()"],
                    "correct_answer": 1,
                    "explanation": "The append() method adds an element to the end of a list."
                }
            ],
            "Machine Learning": [
                {
                    "id": "ml_001",
                    "question": "What is overfitting in machine learning?",
                    "options": ["Model performs well on training data but poorly on test data", 
                              "Model performs poorly on both training and test data",
                              "Model performs well on both training and test data",
                              "Model cannot be trained"],
                    "correct_answer": 0,
                    "explanation": "Overfitting occurs when a model learns the training data too well, including noise."
                }
            ]
        }
    
    def generate_questions(self, topic: str, difficulty: str, count: int):
        """Generate sample questions for demo"""
        questions = self.sample_questions.get(topic, [])
        return random.sample(questions, min(count, len(questions))) if questions else []
    
    def generate_question(self, category: str, difficulty: str):
        """Generate a single question"""
        questions = self.generate_questions(category, difficulty, 1)
        return questions[0] if questions else None

class AdvancedQuizSystemDemo(QMainWindow):
    """Demo window showcasing the advanced quiz system"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Advanced Quiz System Demo - Phase 2A")
        self.setMinimumSize(1400, 900)
        
        # Initialize components
        self.quiz_controller = None
        self.toast_system = None
        self.current_session_id = None
        self.demo_timer = QTimer()
        
        self.setup_ui()
        self.setup_quiz_system()
        
    def setup_ui(self):
        """Set up the demo UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Title
        title = QLabel("🧠 Advanced Quiz System Demo - Phase 2A")
        title.setFont(QFont("Arial", 24, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #8B5CF6; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # Create tabbed interface
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.create_quiz_tab()
        self.create_analytics_tab()
        self.create_spaced_repetition_tab()
        self.create_insights_tab()
        
    def create_quiz_tab(self):
        """Create the quiz interface tab"""
        quiz_widget = QWidget()
        layout = QVBoxLayout(quiz_widget)
        
        # Quiz controls
        controls_section = self.create_demo_section("🎯 Quiz Controls")
        layout.addWidget(controls_section)
        
        controls_layout = QGridLayout()
        controls_section.layout().addLayout(controls_layout)
        
        # Topic selection
        controls_layout.addWidget(QLabel("Topic:"), 0, 0)
        self.topic_combo = QComboBox()
        self.topic_combo.addItems(["Python Programming", "Machine Learning", "Data Science"])
        controls_layout.addWidget(self.topic_combo, 0, 1)
        
        # Difficulty selection
        controls_layout.addWidget(QLabel("Difficulty:"), 0, 2)
        self.difficulty_combo = QComboBox()
        self.difficulty_combo.addItems(["easy", "medium", "hard"])
        controls_layout.addWidget(self.difficulty_combo, 0, 3)
        
        # Quiz mode selection
        controls_layout.addWidget(QLabel("Mode:"), 1, 0)
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["adaptive", "spaced_repetition", "weakness_focus", "casual"])
        controls_layout.addWidget(self.mode_combo, 1, 1)
        
        # Question count
        controls_layout.addWidget(QLabel("Questions:"), 1, 2)
        self.question_count = QSpinBox()
        self.question_count.setRange(5, 20)
        self.question_count.setValue(10)
        controls_layout.addWidget(self.question_count, 1, 3)
        
        # Start quiz button
        start_quiz_btn = ProfessionalButton("Start Quiz Session", button_type="primary")
        start_quiz_btn.clicked.connect(self.start_quiz_session)
        controls_layout.addWidget(start_quiz_btn, 2, 0, 1, 4)
        
        # Quiz display area
        self.quiz_display = QFrame()
        self.quiz_display.setFrameStyle(QFrame.Box)
        self.quiz_display.setStyleSheet("""
            QFrame {
                border: 2px solid #E5E7EB;
                border-radius: 12px;
                background: #F9FAFB;
                padding: 20px;
                min-height: 300px;
            }
        """)
        
        self.quiz_layout = QVBoxLayout(self.quiz_display)
        
        # Initial message
        initial_msg = QLabel("Select quiz parameters and click 'Start Quiz Session' to begin!")
        initial_msg.setAlignment(Qt.AlignCenter)
        initial_msg.setStyleSheet("color: #6B7280; font-size: 16px;")
        self.quiz_layout.addWidget(initial_msg)
        
        layout.addWidget(self.quiz_display)
        
        self.tab_widget.addTab(quiz_widget, "🎯 Quiz")
        
    def create_analytics_tab(self):
        """Create the analytics tab"""
        analytics_widget = QWidget()
        layout = QVBoxLayout(analytics_widget)
        
        # Learning statistics
        stats_section = self.create_demo_section("📊 Learning Statistics")
        layout.addWidget(stats_section)
        
        self.stats_display = QTextEdit()
        self.stats_display.setMaximumHeight(200)
        self.stats_display.setReadOnly(True)
        stats_section.layout().addWidget(self.stats_display)
        
        # Performance history
        history_section = self.create_demo_section("📈 Performance History")
        layout.addWidget(history_section)
        
        self.history_display = QTextEdit()
        self.history_display.setMaximumHeight(200)
        self.history_display.setReadOnly(True)
        history_section.layout().addWidget(self.history_display)
        
        # Refresh button
        refresh_btn = ProfessionalButton("Refresh Analytics", button_type="secondary")
        refresh_btn.clicked.connect(self.refresh_analytics)
        layout.addWidget(refresh_btn)
        
        self.tab_widget.addTab(analytics_widget, "📊 Analytics")
        
    def create_spaced_repetition_tab(self):
        """Create the spaced repetition tab"""
        sr_widget = QWidget()
        layout = QVBoxLayout(sr_widget)
        
        # Spaced repetition status
        sr_section = self.create_demo_section("🧠 Spaced Repetition Status")
        layout.addWidget(sr_section)
        
        self.sr_display = QTextEdit()
        self.sr_display.setMaximumHeight(250)
        self.sr_display.setReadOnly(True)
        sr_section.layout().addWidget(self.sr_display)
        
        # Demo controls
        demo_section = self.create_demo_section("🎮 Demo Controls")
        layout.addWidget(demo_section)
        
        demo_layout = QHBoxLayout()
        demo_section.layout().addLayout(demo_layout)
        
        add_cards_btn = ProfessionalButton("Add Demo Learning Cards", button_type="primary")
        add_cards_btn.clicked.connect(self.add_demo_cards)
        demo_layout.addWidget(add_cards_btn)
        
        simulate_learning_btn = ProfessionalButton("Simulate Learning Session", button_type="secondary")
        simulate_learning_btn.clicked.connect(self.simulate_learning_session)
        demo_layout.addWidget(simulate_learning_btn)
        
        self.tab_widget.addTab(sr_widget, "🧠 Spaced Repetition")
        
    def create_insights_tab(self):
        """Create the insights tab"""
        insights_widget = QWidget()
        layout = QVBoxLayout(insights_widget)
        
        # Personalized insights
        insights_section = self.create_demo_section("💡 Personalized Learning Insights")
        layout.addWidget(insights_section)
        
        self.insights_display = QTextEdit()
        self.insights_display.setMaximumHeight(200)
        self.insights_display.setReadOnly(True)
        insights_section.layout().addWidget(self.insights_display)
        
        # Recommendations
        recommendations_section = self.create_demo_section("🎯 Learning Recommendations")
        layout.addWidget(recommendations_section)
        
        self.recommendations_display = QTextEdit()
        self.recommendations_display.setMaximumHeight(200)
        self.recommendations_display.setReadOnly(True)
        recommendations_section.layout().addWidget(self.recommendations_display)
        
        self.tab_widget.addTab(insights_widget, "💡 Insights")
        
    def create_demo_section(self, title):
        """Create a demo section with title"""
        section = QFrame()
        section.setFrameStyle(QFrame.Box)
        section.setStyleSheet("""
            QFrame {
                border: 2px solid #E5E7EB;
                border-radius: 12px;
                background: #F9FAFB;
                padding: 16px;
            }
        """)
        
        layout = QVBoxLayout(section)
        
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: #374151; border: none; background: transparent;")
        layout.addWidget(title_label)
        
        return section
        
    def setup_quiz_system(self):
        """Set up the advanced quiz system"""
        # Initialize mock question generator
        question_generator = MockQuestionGenerator()
        
        # Initialize quiz controller
        self.quiz_controller = AdvancedQuizController(
            storage_manager=None,  # Using None for demo
            question_generator=question_generator
        )
        
        # Initialize toast system
        self.toast_system = ProfessionalToastNotification(self)
        
        # Add some demo learning cards
        self.add_demo_cards()
        
        # Refresh displays
        self.refresh_analytics()
        
        logger.info("🧠 Advanced quiz system ready!")
        
    def start_quiz_session(self):
        """Start a new quiz session"""
        try:
            topic = self.topic_combo.currentText()
            difficulty = self.difficulty_combo.currentText()
            mode = self.mode_combo.currentText()
            max_questions = self.question_count.value()
            
            # Start session
            self.current_session_id = self.quiz_controller.start_quiz_session(
                topic=topic,
                difficulty=difficulty,
                mode=mode,
                max_questions=max_questions
            )
            
            # Show first question
            self.show_next_question()
            
            self.toast_system.show_success(f"Started {mode} quiz session!")
            
        except Exception as e:
            logger.error(f"Error starting quiz: {e}")
            self.toast_system.show_error(f"Error starting quiz: {e}")
    
    def show_next_question(self):
        """Show the next question in the current session"""
        if not self.current_session_id:
            return
        
        try:
            question = self.quiz_controller.get_next_question(self.current_session_id)
            
            if not question:
                # Quiz completed
                self.complete_quiz_session()
                return
            
            # Clear previous content
            for i in reversed(range(self.quiz_layout.count())):
                self.quiz_layout.itemAt(i).widget().setParent(None)
            
            # Question header
            header = QLabel(f"Question {question['question_number']} of {question['total_questions']}")
            header.setFont(QFont("Arial", 14, QFont.Bold))
            header.setStyleSheet("color: #374151; margin-bottom: 10px;")
            self.quiz_layout.addWidget(header)
            
            # Question text
            question_text = QLabel(question['question'])
            question_text.setFont(QFont("Arial", 16))
            question_text.setWordWrap(True)
            question_text.setStyleSheet("color: #1F2937; margin-bottom: 15px;")
            self.quiz_layout.addWidget(question_text)
            
            # Answer options
            self.answer_buttons = []
            for i, option in enumerate(question['options']):
                btn = ProfessionalButton(f"{chr(65+i)}. {option}", button_type="secondary")
                btn.clicked.connect(lambda checked, idx=i: self.submit_answer(idx))
                self.quiz_layout.addWidget(btn)
                self.answer_buttons.append(btn)
            
            # Store current question
            self.current_question = question
            
        except Exception as e:
            logger.error(f"Error showing question: {e}")
            self.toast_system.show_error(f"Error showing question: {e}")
    
    def submit_answer(self, selected_option):
        """Submit an answer"""
        if not self.current_session_id:
            return
        
        try:
            # Simulate response time
            response_time = random.uniform(2.0, 8.0)
            
            # Submit answer
            feedback = self.quiz_controller.submit_answer(
                self.current_session_id,
                selected_option,
                response_time
            )
            
            # Show feedback
            self.show_answer_feedback(feedback)
            
            # Schedule next question
            QTimer.singleShot(3000, self.show_next_question)
            
        except Exception as e:
            logger.error(f"Error submitting answer: {e}")
            self.toast_system.show_error(f"Error submitting answer: {e}")
    
    def show_answer_feedback(self, feedback):
        """Show answer feedback"""
        # Disable answer buttons
        for btn in self.answer_buttons:
            btn.setEnabled(False)
        
        # Add feedback
        if feedback['is_correct']:
            feedback_text = "✅ Correct!"
            feedback_color = "#10B981"
        else:
            feedback_text = f"❌ Incorrect. The correct answer was {chr(65 + feedback['correct_answer'])}."
            feedback_color = "#EF4444"
        
        feedback_label = QLabel(feedback_text)
        feedback_label.setFont(QFont("Arial", 14, QFont.Bold))
        feedback_label.setStyleSheet(f"color: {feedback_color}; margin-top: 15px;")
        self.quiz_layout.addWidget(feedback_label)
        
        # Add explanation
        if feedback.get('explanation'):
            explanation = QLabel(f"💡 {feedback['explanation']}")
            explanation.setWordWrap(True)
            explanation.setStyleSheet("color: #6B7280; margin-top: 10px;")
            self.quiz_layout.addWidget(explanation)
        
        # Add spaced repetition info
        if feedback.get('spaced_repetition'):
            sr_info = feedback['spaced_repetition']
            sr_text = f"🧠 Next review: {sr_info.get('interval_change', 'N/A')}"
            sr_label = QLabel(sr_text)
            sr_label.setStyleSheet("color: #8B5CF6; margin-top: 5px;")
            self.quiz_layout.addWidget(sr_label)
    
    def complete_quiz_session(self):
        """Complete the current quiz session"""
        if not self.current_session_id:
            return
        
        try:
            results = self.quiz_controller.complete_quiz_session(self.current_session_id)
            
            # Clear quiz display
            for i in reversed(range(self.quiz_layout.count())):
                self.quiz_layout.itemAt(i).widget().setParent(None)
            
            # Show results
            stats = results['session_stats']
            accuracy = stats['accuracy']
            
            results_title = QLabel("🏁 Quiz Completed!")
            results_title.setFont(QFont("Arial", 20, QFont.Bold))
            results_title.setAlignment(Qt.AlignCenter)
            results_title.setStyleSheet("color: #8B5CF6; margin-bottom: 20px;")
            self.quiz_layout.addWidget(results_title)
            
            # Statistics
            stats_text = f"""
            📊 Session Statistics:
            • Accuracy: {accuracy:.1f}%
            • Questions: {stats['questions_answered']}
            • Correct: {stats['correct_answers']}
            • Average Response Time: {stats['average_response_time']:.1f}s
            • Session Duration: {stats['session_duration']:.1f}s
            """
            
            stats_label = QLabel(stats_text)
            stats_label.setStyleSheet("color: #374151; font-size: 14px;")
            self.quiz_layout.addWidget(stats_label)
            
            # Insights
            insights = results.get('learning_insights', [])
            if insights:
                insights_text = "💡 Learning Insights:\n" + "\n".join(f"• {insight}" for insight in insights)
                insights_label = QLabel(insights_text)
                insights_label.setWordWrap(True)
                insights_label.setStyleSheet("color: #059669; font-size: 14px; margin-top: 15px;")
                self.quiz_layout.addWidget(insights_label)
            
            # Reset session
            self.current_session_id = None
            
            # Refresh analytics
            self.refresh_analytics()
            
            self.toast_system.show_success(f"Quiz completed! {accuracy:.1f}% accuracy")
            
        except Exception as e:
            logger.error(f"Error completing quiz: {e}")
            self.toast_system.show_error(f"Error completing quiz: {e}")
    
    def add_demo_cards(self):
        """Add demo learning cards"""
        if not self.quiz_controller:
            return
        
        topics = ["Python Programming", "Machine Learning", "Data Science"]
        
        for topic in topics:
            for i in range(5):
                self.quiz_controller.sr_engine.add_learning_item(
                    topic=topic,
                    subtopic=f"Concept {i+1}",
                    question_id=f"q_{topic.lower().replace(' ', '_')}_{i+1}"
                )
        
        self.toast_system.show_success("Demo learning cards added!")
        self.refresh_analytics()
    
    def simulate_learning_session(self):
        """Simulate a learning session with random performance"""
        if not self.quiz_controller:
            return
        
        # Simulate session data
        session_data = {
            'topic': random.choice(["Python Programming", "Machine Learning"]),
            'difficulty': random.choice(["easy", "medium", "hard"]),
            'questions_answered': random.randint(5, 15),
            'correct_answers': random.randint(3, 12),
            'average_response_time': random.uniform(3.0, 8.0),
            'session_duration': random.uniform(300, 900)
        }
        
        session_data['accuracy'] = (session_data['correct_answers'] / session_data['questions_answered']) * 100
        
        # Record session
        self.quiz_controller.analytics.record_session_performance(session_data)
        
        self.toast_system.show_success("Learning session simulated!")
        self.refresh_analytics()
    
    def refresh_analytics(self):
        """Refresh all analytics displays"""
        if not self.quiz_controller:
            return
        
        # Learning statistics
        stats = self.quiz_controller.sr_engine.get_learning_statistics()
        stats_text = f"""
📊 Learning Statistics:
• Total Cards: {stats.get('total_cards', 0)}
• Due for Review: {stats.get('due_cards', 0)}
• Learning Phase: {stats.get('learning_cards', 0)}
• Mastered: {stats.get('mastered_cards', 0)}
• Average Accuracy: {stats.get('average_accuracy', 0):.1%}
• Mastery Percentage: {stats.get('mastery_percentage', 0):.1f}%
• Topics: {stats.get('topics_count', 0)}
        """
        self.stats_display.setPlainText(stats_text.strip())
        
        # Spaced repetition status
        weak_topics = stats.get('weakness_topics', [])
        sr_text = f"""
🧠 Spaced Repetition Status:
• Total Learning Cards: {stats.get('total_cards', 0)}
• Cards Due Today: {stats.get('due_cards', 0)}
• Cards in Learning Phase: {stats.get('learning_cards', 0)}
• Cards in Review Phase: {stats.get('review_cards', 0)}
• Mastered Cards: {stats.get('mastered_cards', 0)}

🎯 Weakness Areas:
        """
        
        if weak_topics:
            for topic, score in weak_topics:
                sr_text += f"• {topic}: {score:.1f} weakness score\n"
        else:
            sr_text += "• No significant weaknesses detected\n"
        
        self.sr_display.setPlainText(sr_text.strip())
        
        # Insights
        insights = self.quiz_controller.analytics.get_personalized_insights()
        insights_text = "\n".join(f"• {insight}" for insight in insights) if insights else "• Complete some quiz sessions to get personalized insights!"
        self.insights_display.setPlainText(insights_text)
        
        # Recommendations (placeholder)
        recommendations_text = """
🎯 Learning Recommendations:
• Try the 'spaced_repetition' quiz mode to review due cards
• Use 'weakness_focus' mode to improve weak areas
• Practice regularly for optimal spaced repetition effectiveness
• Aim for 70%+ accuracy to build long-term retention
        """
        self.recommendations_display.setPlainText(recommendations_text.strip())

def main():
    """Run the advanced quiz system demo"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # Set application properties
    app.setApplicationName("Advanced Quiz System Demo")
    app.setApplicationVersion("1.0.0")
    
    # Create and show demo window
    demo = AdvancedQuizSystemDemo()
    demo.show()
    
    logger.info("🚀 Advanced quiz system demo started!")
    logger.info("🧠 Experience spaced repetition and adaptive learning!")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
