#!/usr/bin/env python3
"""
Prebuilt Tokenizers Installation Script

This script helps install prebuilt tokenizers wheels instead of compiling from source,
which avoids Rust compilation errors.
"""

import os
import sys
import platform
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("tokenizers_install.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_python_version():
    """Get the Python version as a string like '3.9', '3.10', etc."""
    return f"{sys.version_info.major}.{sys.version_info.minor}"

def get_system_info():
    """Get system information for wheel selection"""
    system = platform.system().lower()
    machine = platform.machine().lower()

    if system == "windows":
        if machine in ("amd64", "x86_64"):
            return "win_amd64"
        elif machine in ("i386", "i686", "x86"):
            return "win32"
    elif system == "linux":
        if machine in ("amd64", "x86_64"):
            return "manylinux2014_x86_64"
    elif system == "darwin":  # macOS
        if machine in ("amd64", "x86_64"):
            return "macosx_10_15_x86_64"
        elif machine == "arm64":
            return "macosx_11_0_arm64"

    # Default fallback
    return f"{system.lower()}_{machine.lower()}"

def install_prebuilt_tokenizers():
    """Install prebuilt tokenizers wheel"""
    try:
        python_version = get_python_version()
        system_info = get_system_info()

        logger.info(f"Python version: {python_version}, System: {system_info}")

        # Try to install from PyPI with specific options to avoid compilation
        logger.info("Attempting to install tokenizers from PyPI with --prefer-binary option...")
        cmd = [
            sys.executable, "-m", "pip", "install",
            "--prefer-binary", "--no-build-isolation",
            "tokenizers"
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            logger.info("Successfully installed tokenizers using --prefer-binary option")
            return True
        else:
            logger.warning(f"Failed to install tokenizers with --prefer-binary: {result.stderr}")

            # Second attempt: Try using a mirror with prebuilt wheels
            logger.info("Attempting alternative installation from Hugging Face mirrors...")
            alternative_cmd = [
                sys.executable, "-m", "pip", "install",
                "--index-url", "https://download.pytorch.org/whl/cpu",
                "--extra-index-url", "https://pypi.org/simple",
                "tokenizers"
            ]

            alt_result = subprocess.run(alternative_cmd, capture_output=True, text=True)

            if alt_result.returncode == 0:
                logger.info("Successfully installed tokenizers from alternative source")
                return True
            else:
                logger.error(f"Failed alternative installation: {alt_result.stderr}")

                # Final attempt: try disabling Rust compilation
                logger.info("Trying with SKIP_TOKENIZERS_RUST_EXTENSION=1...")
                env = os.environ.copy()
                env["SKIP_TOKENIZERS_RUST_EXTENSION"] = "1"

                final_cmd = [
                    sys.executable, "-m", "pip", "install",
                    "tokenizers==0.14.1"  # Specify an older version that's more likely to have prebuilt wheels
                ]

                final_result = subprocess.run(final_cmd, env=env, capture_output=True, text=True)

                if final_result.returncode == 0:
                    logger.info("Successfully installed tokenizers by skipping Rust extension")
                    return True
                else:
                    logger.error(f"All installation attempts failed for tokenizers")
                    return False

    except Exception as e:
        logger.error(f"Error installing tokenizers: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    logger.info("Starting prebuilt tokenizers installation")
    success = install_prebuilt_tokenizers()

    if success:
        logger.info("Tokenizers installation completed successfully")
        sys.exit(0)
    else:
        logger.error("Failed to install tokenizers")
        sys.exit(1)
