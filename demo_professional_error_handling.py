#!/usr/bin/env python3
"""
Professional Error Handling Demo - Phase 1B Implementation

This script demonstrates the enhanced professional error handling system including:
- Professional error dialogs with actionable guidance
- Smart error categorization and user-friendly messages
- Integration with toast notification system
- Loading state management
- Recovery action suggestions

Run this to see the user-friendly error experience in action!
"""

import sys
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QPushButton, QFrame, QGridLayout, QTextEdit
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# Import our professional components
from knowledge_app.ui.professional_error_manager import ProfessionalErrorManager
from knowledge_app.ui.professional_error_dialogs import ErrorSeverity, ErrorCategory
from knowledge_app.ui.seductive_transitions import (
    ProfessionalToastNotification, ProfessionalLoadingOverlay
)
from knowledge_app.ui.professional_buttons import ProfessionalButton

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProfessionalErrorHandlingDemo(QMainWindow):
    """Demo window showcasing professional error handling"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Professional Error Handling Demo - Phase 1B")
        self.setMinimumSize(1200, 800)
        
        # Initialize components
        self.error_manager = None
        self.toast_system = None
        self.loading_overlay = None
        
        self.setup_ui()
        self.setup_error_system()
        
    def setup_ui(self):
        """Set up the demo UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Title
        title = QLabel("🛡️ Professional Error Handling Demo - Phase 1B")
        title.setFont(QFont("Arial", 24, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #EF4444; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # Create demo sections
        self.create_error_dialog_demo(layout)
        self.create_toast_notification_demo(layout)
        self.create_loading_state_demo(layout)
        self.create_recovery_actions_demo(layout)
        
    def create_error_dialog_demo(self, parent_layout):
        """Create error dialog demo section"""
        section = self.create_demo_section("🚨 Professional Error Dialogs")
        parent_layout.addWidget(section)
        
        content_layout = QGridLayout()
        section.layout().addLayout(content_layout)
        
        # Different error types
        network_btn = ProfessionalButton("Network Error", button_type="secondary")
        network_btn.clicked.connect(self.demo_network_error)
        content_layout.addWidget(network_btn, 0, 0)
        
        model_btn = ProfessionalButton("AI Model Error", button_type="secondary")
        model_btn.clicked.connect(self.demo_model_error)
        content_layout.addWidget(model_btn, 0, 1)
        
        file_btn = ProfessionalButton("File System Error", button_type="secondary")
        file_btn.clicked.connect(self.demo_file_error)
        content_layout.addWidget(file_btn, 0, 2)
        
        gpu_btn = ProfessionalButton("GPU Error", button_type="secondary")
        gpu_btn.clicked.connect(self.demo_gpu_error)
        content_layout.addWidget(gpu_btn, 1, 0)
        
        critical_btn = ProfessionalButton("Critical Error", button_type="primary")
        critical_btn.clicked.connect(self.demo_critical_error)
        content_layout.addWidget(critical_btn, 1, 1)
        
        memory_btn = ProfessionalButton("Memory Error", button_type="secondary")
        memory_btn.clicked.connect(self.demo_memory_error)
        content_layout.addWidget(memory_btn, 1, 2)
        
    def create_toast_notification_demo(self, parent_layout):
        """Create toast notification demo section"""
        section = self.create_demo_section("🔔 Smart Error Notifications")
        parent_layout.addWidget(section)
        
        content_layout = QHBoxLayout()
        section.layout().addLayout(content_layout)
        
        # Toast notification buttons
        warning_toast_btn = ProfessionalButton("Warning Toast", button_type="secondary")
        warning_toast_btn.clicked.connect(self.demo_warning_toast)
        content_layout.addWidget(warning_toast_btn)
        
        error_toast_btn = ProfessionalButton("Error Toast", button_type="secondary")
        error_toast_btn.clicked.connect(self.demo_error_toast)
        content_layout.addWidget(error_toast_btn)
        
        success_toast_btn = ProfessionalButton("Success Toast", button_type="primary")
        success_toast_btn.clicked.connect(self.demo_success_toast)
        content_layout.addWidget(success_toast_btn)
        
        info_toast_btn = ProfessionalButton("Info Toast", button_type="secondary")
        info_toast_btn.clicked.connect(self.demo_info_toast)
        content_layout.addWidget(info_toast_btn)
        
    def create_loading_state_demo(self, parent_layout):
        """Create loading state demo section"""
        section = self.create_demo_section("⏳ Professional Loading States")
        parent_layout.addWidget(section)
        
        content_layout = QHBoxLayout()
        section.layout().addLayout(content_layout)
        
        # Loading state buttons
        simple_loading_btn = ProfessionalButton("Simple Loading", button_type="secondary")
        simple_loading_btn.clicked.connect(self.demo_simple_loading)
        content_layout.addWidget(simple_loading_btn)
        
        cancellable_loading_btn = ProfessionalButton("Cancellable Loading", button_type="secondary")
        cancellable_loading_btn.clicked.connect(self.demo_cancellable_loading)
        content_layout.addWidget(cancellable_loading_btn)
        
        error_during_loading_btn = ProfessionalButton("Loading with Error", button_type="primary")
        error_during_loading_btn.clicked.connect(self.demo_loading_with_error)
        content_layout.addWidget(error_during_loading_btn)
        
    def create_recovery_actions_demo(self, parent_layout):
        """Create recovery actions demo section"""
        section = self.create_demo_section("🔧 Recovery Actions & Solutions")
        parent_layout.addWidget(section)
        
        content_layout = QVBoxLayout()
        section.layout().addLayout(content_layout)
        
        # Description
        desc = QLabel("Professional error dialogs provide actionable solutions and recovery options:")
        desc.setStyleSheet("color: #6B7280; font-size: 14px; margin-bottom: 10px;")
        content_layout.addWidget(desc)
        
        # Recovery demo buttons
        recovery_layout = QHBoxLayout()
        
        model_recovery_btn = ProfessionalButton("Model Error with Recovery", button_type="primary")
        model_recovery_btn.clicked.connect(self.demo_model_error_with_recovery)
        recovery_layout.addWidget(model_recovery_btn)
        
        network_recovery_btn = ProfessionalButton("Network Error with Retry", button_type="secondary")
        network_recovery_btn.clicked.connect(self.demo_network_error_with_retry)
        recovery_layout.addWidget(network_recovery_btn)
        
        content_layout.addLayout(recovery_layout)
        
    def create_demo_section(self, title):
        """Create a demo section with title"""
        section = QFrame()
        section.setFrameStyle(QFrame.Box)
        section.setStyleSheet("""
            QFrame {
                border: 2px solid #E5E7EB;
                border-radius: 12px;
                background: #F9FAFB;
                padding: 16px;
            }
        """)
        
        layout = QVBoxLayout(section)
        
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: #374151; border: none; background: transparent;")
        layout.addWidget(title_label)
        
        return section
        
    def setup_error_system(self):
        """Set up the professional error handling system"""
        # Initialize toast system
        self.toast_system = ProfessionalToastNotification(self)
        
        # Initialize error manager
        self.error_manager = ProfessionalErrorManager(self)
        self.error_manager.set_toast_system(self.toast_system)
        
        # Register recovery handlers
        self.error_manager.register_recovery_handler('restart_app', self.handle_restart_app)
        self.error_manager.register_recovery_handler('enable_offline_mode', self.handle_offline_mode)
        self.error_manager.register_recovery_handler('retry_operation', self.handle_retry_operation)
        
        # Initialize loading overlay
        self.loading_overlay = ProfessionalLoadingOverlay(self)
        self.loading_overlay.cancelled.connect(self.handle_loading_cancelled)
        
        logger.info("🛡️ Professional error handling system ready!")
        
    # Error dialog demos
    def demo_network_error(self):
        """Demo network error dialog"""
        error = ConnectionError("Unable to connect to the AI service")
        self.error_manager.handle_network_error(error, "Quiz Generation")
        
    def demo_model_error(self):
        """Demo AI model error dialog"""
        error = RuntimeError("AI model failed to load: insufficient memory")
        self.error_manager.handle_model_error(error, "Mistral-7B")
        
    def demo_file_error(self):
        """Demo file system error dialog"""
        error = FileNotFoundError("The requested document could not be found")
        self.error_manager.handle_file_error(error, "/path/to/document.pdf")
        
    def demo_gpu_error(self):
        """Demo GPU error dialog"""
        error = RuntimeError("CUDA out of memory: tried to allocate 2.00 GiB")
        self.error_manager.handle_gpu_error(error, "Model Training")
        
    def demo_critical_error(self):
        """Demo critical error dialog"""
        error = SystemError("Critical system failure detected")
        self.error_manager.handle_error(error, "System Initialization", ErrorSeverity.CRITICAL)
        
    def demo_memory_error(self):
        """Demo memory error dialog"""
        error = MemoryError("Unable to allocate memory for operation")
        self.error_manager.handle_error(error, "Large Document Processing", ErrorSeverity.ERROR)
        
    # Toast notification demos
    def demo_warning_toast(self):
        """Demo warning toast"""
        self.error_manager.show_warning_message("Connection is slow. Some features may be limited.")
        
    def demo_error_toast(self):
        """Demo error toast"""
        self.toast_system.show_error("Failed to save settings. Please try again.")
        
    def demo_success_toast(self):
        """Demo success toast"""
        self.error_manager.show_success_message("Settings saved successfully! 🎉")
        
    def demo_info_toast(self):
        """Demo info toast"""
        self.error_manager.show_info_message("Tip: You can use keyboard shortcuts for faster navigation.")
        
    # Loading state demos
    def demo_simple_loading(self):
        """Demo simple loading state"""
        self.loading_overlay.show_loading("Processing your request...")
        QTimer.singleShot(3000, self.loading_overlay.hide_loading)
        
    def demo_cancellable_loading(self):
        """Demo cancellable loading state"""
        self.loading_overlay.show_loading("Training AI model... This may take a while.", cancellable=True)
        QTimer.singleShot(5000, self.loading_overlay.hide_loading)
        
    def demo_loading_with_error(self):
        """Demo loading that ends with an error"""
        self.loading_overlay.show_loading("Connecting to AI service...")
        
        # Update message after 2 seconds
        QTimer.singleShot(2000, lambda: self.loading_overlay.update_message("Retrying connection..."))
        
        # Show error after 4 seconds
        QTimer.singleShot(4000, self.show_loading_error)
        
    def show_loading_error(self):
        """Show error after loading"""
        self.loading_overlay.hide_loading()
        error = TimeoutError("Connection timed out after 30 seconds")
        self.error_manager.handle_network_error(error, "AI Service Connection")
        
    # Recovery action demos
    def demo_model_error_with_recovery(self):
        """Demo model error with recovery actions"""
        error = RuntimeError("Model checkpoint corrupted or incompatible")
        self.error_manager.handle_model_error(error, "GPT-4")
        
    def demo_network_error_with_retry(self):
        """Demo network error with retry action"""
        error = ConnectionError("Server temporarily unavailable")
        self.error_manager.handle_network_error(error, "Document Upload")
        
    # Recovery handlers
    def handle_restart_app(self, data):
        """Handle restart application recovery"""
        self.toast_system.show_info("Application restart initiated...")
        logger.info("Restart app recovery action triggered")
        return True
        
    def handle_offline_mode(self, data):
        """Handle offline mode recovery"""
        self.toast_system.show_success("Offline mode enabled successfully!")
        logger.info("Offline mode recovery action triggered")
        return True
        
    def handle_retry_operation(self, data):
        """Handle retry operation recovery"""
        context = data.get('context', 'operation')
        self.toast_system.show_info(f"Retrying {context}...")
        logger.info(f"Retry operation recovery action triggered: {context}")
        return True
        
    def handle_loading_cancelled(self):
        """Handle loading cancellation"""
        self.toast_system.show_info("Operation cancelled by user")
        logger.info("Loading operation cancelled")
        
    def resizeEvent(self, event):
        """Handle window resize"""
        super().resizeEvent(event)
        if self.loading_overlay:
            self.loading_overlay.resize(self.size())

def main():
    """Run the professional error handling demo"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # Set application properties
    app.setApplicationName("Professional Error Handling Demo")
    app.setApplicationVersion("1.0.0")
    
    # Create and show demo window
    demo = ProfessionalErrorHandlingDemo()
    demo.show()
    
    logger.info("🚀 Professional error handling demo started!")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
