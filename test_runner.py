#!/usr/bin/env python3
"""
Unified Test Runner for Knowledge App

This script provides a modern, comprehensive testing solution that supports
different test categories and configurations, replacing the multiple
individual test runners.

Usage:
    python test_runner.py                    # Run all tests
    python test_runner.py --fast             # Skip slow tests
    python test_runner.py --ui               # Run only UI tests
    python test_runner.py --gpu              # Run only GPU tests
    python test_runner.py --integration      # Run integration tests
    python test_runner.py --coverage         # Run with coverage report
    python test_runner.py --verbose          # Verbose output
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path
from typing import List, Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def run_command(cmd: List[str], description: str) -> bool:
    """Run a command and return success status"""
    print(f"\n🔄 {description}")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Success")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed with exit code {e.returncode}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False
    except FileNotFoundError:
        print(f"❌ Command not found: {cmd[0]}")
        return False

def check_dependencies() -> bool:
    """Check if required testing dependencies are installed"""
    print("🔍 Checking test dependencies...")

    # Check packages with their actual import names
    required_imports = {
        "pytest": "pytest",
        "pytest-qt": "pytestqt",
        "pytest-cov": "pytest_cov"
    }
    missing_packages = []

    for package_name, import_name in required_imports.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install pytest pytest-qt pytest-cov")
        return False

    print("✅ All test dependencies available")
    return True

def build_pytest_command(args) -> List[str]:
    """Build the pytest command based on arguments"""
    cmd = ["python", "-m", "pytest"]
    
    # Add test markers based on arguments
    markers = []
    
    if args.fast:
        markers.append("not slow")
    
    if args.ui:
        markers.append("ui")
    
    if args.gpu:
        markers.append("gpu")
    
    if args.integration:
        markers.append("integration")
    
    if args.training:
        markers.append("training")
    
    if args.model:
        markers.append("model")
    
    if markers:
        cmd.extend(["-m", " and ".join(markers)])
    
    # Add coverage if requested
    if args.coverage:
        cmd.extend([
            "--cov=src/knowledge_app",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov",
            "--cov-fail-under=70"
        ])
    
    # Add verbosity
    if args.verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
    
    # Add other options
    cmd.extend([
        "--tb=short",
        "--strict-markers",
        "--strict-config"
    ])
    
    # Add test paths
    if args.test_path:
        cmd.append(args.test_path)
    else:
        cmd.append("tests/")
    
    return cmd

def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(
        description="Unified test runner for Knowledge App",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_runner.py                    # Run all tests
  python test_runner.py --fast             # Skip slow tests
  python test_runner.py --ui --verbose     # Run UI tests with verbose output
  python test_runner.py --coverage         # Run with coverage report
        """
    )
    
    # Test selection arguments
    parser.add_argument("--fast", action="store_true", 
                       help="Skip slow tests")
    parser.add_argument("--ui", action="store_true",
                       help="Run only UI tests")
    parser.add_argument("--gpu", action="store_true",
                       help="Run only GPU tests")
    parser.add_argument("--integration", action="store_true",
                       help="Run only integration tests")
    parser.add_argument("--training", action="store_true",
                       help="Run only training tests")
    parser.add_argument("--model", action="store_true",
                       help="Run only model tests")
    
    # Output options
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Verbose output")
    parser.add_argument("--coverage", action="store_true",
                       help="Run with coverage report")
    
    # Test path
    parser.add_argument("--test-path", type=str,
                       help="Specific test file or directory to run")
    
    # Parse arguments
    args = parser.parse_args()
    
    print("🧪 Knowledge App Unified Test Runner")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    success = True
    
    # Build and run pytest command
    pytest_cmd = build_pytest_command(args)
    
    if not run_command(pytest_cmd, "Running tests with pytest"):
        success = False
    
    # Print summary
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed!")
        if args.coverage:
            print("📊 Coverage report generated in htmlcov/")
    else:
        print("❌ Some tests failed!")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
