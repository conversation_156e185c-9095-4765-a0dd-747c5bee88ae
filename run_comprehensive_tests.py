#!/usr/bin/env python3
"""
Comprehensive Test Suite for Knowledge App
Runs all tests to catch potential issues before they occur in production
"""

import sys
import os
import logging
import traceback
import asyncio
import time
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestResult:
    def __init__(self, name: str):
        self.name = name
        self.success = False
        self.error = None
        self.duration = 0
        self.details = ""

class ComprehensiveTestSuite:
    def __init__(self):
        self.results = []
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0

    def run_test(self, test_name: str, test_func):
        """Run a single test and record results"""
        print(f"\n🧪 Running Test: {test_name}")
        print("-" * 60)
        
        result = TestResult(test_name)
        start_time = time.time()
        
        try:
            success = test_func()
            result.success = success
            result.duration = time.time() - start_time
            
            if success:
                print(f"✅ {test_name} PASSED ({result.duration:.2f}s)")
                self.passed_tests += 1
            else:
                print(f"❌ {test_name} FAILED ({result.duration:.2f}s)")
                self.failed_tests += 1
                
        except Exception as e:
            result.success = False
            result.error = str(e)
            result.duration = time.time() - start_time
            print(f"💥 {test_name} CRASHED ({result.duration:.2f}s): {e}")
            traceback.print_exc()
            self.failed_tests += 1
            
        self.results.append(result)
        self.total_tests += 1
        return result.success

    def test_instant_mcq_generation(self):
        """Test instant MCQ generation"""
        try:
            from knowledge_app.core.mcq_manager import get_mcq_manager
            from knowledge_app.core.app_config import AppConfig
            
            config = AppConfig()
            mcq_manager = get_mcq_manager(config)
            mcq_manager.set_instant_mode(True)
            
            # Test content
            test_content = "Python is a programming language. It is used for web development, data science, and automation."
            
            # Generate MCQ
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                result = loop.run_until_complete(
                    mcq_manager.generate_quiz_async(test_content, "medium")
                )
                
                # Validate result
                if not result or not result.get('question'):
                    return False
                    
                required_fields = ['question', 'options', 'correct', 'explanation']
                if not all(field in result for field in required_fields):
                    return False
                    
                print(f"Generated question: {result['question'][:50]}...")
                return True
                
            finally:
                loop.close()
                
        except Exception as e:
            print(f"Instant MCQ test failed: {e}")
            return False

    def test_mcq_manager_initialization(self):
        """Test MCQ manager initialization"""
        try:
            from knowledge_app.core.mcq_manager import get_mcq_manager
            from knowledge_app.core.app_config import AppConfig
            
            config = AppConfig()
            mcq_manager = get_mcq_manager(config)
            
            # Check instant availability
            instant_available = mcq_manager.is_instant_available()
            instant_mode = mcq_manager.is_instant_mode()
            
            print(f"Instant available: {instant_available}")
            print(f"Instant mode: {instant_mode}")
            
            return instant_available and instant_mode
            
        except Exception as e:
            print(f"MCQ manager initialization test failed: {e}")
            return False

    def test_main_window_initialization(self):
        """Test MainWindow initialization"""
        try:
            # Import Qt first
            from PyQt5.QtWidgets import QApplication
            from PyQt5.QtCore import Qt
            
            # Create QApplication if it doesn't exist
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
                app.setAttribute(Qt.AA_DisableWindowContextHelpButton)
            
            from knowledge_app.ui.main_window import MainWindow
            
            # Create main window
            window = MainWindow()
            
            # Check required attributes
            required_attrs = ['colors', 'current_font_size', 'stack', 'main_menu']
            for attr in required_attrs:
                if not hasattr(window, attr):
                    print(f"Missing attribute: {attr}")
                    return False
                    
            # Check colors attribute
            if not window.colors or not isinstance(window.colors, dict):
                print("Colors attribute is invalid")
                return False
                
            print("MainWindow initialized successfully with all required attributes")
            
            # Clean up
            window.close()
            return True
            
        except Exception as e:
            print(f"MainWindow initialization test failed: {e}")
            return False

    def test_styles_import(self):
        """Test styles import and usage"""
        try:
            from knowledge_app.ui.styles import AppStyles
            
            # Check required attributes
            required_attrs = ['COLORS', 'FONT_SIZES', 'SPACING']
            for attr in required_attrs:
                if not hasattr(AppStyles, attr):
                    print(f"Missing AppStyles attribute: {attr}")
                    return False
                    
            # Check colors
            colors = AppStyles.COLORS
            required_colors = ['PRIMARY_COLOR', 'BACKGROUND_PRIMARY', 'TEXT_PRIMARY']
            for color in required_colors:
                if color not in colors:
                    print(f"Missing color: {color}")
                    return False
                    
            print("AppStyles imported and validated successfully")
            return True
            
        except Exception as e:
            print(f"Styles import test failed: {e}")
            return False

    def test_config_system(self):
        """Test configuration system"""
        try:
            from knowledge_app.core.app_config import AppConfig
            from knowledge_app.core.config_manager import get_config
            
            # Test AppConfig
            config1 = AppConfig()
            if not hasattr(config1, 'get_value'):
                print("AppConfig missing get_value method")
                return False
                
            # Test config manager
            config2 = get_config()
            if not hasattr(config2, 'get_value'):
                print("Config manager missing get_value method")
                return False
                
            print("Configuration system working correctly")
            return True
            
        except Exception as e:
            print(f"Config system test failed: {e}")
            return False

    def test_import_dependencies(self):
        """Test all critical imports"""
        try:
            # Core imports
            from knowledge_app.core.mcq_manager import MCQManager
            from knowledge_app.core.instant_mcq_generator import InstantMCQGenerator
            from knowledge_app.ui.main_window import MainWindow
            from knowledge_app.ui.styles import AppStyles
            
            # UI imports
            from knowledge_app.ui.main_menu import MainMenu
            from knowledge_app.ui.quiz_setup_screen import QuizSetupScreen
            
            print("All critical imports successful")
            return True
            
        except Exception as e:
            print(f"Import test failed: {e}")
            return False

    def run_all_tests(self):
        """Run all tests in the suite"""
        print("🚀 Starting Comprehensive Test Suite")
        print("=" * 80)
        
        # Define tests to run
        tests = [
            ("Import Dependencies", self.test_import_dependencies),
            ("Configuration System", self.test_config_system),
            ("Styles Import", self.test_styles_import),
            ("MCQ Manager Initialization", self.test_mcq_manager_initialization),
            ("Instant MCQ Generation", self.test_instant_mcq_generation),
            ("MainWindow Initialization", self.test_main_window_initialization),
        ]
        
        # Run each test
        for test_name, test_func in tests:
            self.run_test(test_name, test_func)
            
        # Print summary
        self.print_summary()
        
        return self.failed_tests == 0

    def print_summary(self):
        """Print test results summary"""
        print("\n" + "=" * 80)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 80)
        
        print(f"Total Tests: {self.total_tests}")
        print(f"✅ Passed: {self.passed_tests}")
        print(f"❌ Failed: {self.failed_tests}")
        print(f"Success Rate: {(self.passed_tests/self.total_tests)*100:.1f}%")
        
        if self.failed_tests > 0:
            print("\n💥 FAILED TESTS:")
            for result in self.results:
                if not result.success:
                    print(f"  - {result.name}")
                    if result.error:
                        print(f"    Error: {result.error}")
        else:
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ The application should work correctly without errors.")

def main():
    """Main test runner"""
    test_suite = ComprehensiveTestSuite()
    success = test_suite.run_all_tests()
    
    if success:
        print("\n🎯 All tests passed! The application is ready to use.")
        return 0
    else:
        print("\n⚠️ Some tests failed. Please review the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
