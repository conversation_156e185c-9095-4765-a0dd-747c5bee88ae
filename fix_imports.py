"""
Fix for Knowledge App import issues in tests
This script fixes common import errors by properly setting up the Python path
"""
import os
import sys
from pathlib import Path

def fix_imports():
    """
    Fix imports for running tests by ensuring all needed paths are in sys.path
    """
    # Get the project root directory
    project_root = Path(__file__).parent.absolute()
    
    # Add project root to path if not already there
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    # Add src directory to path
    src_path = project_root / 'src'
    if src_path.exists() and str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
    
    # This is the key fix - we also need to add one level up from src
    # So that 'knowledge_app.ui' can be found
    knowledge_app_path = project_root / 'src' / 'knowledge_app'
    parent_path = knowledge_app_path.parent
    if parent_path.exists() and str(parent_path) not in sys.path:
        sys.path.insert(0, str(parent_path))

if __name__ == "__main__":
    fix_imports()
    print("Python import paths fixed!")
    print("\nPython path now includes:")
    for path in sys.path[:5]:  # Show first 5 paths
        print(f"  - {path}")
