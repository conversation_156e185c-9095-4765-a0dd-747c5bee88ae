#!/usr/bin/env python3
"""
🔥 FIRE METHOD 2: Process-Based Model Server

This is the nuclear solution. The model lives in its own process fortress,
completely isolated from the UI. No more race conditions. No more deadlocks.
The model is the SOLE OWNER of its own memory space.

Architecture:
- This process ONLY loads and runs the model
- Communication via multiprocessing queues
- UI cannot touch the model directly
- Impossible for UI threads to corrupt model memory
"""

import sys
import os
import time
import logging
import traceback
import multiprocessing as mp
from pathlib import Path
from typing import Dict, Any, Optional
import signal

# Setup logging for the server process
logging.basicConfig(
    level=logging.INFO,
    format='[MODEL-SERVER] %(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('model_server.log')
    ]
)
logger = logging.getLogger(__name__)

class ModelServer:
    """
    Isolated model server that owns the model completely.
    Runs in its own process, communicates via queues.
    """
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.model_id = None
        self.is_loaded = False
        self.shutdown_requested = False
        
        # Server stats
        self.requests_processed = 0
        self.start_time = time.time()
        
    def load_model(self, model_id: str = "mistralai/Mistral-7B-Instruct-v0.2") -> bool:
        """Load the model once and keep it loaded"""
        try:
            logger.info(f"MODEL SERVER: Loading model {model_id}")
            
            import torch
            from transformers import AutoTokenizer, AutoModelForCausalLM
            
            # Clear any existing CUDA cache
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                initial_memory = torch.cuda.memory_allocated()
                logger.info(f"Initial GPU memory: {initial_memory // 1024**2}MB")
            
            # Load tokenizer
            logger.info("Loading tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(model_id)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Load model with 4-bit quantization for stability
            logger.info("Loading model...")
            self.model = AutoModelForCausalLM.from_pretrained(
                model_id,
                torch_dtype=torch.bfloat16,
                device_map="auto",
                load_in_4bit=True,
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            
            self.model_id = model_id
            self.is_loaded = True
            
            # Log final memory usage
            if torch.cuda.is_available():
                final_memory = torch.cuda.memory_allocated()
                logger.info(f"Final GPU memory: {final_memory // 1024**2}MB")
                logger.info(f"Model memory usage: {(final_memory - initial_memory) // 1024**2}MB")
            
            logger.info(f"MODEL SERVER: Successfully loaded {model_id}")
            return True
            
        except Exception as e:
            logger.error(f"MODEL SERVER: Failed to load model: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            self.is_loaded = False
            return False
    
    def generate_text(self, prompt: str, max_tokens: int = 150) -> Dict[str, Any]:
        """Generate text using the loaded model"""
        if not self.is_loaded:
            return {
                "success": False,
                "error": "Model not loaded",
                "result": None
            }
        
        try:
            import torch
            
            logger.info(f"MODEL SERVER: Generating for prompt: {prompt[:50]}...")
            
            # Tokenize input
            inputs = self.tokenizer(prompt, return_tensors="pt", padding=True, truncation=True)
            
            # Move to model device
            device = next(self.model.parameters()).device
            inputs = {k: v.to(device) for k, v in inputs.items()}
            
            # Generate
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=max_tokens,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                    use_cache=True
                )
            
            # Decode result
            result = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            self.requests_processed += 1
            logger.info(f"MODEL SERVER: Generation successful ({self.requests_processed} total)")
            
            return {
                "success": True,
                "error": None,
                "result": result,
                "prompt": prompt,
                "length": len(result)
            }
            
        except Exception as e:
            logger.error(f"MODEL SERVER: Generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "result": None
            }
    
    def get_status(self) -> Dict[str, Any]:
        """Get server status"""
        uptime = time.time() - self.start_time
        return {
            "is_loaded": self.is_loaded,
            "model_id": self.model_id,
            "requests_processed": self.requests_processed,
            "uptime_seconds": uptime,
            "shutdown_requested": self.shutdown_requested
        }
    
    def shutdown(self):
        """Shutdown the server"""
        logger.info("MODEL SERVER: Shutdown requested")
        self.shutdown_requested = True
        
        try:
            if self.model is not None:
                del self.model
            if self.tokenizer is not None:
                del self.tokenizer
            
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            import gc
            gc.collect()
            
            logger.info("MODEL SERVER: Cleanup completed")
            
        except Exception as e:
            logger.error(f"MODEL SERVER: Error during shutdown: {e}")
    
    def run_server(self, request_queue: mp.Queue, response_queue: mp.Queue):
        """Main server loop"""
        logger.info("MODEL SERVER: Starting server loop")
        
        # Load model on startup
        if not self.load_model():
            logger.error("MODEL SERVER: Failed to load model, shutting down")
            return
        
        logger.info("MODEL SERVER: Ready to process requests")
        
        # Main processing loop
        while not self.shutdown_requested:
            try:
                # Check for requests with timeout
                try:
                    request = request_queue.get(timeout=1.0)
                except:
                    continue  # Timeout, check shutdown flag
                
                logger.info(f"MODEL SERVER: Received request: {request.get('action', 'unknown')}")
                
                # Process request based on action
                if request.get("action") == "generate":
                    prompt = request.get("prompt", "")
                    max_tokens = request.get("max_tokens", 150)
                    
                    result = self.generate_text(prompt, max_tokens)
                    result["request_id"] = request.get("request_id")
                    
                    response_queue.put(result)
                    
                elif request.get("action") == "status":
                    status = self.get_status()
                    status["request_id"] = request.get("request_id")
                    response_queue.put(status)
                    
                elif request.get("action") == "shutdown":
                    logger.info("MODEL SERVER: Shutdown command received")
                    self.shutdown_requested = True
                    response_queue.put({
                        "success": True,
                        "message": "Shutdown initiated",
                        "request_id": request.get("request_id")
                    })
                    
                else:
                    logger.warning(f"MODEL SERVER: Unknown action: {request.get('action')}")
                    response_queue.put({
                        "success": False,
                        "error": f"Unknown action: {request.get('action')}",
                        "request_id": request.get("request_id")
                    })
                
            except Exception as e:
                logger.error(f"MODEL SERVER: Error processing request: {e}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                
                # Send error response
                try:
                    response_queue.put({
                        "success": False,
                        "error": str(e),
                        "request_id": request.get("request_id", "unknown")
                    })
                except:
                    pass  # Queue might be broken
        
        # Cleanup on exit
        self.shutdown()
        logger.info("MODEL SERVER: Server loop ended")

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info(f"MODEL SERVER: Received signal {signum}, shutting down")
    sys.exit(0)

def run_model_server(request_queue: mp.Queue, response_queue: mp.Queue):
    """Entry point for the model server process"""
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create and run server
    server = ModelServer()
    server.run_server(request_queue, response_queue)

if __name__ == "__main__":
    # For testing the server directly
    logger.info("MODEL SERVER: Starting standalone test")
    
    # Create queues
    request_queue = mp.Queue()
    response_queue = mp.Queue()
    
    # Start server
    server = ModelServer()
    
    try:
        # Test the server
        if server.load_model():
            logger.info("MODEL SERVER: Standalone test - model loaded")
            
            # Test generation
            result = server.generate_text("Generate a question about physics.")
            logger.info(f"MODEL SERVER: Test result: {result['success']}")
            
            # Cleanup
            server.shutdown()
            logger.info("MODEL SERVER: Standalone test completed")
        else:
            logger.error("MODEL SERVER: Standalone test failed - could not load model")
            
    except KeyboardInterrupt:
        logger.info("MODEL SERVER: Interrupted by user")
        server.shutdown()
    except Exception as e:
        logger.error(f"MODEL SERVER: Standalone test error: {e}")
        server.shutdown()
