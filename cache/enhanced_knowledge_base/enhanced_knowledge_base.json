{"chunks": [{"text": "Python is a high-level, interpreted programming language known for its simplicity and readability. \nKey features include dynamic typing, automatic memory management, and extensive standard library.\nBasic syntax includes variables, functions, classes, and control structures like if/else, loops.\nPython supports multiple programming paradigms: procedural, object-oriented, and functional programming.", "source": "enhanced_curated_python", "topic": "python_programming", "chunk_id": "python_basics", "chunk_type": "text", "difficulty_level": "easy", "keywords": [], "concepts": ["variables", "functions", "classes", "control structures", "dynamic typing"], "quality_score": 0.9, "relevance_score": 1.0, "created_at": "2025-06-12T12:33:45.547347"}, {"text": "Python provides built-in data structures: lists (mutable sequences), tuples (immutable sequences), \ndictionaries (key-value mappings), and sets (unique elements). Lists support indexing, slicing, \nand methods like append(), remove(), and sort(). Dictionaries use hash tables for O(1) average \nlookup time. Sets support mathematical operations like union, intersection, and difference.", "source": "enhanced_curated_python", "topic": "python_programming", "chunk_id": "python_data_structures", "chunk_type": "text", "difficulty_level": "medium", "keywords": [], "concepts": ["lists", "tuples", "dictionaries", "sets", "indexing", "slicing"], "quality_score": 0.9, "relevance_score": 1.0, "created_at": "2025-06-12T12:33:45.547347"}, {"text": "Object-oriented programming in Python uses classes to define objects with attributes and methods.\nInheritance allows classes to inherit from parent classes. Polymorphism enables objects of different \ntypes to be treated uniformly. Encapsulation hides internal implementation details. Special methods \nlike __init__, __str__, and __len__ define object behavior.", "source": "enhanced_curated_python", "topic": "python_programming", "chunk_id": "python_oop", "chunk_type": "text", "difficulty_level": "hard", "keywords": [], "concepts": ["classes", "objects", "inheritance", "polymorphism", "encapsulation"], "quality_score": 0.9, "relevance_score": 1.0, "created_at": "2025-06-12T12:33:45.547347"}, {"text": "Machine learning is a subset of artificial intelligence that enables computers to learn and improve \nfrom experience without being explicitly programmed. Three main types: supervised learning (labeled data), \nunsupervised learning (unlabeled data), and reinforcement learning (reward-based). Common algorithms \ninclude linear regression, decision trees, neural networks, and support vector machines.", "source": "enhanced_curated_ml", "topic": "machine_learning", "chunk_id": "ml_fundamentals", "chunk_type": "text", "difficulty_level": "medium", "keywords": [], "concepts": ["supervised learning", "unsupervised learning", "reinforcement learning", "algorithms"], "quality_score": 0.9, "relevance_score": 1.0, "created_at": "2025-06-12T12:33:45.547347"}, {"text": "Neural networks are computing systems inspired by biological neural networks. They consist of layers \nof interconnected nodes (neurons) that process information. Deep learning uses neural networks with \nmultiple hidden layers. Backpropagation algorithm trains networks by adjusting weights based on errors. \nCommon architectures include feedforward, convolutional (CNN), and recurrent (RNN) networks.", "source": "enhanced_curated_ml", "topic": "machine_learning", "chunk_id": "neural_networks", "chunk_type": "text", "difficulty_level": "hard", "keywords": [], "concepts": ["neural networks", "deep learning", "backpropagation", "CNN", "RNN"], "quality_score": 0.9, "relevance_score": 1.0, "created_at": "2025-06-12T12:33:45.547347"}], "chunk_count": 5, "topics": ["machine_learning", "python_programming"], "created_at": "2025-06-12T12:33:45.831346"}