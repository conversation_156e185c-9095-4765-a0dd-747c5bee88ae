#!/usr/bin/env python3
"""
Detailed Import Tracking Test

This script tracks exactly which imports are causing heavy libraries to be loaded
during application startup, helping identify the specific culprits.
"""

import sys
import os
import logging
import importlib.util

# Add the src directory to path for local imports
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def track_import_chain():
    """Track the import chain that leads to heavy library imports"""
    
    heavy_modules = ['torch', 'transformers', 'peft', 'datasets']
    
    logger.info("🔍 Starting detailed import chain tracking...")
    logger.info(f"📦 Initial heavy modules in sys.modules: {[m for m in heavy_modules if m in sys.modules]}")
    
    # Test individual components step by step
    test_sequence = [
        # Core modules first
        ("numpy", "import numpy as np"),
        ("training_metrics", "from knowledge_app.core.training_metrics import TrainingMetrics"),
        
        # Check if fire_estimator itself imports torch
        ("fire_estimator_module", "import knowledge_app.core.fire_estimator"),
        
        # Check what imports fire_estimator
        ("config_manager", "from knowledge_app.core.config_manager import ConfigManager"),
        ("mcq_manager", "from knowledge_app.core.mcq_manager import MCQManager"),
        ("rag_mcq_generator", "from knowledge_app.core.rag_mcq_generator import RAGMCQGenerator"),
        ("professional_embedder", "from knowledge_app.core.professional_embedder import ProfessionalEmbedder"),
        
        # Check UI components
        ("main_window_mvc", "from knowledge_app.ui.mvc.main_window_mvc import MainWindowModel"),
        ("enterprise_main_window", "from knowledge_app.ui.enterprise_main_window import create_enterprise_main_window"),
    ]
    
    culprits = []
    
    for test_name, import_statement in test_sequence:
        logger.info(f"\n🔄 Testing: {test_name}")
        logger.info(f"   Import: {import_statement}")
        
        # Check before
        before = [m for m in heavy_modules if m in sys.modules]
        logger.info(f"   📦 Heavy modules before: {before}")
        
        try:
            # Execute the import
            exec(import_statement)
            
            # Check after
            after = [m for m in heavy_modules if m in sys.modules]
            newly_imported = [m for m in after if m not in before]
            
            logger.info(f"   📦 Heavy modules after: {after}")
            
            if newly_imported:
                logger.warning(f"   ⚠️ FOUND CULPRIT! {test_name} imported: {newly_imported}")
                culprits.append((test_name, import_statement, newly_imported))
            else:
                logger.info(f"   ✅ {test_name} is clean")
                
        except Exception as e:
            logger.error(f"   ❌ Failed to import {test_name}: {e}")
    
    return culprits

def analyze_fire_estimator_specifically():
    """Analyze fire_estimator.py specifically to find torch import"""
    logger.info("\n🔍 Analyzing fire_estimator.py specifically...")
    
    heavy_modules = ['torch', 'transformers', 'peft', 'datasets']
    
    # Check if torch is already imported
    before = [m for m in heavy_modules if m in sys.modules]
    logger.info(f"📦 Heavy modules before fire_estimator: {before}")
    
    # Import fire_estimator step by step
    try:
        # First, just import the module without executing
        spec = importlib.util.spec_from_file_location(
            "fire_estimator", 
            "src/knowledge_app/core/fire_estimator.py"
        )
        module = importlib.util.module_from_spec(spec)
        
        # Check what happens when we execute the module
        logger.info("🔄 Executing fire_estimator module...")
        spec.loader.exec_module(module)
        
        after = [m for m in heavy_modules if m in sys.modules]
        newly_imported = [m for m in after if m not in before]
        
        if newly_imported:
            logger.warning(f"⚠️ fire_estimator.py directly imported: {newly_imported}")
            return newly_imported
        else:
            logger.info("✅ fire_estimator.py itself is clean")
            return []
            
    except Exception as e:
        logger.error(f"❌ Failed to analyze fire_estimator: {e}")
        return []

def main():
    """Run detailed import tracking test"""
    logger.info("🚀 Starting detailed import tracking test...")
    logger.info("=" * 80)
    
    # First, analyze fire_estimator specifically
    fire_estimator_imports = analyze_fire_estimator_specifically()
    
    # Then track the full import chain
    culprits = track_import_chain()
    
    logger.info("\n" + "=" * 80)
    logger.info("📊 DETAILED IMPORT TRACKING RESULTS")
    logger.info("=" * 80)
    
    if fire_estimator_imports:
        logger.error(f"❌ fire_estimator.py directly imports: {fire_estimator_imports}")
    
    if culprits:
        logger.error("❌ Import chain culprits found:")
        for test_name, import_statement, imported_modules in culprits:
            logger.error(f"   • {test_name}: {imported_modules}")
            logger.error(f"     Import: {import_statement}")
        return 1
    else:
        logger.info("✅ No import chain culprits found!")
        return 0

if __name__ == "__main__":
    sys.exit(main())
