#!/usr/bin/env python3
"""
Test script to verify critical fixes for:
1. Screen stack initialization error
2. Pydantic schema error for numpy arrays
"""

import sys
import os
import logging

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_pydantic_numpy_fix():
    """Test the pydantic numpy array configuration fix"""
    print("🔧 Testing Pydantic NumPy Array Fix")
    print("=" * 50)
    
    try:
        # Test pydantic configuration
        from knowledge_app.core.pydantic_config import configure_pydantic_for_dataframes, RAGModelConfig
        
        # Configure pydantic
        result = configure_pydantic_for_dataframes()
        print(f"✅ Pydantic configuration result: {result}")
        
        # Test numpy array handling
        import numpy as np
        test_array = np.array([1, 2, 3, 4, 5])
        print(f"✅ Created test numpy array: {test_array}")
        
        # Test RAG model config
        try:
            from pydantic import BaseModel
            
            class TestRAGModel(RAGModelConfig):
                name: str
                data: np.ndarray = None
                
            # Create instance with numpy array
            model = TestRAGModel(name="test", data=test_array)
            print(f"✅ RAG model created successfully with numpy array")
            print(f"   Model data: {model.data}")
            
        except Exception as model_error:
            print(f"⚠️ RAG model test failed: {model_error}")
            
        return True
        
    except Exception as e:
        print(f"❌ Pydantic numpy fix test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rag_mcq_generator_init():
    """Test RAG MCQ generator initialization"""
    print("\n🔧 Testing RAG MCQ Generator Initialization")
    print("=" * 50)
    
    try:
        # Import and initialize RAG MCQ generator
        from knowledge_app.core.rag_mcq_generator import RAGMCQGenerator
        
        generator = RAGMCQGenerator()
        print("✅ RAG MCQ generator created")
        
        # Test initialization
        init_result = generator.initialize()
        print(f"✅ RAG MCQ generator initialization result: {init_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ RAG MCQ generator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mvc_screen_stack():
    """Test MVC screen stack initialization"""
    print("\n🔧 Testing MVC Screen Stack Initialization")
    print("=" * 50)
    
    try:
        # Test MVC components
        from knowledge_app.ui.mvc.main_window_mvc import MainWindowModel, MainWindowView, MainWindowController
        from knowledge_app.ui.mvc.base_mvc import MVCTriad
        
        # Create MVC components
        model = MainWindowModel()
        print("✅ MainWindowModel created")
        
        view = MainWindowView()
        print("✅ MainWindowView created")
        
        controller = MainWindowController()
        print("✅ MainWindowController created")
        
        # Test MVC triad creation
        triad = MVCTriad(model, view, controller)
        print("✅ MVC triad created successfully")
        
        # Test screen addition (simulated)
        view.add_screen("test_screen", view)  # Use view as dummy screen
        print("✅ Screen added to view")
        
        # Test navigation
        model.set_data('current_screen', 'test_screen')
        print("✅ Navigation test completed")
        
        return True
        
    except Exception as e:
        print(f"❌ MVC screen stack test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all critical fix tests"""
    print("🚀 Testing Critical Fixes")
    print("=" * 60)
    
    tests = [
        ("Pydantic NumPy Fix", test_pydantic_numpy_fix),
        ("RAG MCQ Generator", test_rag_mcq_generator_init),
        ("MVC Screen Stack", test_mvc_screen_stack),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 All critical fixes are working correctly!")
        return 0
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
