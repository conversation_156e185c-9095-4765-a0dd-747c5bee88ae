#!/usr/bin/env python3
"""
Test script to verify that the light theme actually has light colors.
This will print out the actual color values for both dark and light themes.
"""

import sys
import os

# Add src directory to path
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

def test_theme_colors():
    """Test and display theme colors"""
    print("🎨 Testing Light vs Dark Theme Colors")
    print("=" * 50)
    
    try:
        from knowledge_app.ui.enterprise_design_system import EnterpriseDesignSystem, Theme
        
        # Create design system
        design_system = EnterpriseDesignSystem()
        
        # Test Dark Theme Colors
        print("\n🌙 DARK THEME COLORS:")
        design_system.set_theme(Theme.DARK)
        dark_colors = {
            'bg_primary': design_system.color('bg_primary'),
            'bg_secondary': design_system.color('bg_secondary'),
            'surface': design_system.color('surface'),
            'text_primary': design_system.color('text_primary'),
            'text_secondary': design_system.color('text_secondary'),
            'primary': design_system.color('primary'),
            'border': design_system.color('border')
        }
        
        for name, color in dark_colors.items():
            print(f"  {name:15}: {color}")
        
        # Test Light Theme Colors
        print("\n☀️ LIGHT THEME COLORS:")
        design_system.set_theme(Theme.LIGHT)
        light_colors = {
            'bg_primary': design_system.color('bg_primary'),
            'bg_secondary': design_system.color('bg_secondary'),
            'surface': design_system.color('surface'),
            'text_primary': design_system.color('text_primary'),
            'text_secondary': design_system.color('text_secondary'),
            'primary': design_system.color('primary'),
            'border': design_system.color('border')
        }
        
        for name, color in light_colors.items():
            print(f"  {name:15}: {color}")
        
        # Analyze the differences
        print("\n🔍 THEME ANALYSIS:")
        print("-" * 30)
        
        # Check if light theme actually has light backgrounds
        light_bg = light_colors['bg_primary']
        dark_bg = dark_colors['bg_primary']
        
        print(f"Dark background:  {dark_bg}")
        print(f"Light background: {light_bg}")
        
        if light_bg.lower() in ['#ffffff', '#f8f9fa', '#f5f5f5', '#fafafa']:
            print("✅ Light theme has proper light background!")
        elif light_bg == dark_bg:
            print("❌ Light theme has same background as dark theme!")
        else:
            print(f"⚠️ Light theme background is: {light_bg}")
        
        # Check text colors
        light_text = light_colors['text_primary']
        dark_text = dark_colors['text_primary']
        
        print(f"Dark text:        {dark_text}")
        print(f"Light text:       {light_text}")
        
        if light_text.lower() in ['#000000', '#212529', '#333333', '#2c3e50']:
            print("✅ Light theme has proper dark text!")
        elif light_text == dark_text:
            print("❌ Light theme has same text color as dark theme!")
        else:
            print(f"⚠️ Light theme text is: {light_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing theme colors: {e}")
        return False

def test_settings_screen_styling():
    """Test that settings screen gets proper styling"""
    print("\n🎛️ Testing Settings Screen Styling")
    print("=" * 40)
    
    try:
        from knowledge_app.ui.professional_settings_screen import ProfessionalSettingsScreen
        from knowledge_app.ui.enterprise_design_system import EnterpriseDesignSystem, Theme
        
        # Create a mock settings screen
        design_system = EnterpriseDesignSystem()
        
        # Test light theme styling
        design_system.set_theme(Theme.LIGHT)
        
        # Get some key colors that should be used
        bg_color = design_system.color('bg_primary')
        surface_color = design_system.color('surface')
        text_color = design_system.color('text_primary')
        border_color = design_system.color('border')
        
        print(f"Settings screen should use:")
        print(f"  Background:     {bg_color}")
        print(f"  Surface:        {surface_color}")
        print(f"  Text:           {text_color}")
        print(f"  Borders:        {border_color}")
        
        # Check if these are actually light colors
        if bg_color.lower() in ['#ffffff', '#f8f9fa', '#f5f5f5', '#fafafa']:
            print("✅ Settings will have light background")
        else:
            print(f"⚠️ Settings background might be too dark: {bg_color}")
            
        if text_color.lower() in ['#000000', '#212529', '#333333', '#2c3e50']:
            print("✅ Settings will have dark text (good for light background)")
        else:
            print(f"⚠️ Settings text might not be dark enough: {text_color}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing settings screen styling: {e}")
        return False

def main():
    """Run all theme color tests"""
    print("🎨 Knowledge App Theme Color Verification")
    print("=" * 60)
    
    success = True
    
    # Test basic theme colors
    if not test_theme_colors():
        success = False
    
    # Test settings screen styling
    if not test_settings_screen_styling():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Theme color tests completed successfully!")
        print("💡 The light theme should now have proper light colors.")
        print("🎯 Try switching to light theme in the app settings!")
    else:
        print("❌ Some theme color tests failed.")
        print("🔧 Check the theme implementation.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
