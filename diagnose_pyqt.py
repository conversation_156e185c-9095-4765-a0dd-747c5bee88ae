#!/usr/bin/env python3
"""
PyQt5 Diagnostic Script

This script diagnoses common PyQt5 issues that could cause 0xC0000005 access violations.
"""

import sys
import os
import platform
import ctypes
import struct

def print_header(title):
    print("\n" + "=" * 50)
    print(f" {title} ".center(50, "="))
    print("=" * 50)

def get_file_architecture(filepath):
    """Determine if a DLL is 32-bit or 64-bit"""
    try:
        with open(filepath, 'rb') as f:
            # Read the DOS header
            dos_header = f.read(64)
            if dos_header[0:2] != b'MZ':
                return "Unknown (Not a valid PE file)"
            
            # Get the offset of the PE header
            pe_offset = struct.unpack('<I', dos_header[60:64])[0]
            f.seek(pe_offset)
            
            # Read the PE signature and machine type
            pe_header = f.read(6)
            if pe_header[0:4] != b'PE\0\0':
                return "Unknown (Not a valid PE file)"
            
            machine_type = struct.unpack('<H', pe_header[4:6])[0]
            
            if machine_type == 0x014c:
                return "32-bit"
            elif machine_type == 0x8664:
                return "64-bit"
            else:
                return f"Unknown (Machine type: {hex(machine_type)})"
    except Exception as e:
        return f"Error: {str(e)}"

print_header("PYTHON INFORMATION")
print(f"Python version: {platform.python_version()}")
print(f"Python architecture: {'64-bit' if sys.maxsize > 2**32 else '32-bit'}")
print(f"Python executable: {sys.executable}")
print(f"System: {platform.system()} {platform.release()} ({platform.architecture()[0]})")

print_header("PYQT5 INSTALLATION")
try:
    import PyQt5
    print(f"PyQt5 installed: Yes")
    print(f"PyQt5 path: {PyQt5.__path__}")
    
    try:
        from PyQt5.QtCore import QT_VERSION_STR, PYQT_VERSION_STR
        print(f"Qt version: {QT_VERSION_STR}")
        print(f"PyQt version: {PYQT_VERSION_STR}")
    except Exception as e:
        print(f"Could not get Qt/PyQt version: {e}")
    
except ImportError as e:
    print(f"PyQt5 installed: No ({e})")

print_header("SIP INFORMATION")
try:
    from PyQt5 import sip
    print(f"SIP imported via PyQt5: Yes")
    if hasattr(sip, '__version__'):
        print(f"SIP version: {sip.__version__}")
    else:
        print("SIP version: Not available directly")
    
    # Try to get version through another method
    try:
        print(f"SIP API version: {sip.SIP_VERSION_STR if hasattr(sip, 'SIP_VERSION_STR') else 'Not available'}")
    except:
        pass
except ImportError as e1:
    print(f"PyQt5.sip import: Failed ({e1})")
    try:
        import sip
        print(f"Standalone sip imported: Yes")
        print(f"SIP version: {sip.__version__ if hasattr(sip, '__version__') else 'Not available'}")
    except ImportError as e2:
        print(f"Standalone sip import: Failed ({e2})")

print_header("QT PLUGINS CHECK")
try:
    from PyQt5.QtCore import QLibraryInfo
    
    plugin_path = QLibraryInfo.location(QLibraryInfo.PluginsPath)
    print(f"Qt plugins path: {plugin_path}")
    
    if os.path.exists(plugin_path):
        print("Plugin directory exists")
        platform_path = os.path.join(plugin_path, "platforms")
        if os.path.exists(platform_path):
            print("Platform plugins directory exists")
            platform_plugins = [f for f in os.listdir(platform_path) if f.endswith('.dll')]
            print(f"Platform plugins: {', '.join(platform_plugins)}")
            
            # Check qwindows.dll architecture
            qwindows_path = os.path.join(platform_path, "qwindows.dll")
            if os.path.exists(qwindows_path):
                arch = get_file_architecture(qwindows_path)
                print(f"qwindows.dll architecture: {arch}")
            else:
                print("qwindows.dll not found!")
        else:
            print("Platform plugins directory does not exist!")
    else:
        print("Plugin directory does not exist!")
except Exception as e:
    print(f"Could not check Qt plugins: {e}")

print_header("DLL ARCHITECTURE CHECK")
try:
    # Check PyQt5 core DLL
    qt_core_path = os.path.join(os.path.dirname(PyQt5.__file__), "Qt5", "bin", "Qt5Core.dll")
    if os.path.exists(qt_core_path):
        arch = get_file_architecture(qt_core_path)
        print(f"Qt5Core.dll architecture: {arch}")
    else:
        alt_path = os.path.join(os.path.dirname(PyQt5.__file__), "Qt5Core.dll")
        if os.path.exists(alt_path):
            arch = get_file_architecture(alt_path)
            print(f"Qt5Core.dll (alternative location) architecture: {arch}")
        else:
            print("Qt5Core.dll not found in expected locations")
except Exception as e:
    print(f"Could not check DLL architecture: {e}")

print_header("ENVIRONMENT VARIABLES")
print(f"QT_DEBUG_PLUGINS: {os.environ.get('QT_DEBUG_PLUGINS', 'Not set')}")
print(f"QT_PLUGIN_PATH: {os.environ.get('QT_PLUGIN_PATH', 'Not set')}")
print(f"QT_QPA_PLATFORM_PLUGIN_PATH: {os.environ.get('QT_QPA_PLATFORM_PLUGIN_PATH', 'Not set')}")

if __name__ == "__main__":
    print_header("DIAGNOSTIC COMPLETE")
