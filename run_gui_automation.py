#!/usr/bin/env python3
"""
GUI Automation Runner for Knowledge App

Simple script to run the GUI automation debugger.
This will automatically:
1. Launch your app
2. Click "Train AI Model"
3. Select Mistral model
4. Monitor for errors
5. Provide debugging information

Usage:
    python run_gui_automation.py
"""

import sys
import os
from pathlib import Path

# Add tests directory to path
tests_dir = Path(__file__).parent / "tests"
sys.path.insert(0, str(tests_dir))

try:
    from gui_automation_debugger import main

    if __name__ == "__main__":
        print("🔥 Testing Training Workflow...")
        print("Press Ctrl+C to stop")
        print()

        # Run the automation
        exit_code = main()

        if exit_code == 0:
            print("🎉 Training workflow is working!")
        else:
            print("🔧 Check automation_screenshots/ for details")

        sys.exit(exit_code)
        
except ImportError as e:
    print(f"❌ Error importing automation debugger: {e}")
    print("Make sure you're running this from the project root directory.")
    sys.exit(1)
except Exception as e:
    print(f"💥 Unexpected error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
